package com.nichesolv.evahanam.service;

import com.nichesolv.evahanam.common.enums.FeatureName;
import com.nichesolv.evahanam.common.jpa.*;
import com.nichesolv.evahanam.common.repository.*;
import com.nichesolv.evahanam.telemetryData.dto.BatteryDataDto;
import com.nichesolv.evahanam.telemetryData.dto.TelemetryDataDto;
import com.nichesolv.evahanam.telemetryData.jpa.TelemetryIdx;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleBatteryData;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleTelemetryData;
import com.nichesolv.evahanam.telemetryData.repository.TelemetryBatteryRepository;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.telemetryData.service.batteryData.BatteryService;
import com.nichesolv.evahanam.telemetryData.service.telemetryData.TelemetryDataService;

import com.nichesolv.evahanam.vehicle.dto.*;
import com.nichesolv.evahanam.vehicle.enums.UpdateSource;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleLatestData;
import com.nichesolv.evahanam.vehicle.dto.AddPartRequestDto;

import com.nichesolv.evahanam.vehicle.dto.VehicleRegistrationDto;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.jpa.*;
import com.nichesolv.evahanam.vehicle.repository.*;
import com.nichesolv.evahanam.vehicle.service.VehicleService;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.jpa.ColorModel;
import com.nichesolv.evahanam.vehicleModel.jpa.PartModel;

import com.nichesolv.evahanam.vehicle.repository.PartReplacementLogRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;

import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.evahanam.vehicleModel.repository.DriveModeRepository;
import com.nichesolv.evahanam.vehicleModel.repository.PartModelRepository;
import com.nichesolv.evahanam.vehicleModel.repository.VehicleModelRepository;
import com.nichesolv.evahanam.trip.enums.EventConstants;
import com.nichesolv.evahanam.trip.jpa.EventDurationConstants;

import com.nichesolv.evahanam.trip.jpa.VehicleEventMonitor;
import com.nichesolv.evahanam.trip.repository.EventDurationConstantsRepository;
import com.nichesolv.evahanam.trip.repository.VehicleEventMonitorRepository;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.nds.repository.CustomUserRepository;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfile;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfileImpl;
import com.nichesolv.usermgmt.user.repository.organisation.OrganisationProfileRepository;
import com.nichesolv.usermgmt.user.repository.user.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Slf4j
public class VehicleServiceTest {


    @Autowired
    CustomUserRepository userRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    VehicleService vehicleService;

    @Autowired
    VehicleModelRepository vehicleModelRepository;

    @Autowired
    VehicleRepository vehicleRepository;


    @Autowired
    PartModelRepository partModelRepository;

    @Autowired
    PartRepository partRepository;

    @Autowired
    OrganisationProfileRepository organisationProfileRepository;

    @Autowired
    PartReplacementLogRepository partReplacementLogRepository;


    @Autowired
    VehicleLatestDataRepository vehicleLatestDataRepository;


    @Autowired
    EventDurationConstantsRepository tripDurationConstantsRepository;

    @Autowired
    VehicleEventMonitorRepository vehicleEventMonitorRepository;

    @Autowired
    DataFrequencyPlanDetailsRepository dataFrequencyPlanDetailsRepository;

    @Autowired
    DataFrequencyPlanRepository dataFrequencyPlanRepository;

    @Autowired
    UserRepository userTestRepository;

    @Autowired
    VehicleRunningMetricsRepository vehicleRunningMetricsRepository;

    CustomOrganisation savedOrg;
    Vehicle savedVehicle;
    @Autowired
    DriveModeRepository driveModeRepository;

    @Autowired
    ActiveVehicleSubscriptionPlanRepository activeVehicleSubscriptionPlanRepository;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;

    @Autowired
    VehicleDataRepository vehicleDataRepository;

    @Autowired
    TelemetryBatteryRepository telemetryBatteryRepository;

    @Autowired
    TelemetryDataService telemetryDataService;

    @Autowired
    BatteryService batteryService;


    @Autowired
    OrganisationSubscriptionRepository organisationSubscriptionRepository;

    @Autowired
    ComboPlanRepository comboPlanRepository;


    @BeforeEach
    public void setup() {

        CustomOrganisation organisation = new CustomOrganisation();
        organisation.setUrlSlug("manufacturerTest3");
        OrganisationProfile organisationProfile = new OrganisationProfileImpl();
        organisationProfile.setName("manufacturerTest3");
        organisationProfile.setEmail("<EMAIL>");
        organisationProfileRepository.save(organisationProfile);
        organisation.setOrganisationProfile(organisationProfile);
        savedOrg = customOrganisationRepository.save(organisation);

        Part part1 = new Part();
        Part part2 = new Part();
        Part part3 = new Part();
        part1.setPartType(PartType.BATTERY);
        part2.setPartType(PartType.BATTERY);
        part3.setPartType(PartType.BATTERY);
        PartModel partModel = new PartModel();
        partModel.setPartType(PartType.BATTERY);
        partModel.setName("testModel876");
        partModel.setManufacturer(organisation);
        partModelRepository.save(partModel);
        part1.setPartModel(partModelRepository.findByName("testModel876").get());
        part1.setManufacturer(organisation);
        part1.setSerialNumber("testBattery876");
        part1.setBatchNumber("testBattery876");
        part2.setPartModel(partModelRepository.findByName("testModel876").get());
        part2.setManufacturer(organisation);
        part2.setSerialNumber("testBattery456");
        part3.setPartModel(partModelRepository.findByName("testModel876").get());
        part3.setManufacturer(organisation);
        part3.setBatchNumber("testBattery243");
        partRepository.save(part1);
        partRepository.save(part2);
        partRepository.save(part3);

        VehicleModel vehicleModel = new VehicleModel();
        vehicleModel.setName("vehicleModel876");
        vehicleModelRepository.save(vehicleModel);
        Vehicle vehicle = new Vehicle();
        vehicle.setVehicleModel(vehicleModel);
        vehicle.setImei("test879");
        vehicle.setVehicleParts(new HashSet<>(partRepository.findBySerialNumber("testBattery876")));
        vehicle.setChassisNumber("CHASSIS9995");
        vehicle.setManufacturer(organisation);
        vehicle.setOwner(organisation);
        vehicle.setVehicleState(VehicleState.OFFLINE);
        savedVehicle = vehicleRepository.save(vehicle);
        VehicleRegistrationDto vehicleRegistrationDto = new VehicleRegistrationDto();
        vehicleRegistrationDto.setRegistrationNumber("test879_reg_no");
        vehicleRegistrationDto.setImei("test879");
        vehicleService.updateVehicleRegistrationDetails(vehicleRegistrationDto);

        //setup for updateVehicleRunningEventTest
        List<EventDurationConstants> tripDurationConstantsList = new ArrayList<>();
        tripDurationConstantsList.add(new EventDurationConstants(EventConstants.MIN_TRIP_DURATION, 20, "seconds", organisation));
        tripDurationConstantsList.add(new EventDurationConstants(EventConstants.STOP_DURATION, 40, "seconds", organisation));
        tripDurationConstantsRepository.saveAll(tripDurationConstantsList);

        List<DataFrequencyPlan> dataFrequencyPlanList = dataFrequencyPlanRepository.findAll();
        if (dataFrequencyPlanList.isEmpty()) {
            List<DataFrequencyPlan> dataFrequencyPlans = new ArrayList<>();
            dataFrequencyPlans.add(new DataFrequencyPlan("High Frequency", 1, ChronoUnit.SECONDS));
            dataFrequencyPlans.add(new DataFrequencyPlan("Medium Frequency", 5, ChronoUnit.SECONDS));
            dataFrequencyPlans.add(new DataFrequencyPlan("Low Frequency", 10, ChronoUnit.SECONDS));
            dataFrequencyPlanRepository.saveAll(dataFrequencyPlans);
        }

        List<ActiveVehicleSubscriptionPlan> activeVehicleSubscriptionPlans = new ArrayList<>();

        activeVehicleSubscriptionPlans.add(new ActiveVehicleSubscriptionPlan(savedVehicle, dataFrequencyPlanRepository.findByName("High Frequency").get()));

        activeVehicleSubscriptionPlanRepository.saveAll(activeVehicleSubscriptionPlans);
    }

    @Transactional
    @Test
    @Rollback
    public void vehicleLatestDataSchemaTest() {

        Optional<Vehicle> vehicle = vehicleRepository.findByImei("test879");
        assertTrue(vehicle.isPresent());
        VehicleLatestData vehicleLatestData = new VehicleLatestData();
        vehicleLatestData.setVehicle(vehicle.get());
        vehicleLatestData.setImei(vehicle.get().getImei());
        vehicleLatestData.setLatitude(12.0f);
        vehicleLatestData.setLongitude(77.0f);
        vehicleLatestData.setLocationUpdatedAt(Instant.now());
        vehicleLatestData.setCalOdometer(657f);
        vehicleLatestData.setCalOdometerUpdatedAt(Instant.now());
        vehicleLatestData.setSoc(77f);
        vehicleLatestData.setSocUpdatedAt(Instant.now());

        vehicleLatestDataRepository.save(vehicleLatestData);

        Optional<VehicleLatestData> dataSaved = vehicleLatestDataRepository.findByImei("test879");

        assertTrue(dataSaved.isPresent());
        assertTrue(dataSaved.get().getCalOdometer().equals(657f));
        assertTrue(dataSaved.get().getLongitude().equals(77.0f));

    }

    @Test
    @WithMockUser(username = "<EMAIL>")
    @Transactional
    @Rollback
    public void addPartToVehicleTest() {
        // Testing first time part addition
        Vehicle vehicle = vehicleRepository.findByImei("test876").get();
        System.out.println(vehicle.toString());
        // With SerialNo & BatchNo
        vehicleService.addPartToVehicle(new AddPartRequestDto("test876", "testBattery876", "testBattery876", PartType.BATTERY, "manufacturerTest1"), userTestRepository.findByEmailIgnoreCase("<EMAIL>").get());
        assertTrue(vehicle.getVehicleParts().stream().anyMatch(e -> e.getPartType().equals(PartType.BATTERY) && e.getSerialNumber().equals("testBattery876") && e.getBatchNumber().equals("testBattery876")));

        //With Only SerialNo
        vehicleService.addPartToVehicle(new AddPartRequestDto("test876", "testBattery456", "", PartType.BATTERY, "manufacturerTest1"), userTestRepository.findByEmailIgnoreCase("<EMAIL>").get());
        assertTrue(vehicle.getVehicleParts().stream().anyMatch(e -> e.getPartType().equals(PartType.BATTERY) && e.getSerialNumber().equals("testBattery456")));

        //With Only BatchNo
        vehicleService.addPartToVehicle(new AddPartRequestDto("test876", "", "testBattery243", PartType.BATTERY, "manufacturerTest1"), userTestRepository.findByEmailIgnoreCase("<EMAIL>").get());
        assertTrue(vehicle.getVehicleParts().stream().anyMatch(e -> e.getPartType().equals(PartType.BATTERY) && e.getBatchNumber().equals("testBattery243")));
        //Testing log
        Optional<PartReplacementLog> partReplacementLog = partReplacementLogRepository.findByVehicleIdAndPartTypeAndEndTimeNull(vehicle.getId(), PartType.BATTERY);
        assertTrue(partReplacementLog.isPresent());
        Long logId = partReplacementLog.get().getId();


        // Testing part replacement
        Part newPart1 = new Part();
        Part newPart2 = new Part();
        Part newPart3 = new Part();
        newPart1.setPartType(PartType.BATTERY);
        newPart1.setPartModel(partModelRepository.findByName("testModel876").get());
        newPart1.setManufacturer(customOrganisationRepository.findByUrlSlug("manufacturerTest1"));
        newPart1.setSerialNumber("testBattery877");
        newPart1.setBatchNumber("testBattery877");
        newPart2.setPartType(PartType.BATTERY);
        newPart2.setPartModel(partModelRepository.findByName("testModel876").get());
        newPart2.setManufacturer(customOrganisationRepository.findByUrlSlug("manufacturerTest1"));
        newPart2.setSerialNumber("testBattery457");
        newPart3.setPartType(PartType.BATTERY);
        newPart3.setPartModel(partModelRepository.findByName("testModel876").get());
        newPart3.setManufacturer(customOrganisationRepository.findByUrlSlug("manufacturerTest1"));
        newPart3.setBatchNumber("testBattery244");
        partRepository.save(newPart1);
        partRepository.save(newPart2);
        partRepository.save(newPart3);

        //With SerialNo & BatchNo
        vehicleService.addPartToVehicle(new AddPartRequestDto("test876", "testBattery877", "testBattery877", PartType.BATTERY, "manufacturerTest1"), userTestRepository.findByEmailIgnoreCase("<EMAIL>").get());
        assertTrue(vehicle.getVehicleParts().stream().anyMatch(e -> e.getPartType().equals(PartType.BATTERY) && e.getSerialNumber().equals("testBattery877") && e.getBatchNumber().equals("testBattery877")));
        assertFalse(vehicle.getVehicleParts().stream().anyMatch(e -> e.getPartType().equals(PartType.BATTERY) && e.getSerialNumber().equals("testBattery876") && e.getBatchNumber().equals("testBattery876")));

        //With SerialNo Only
        vehicleService.addPartToVehicle(new AddPartRequestDto("test876", "testBattery457", "", PartType.BATTERY, "manufacturerTest1"), userTestRepository.findByEmailIgnoreCase("<EMAIL>").get());
        assertTrue(vehicle.getVehicleParts().stream().anyMatch(e -> e.getPartType().equals(PartType.BATTERY) && e.getSerialNumber().equals("testBattery457")));
        assertFalse(vehicle.getVehicleParts().stream().anyMatch(e -> e.getPartType().equals(PartType.BATTERY) && e.getSerialNumber().equals("testBattery456")));

        //With BatchNo Only
        vehicleService.addPartToVehicle(new AddPartRequestDto("test876", "", "testBattery244", PartType.BATTERY, "manufacturerTest1"), userTestRepository.findByEmailIgnoreCase("<EMAIL>").get());
        assertTrue(vehicle.getVehicleParts().stream().anyMatch(e -> e.getPartType().equals(PartType.BATTERY) && e.getBatchNumber().equals("testBattery244")));
        assertFalse(vehicle.getVehicleParts().stream().anyMatch(e -> e.getPartType().equals(PartType.BATTERY) && e.getBatchNumber().equals("testBattery243")));


        //Testing log
        assertTrue(partReplacementLogRepository.findByVehicleIdAndPartTypeAndEndTimeNull(vehicle.getId(), PartType.BATTERY).isPresent());
        PartReplacementLog oldLog = partReplacementLogRepository.findById(logId).get();
        assertNotNull(oldLog.getEndTime());
    }

    @Transactional
    @WithMockUser(username = "<EMAIL>")
    @Test
    @Rollback
    public void addPartToVehicleTestGsm() {
        CustomOrganisation organisation = new CustomOrganisation();
        organisation.setUrlSlug("manufacturerTest1");
        OrganisationProfile organisationProfile = new OrganisationProfileImpl();
        organisationProfile.setName("manufacturerTest1");
        organisationProfile.setEmail("<EMAIL>");
        organisationProfileRepository.save(organisationProfile);
        organisation.setOrganisationProfile(organisationProfile);
        customOrganisationRepository.save(organisation);
        Part gsmPart = new Part();
        Part gsmPart1 = new Part();
        gsmPart1.setPartType(PartType.GSM);
        gsmPart.setPartType(PartType.GSM);
        PartModel gsmPartModel = new PartModel();
        gsmPartModel.setPartType(PartType.GSM);
        gsmPartModel.setName("testModelGsm");
        gsmPartModel.setManufacturer(organisation);
        gsmPartModel.setDescription("gsm part model");
        partModelRepository.save(gsmPartModel);
        gsmPart.setSerialNumber("2345");
        gsmPart.setManufacturer(organisation);
        gsmPart1.setSerialNumber("234");
        gsmPart1.setManufacturer(organisation);
        gsmPart1.setPartModel(gsmPartModel);
        gsmPart.setPartModel(gsmPartModel);
        partRepository.save(gsmPart1);
        partRepository.save(gsmPart);
        VehicleModel vehicleModel = new VehicleModel();
        vehicleModel.setName("vehicleModel1");
        Set<PartModel> partModel = new HashSet<>();
        partModel.add(gsmPartModel);
        vehicleModel.setPartModels(partModel);
        vehicleModelRepository.save(vehicleModel);
        Vehicle vehicle = new Vehicle();
        vehicle.setVehicleModel(vehicleModel);
        vehicle.setImei("12345678");
        Set<Part> part = new HashSet<>();
        part.add(gsmPart);
        vehicle.setVehicleParts(part);
        vehicle.setChassisNumber("CHASSIS8888");
        vehicle.setManufacturer(organisation);
        vehicle.setOwner(organisation);
        vehicleRepository.save(vehicle);
        AddPartRequestDto addPartRequestDto = new AddPartRequestDto("12345678", "234", null, PartType.GSM, "manufacturerTest1");
        //when already gsm exists for a vehicle and new gsm gets added , the imei of the vehicle is same as serial number
        vehicleService.addPartToVehicle(addPartRequestDto, userTestRepository.findByEmailIgnoreCase("<EMAIL>").get());
        assertEquals("234", vehicle.getImei());
    }

    @Test
    @Rollback
    @Transactional
    void updateVehicleRunningEventTest() {
        String imei = "test876";
        Instant now = Instant.now();
        Vehicle vehicle = vehicleRepository.findByImei(imei).get();
        VehicleStatusIdx vehicleStatusIdx = new VehicleStatusIdx(now, imei);

        VehicleStatus vehicleStatus = new VehicleStatus(vehicleStatusIdx, vehicle, VehicleState.RUNNING, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);

        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);
        Optional<VehicleEventMonitor> vehicleEventMonitor = vehicleEventMonitorRepository.findByVehicle(vehicle);
        assertTrue(vehicleEventMonitor.isPresent());
        Long runningTime = vehicleEventMonitor.get().getRunningTime();
        assertEquals(0, (long) runningTime);

        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(10), imei);
        vehicleStatus = new VehicleStatus(vehicleStatusIdx, vehicle, VehicleState.RUNNING, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);

        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);
        assertTrue(vehicleEventMonitor.get().getRunningTime() > runningTime);

        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(20), imei);
        vehicleStatus = new VehicleStatus(vehicleStatusIdx, vehicle, VehicleState.STOPPED, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);

        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);
        assertEquals(0, (long) vehicleEventMonitor.get().getRunningTime());
        Long stoppageTime = vehicleEventMonitor.get().getStoppageTime();

        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(30), imei);
        vehicleStatus = new VehicleStatus(vehicleStatusIdx, vehicle, VehicleState.CHARGING, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);
        assertTrue(vehicleEventMonitor.get().getStoppageTime() > stoppageTime);

        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(40), imei);
        vehicleStatus = new VehicleStatus(vehicleStatusIdx, vehicle, VehicleState.RUNNING, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);
        assertEquals(0, vehicleEventMonitor.get().getStoppageTime());
    }

    @Test
    @Transactional
    @Rollback
    void updateVehicleStatusForDataDelayTest() {
        Optional<DataFrequencyPlan> dataFrequencyPlan = dataFrequencyPlanRepository.findByName("High Frequency");
        Optional<DataFrequencyPlanDetails> dataFrequencyPlanDetailsForDataDelay = dataFrequencyPlanDetailsRepository.findByFeatureNameAndDataFrequencyPlan(FeatureName.VEHICLE_STATUS_UPDATION, dataFrequencyPlan.get());
        Optional<DataFrequencyPlanDetails> dataFrequencyPlanDetailsForVehicleStatus = dataFrequencyPlanDetailsRepository.findByFeatureNameAndDataFrequencyPlan(FeatureName.VEHICLE_STATUS, dataFrequencyPlan.get());
        Instant time = Instant.now().minusSeconds(600);
        VehicleStatusIdx vehicleStatusIdx = new VehicleStatusIdx(time, "test879");
        VehicleStatus vehicleStatus = new VehicleStatus(vehicleStatusIdx, VehicleState.OFFLINE, null, null, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleStatus = vehicleStatusRepository.save(vehicleStatus);
        assertTrue(vehicleStatus.getVehicleState().equals(VehicleState.OFFLINE));
        TelemetryDataDto telemetryDataDto = new TelemetryDataDto("test879", time.getEpochSecond(), "abcxyz", false, true, false, true, false, true, true, true, 1.2f, 2.3f, 1.8f, 2.3f, 3, 4);
        TelemetryIdx idx = new TelemetryIdx(time, "test879");
        telemetryDataService.addData(telemetryDataDto);
        Optional<VehicleTelemetryData> vehicleTelemetryDataOptional = vehicleDataRepository.findById(idx);
        assertTrue(vehicleTelemetryDataOptional.isPresent());

        BatteryDataDto batteryDataDto = new BatteryDataDto("test879", time.getEpochSecond(), "xyz", 5f, 6f, 4f, 4f, 3f, 7f, 15f, 16f, 1, 2, 5, 20f, 20f, 20f, 20f, 1);
        batteryService.add(batteryDataDto);
        Optional<VehicleBatteryData> vehicleBatteryData = telemetryBatteryRepository.findById(idx);
        assertTrue(vehicleBatteryData.isPresent());

        vehicleService.updateVehicleStateForDataDelay("test879", time, dataFrequencyPlanDetailsForDataDelay.get());
        Optional<VehicleStatus> updatedVehicleStatus = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestamp("test879", time);
        assertTrue(updatedVehicleStatus.get().getVehicleState().equals(VehicleState.STOPPED));
    }

    @Transactional
    @WithMockUser(username = "<EMAIL>")
    @Test
    @Rollback
    public void createVehicleTest() {
        CustomOrganisation organisation = new CustomOrganisation();
        organisation.setUrlSlug("manufacturerTest1");
        OrganisationProfile organisationProfile = new OrganisationProfileImpl();
        organisationProfile.setName("manufacturerTest1");
        organisationProfile.setEmail("<EMAIL>");
        organisationProfileRepository.save(organisationProfile);
        organisation.setOrganisationProfile(organisationProfile);
        customOrganisationRepository.save(organisation);
        OrganisationSubscription organisationSubscription = new OrganisationSubscription();
        organisationSubscription.setStartDate(Instant.now());
        organisationSubscription.setOrganisation(organisation);
        Optional<ComboPlan> comboPlan = comboPlanRepository.findByName("Gold package + High Frequency");
        organisationSubscription.setComboPlan(comboPlan.get());
        organisationSubscriptionRepository.save(organisationSubscription);
        VehicleModel vehicleModel = new VehicleModel();
        vehicleModel.setModelNo("TEST_MODEL");
        vehicleModel.setName("TEST_MODEL");
        vehicleModel.setManufacturer(organisation);
        ColorModel colorModel = new ColorModel();
        colorModel.setManufacturer(organisation);
        colorModel.setName("TEST_COLOR");
        colorModel.setPartType(PartType.COLOR);
        partModelRepository.save(colorModel);
        PartModel gsmModel = partModelRepository.findByName("ajjas-1").get();
        vehicleModel.setPartModels(Set.of(colorModel, gsmModel));
        vehicleModelRepository.save(vehicleModel);
        ErpVehicleDto newVehicleDto = new ErpVehicleDto();
        newVehicleDto.setImei("test_123456789");
        newVehicleDto.setVehicleModelName("TEST_MODEL");
        newVehicleDto.setColor("TEST_COLOR");
        newVehicleDto.setManufacturedDate(LocalDate.now());
        newVehicleDto.setManufacturerName("manufacturerTest1");
        newVehicleDto.setOwnerName("manufacturerTest1");
        newVehicleDto.setChassisNumber("chassisNumberTest");
        assertFalse(vehicleRepository.findByImei("test_123456789").isPresent());
        vehicleService.createVehicle(newVehicleDto, userTestRepository.findByEmailIgnoreCase("<EMAIL>").get());
        assertTrue(vehicleRepository.findByImei("test_123456789").isPresent());
        assertEquals(activeVehicleSubscriptionPlanRepository.findByVehicle(vehicleRepository.findByImei("test_123456789").get()).get().getComboPlan(), comboPlan.get());
    }

    @Test
    @Transactional
    @Rollback
    void getVehicleRunningStatsTest() {
        //setup for getTopAndLeast10RunningVehiclesTest
        com.nichesolv.evahanam.vehicleModel.jpa.DriveMode driveMode = new com.nichesolv.evahanam.vehicleModel.jpa.DriveMode();
        driveMode.setOrganisation(savedOrg);
        driveMode.setName("Demo Drive Mode");
        driveMode = driveModeRepository.save(driveMode);
        List<VehicleRunningMetrics> vehicleRunningMetrics = new ArrayList<>();
        Instant now = Instant.now();

        for (int i = 60; i >= 0; i = i - 10) {
            TelemetryIdx idx = new TelemetryIdx(now.minusSeconds(10), "test879");
            vehicleRunningMetrics.add(new VehicleRunningMetrics(idx, null, 0.2f, 0.0, 0.0, null, null, null, driveMode, null));
        }
        vehicleRunningMetricsRepository.saveAllAndFlush(vehicleRunningMetrics);
        Pageable pageable = PageRequest.of(0, 10);
        VehicleRunningStats vehicleRunningStats = vehicleService.getVehicleRunningStats(savedOrg, Instant.now().minusSeconds(86400).toEpochMilli(), Instant.now().toEpochMilli(), pageable);
        assertFalse(vehicleRunningStats.getLeastRunning().isEmpty());
        assertFalse(vehicleRunningStats.getTopRunning().isEmpty());
    }

    @Test
    @Transactional
    @Rollback
    void getVehiclesByVehicleStateWithOutPaginationTest() {
        VehicleStatusListAllDto vehicleLocationDtoList = vehicleService.getVehiclesByVehicleStateWithOutPagination(Optional.empty(), Optional.of(VehicleState.OFFLINE), savedOrg.getId());

    }
}