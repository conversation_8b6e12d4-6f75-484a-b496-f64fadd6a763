package com.nichesolv.evahanam.service;

import com.nichesolv.evahanam.common.exception.ImageException;
import com.nichesolv.evahanam.common.jpa.Image;
import com.nichesolv.evahanam.common.repository.ImageRepository;
import com.nichesolv.evahanam.vehicle.repository.*;
import com.nichesolv.evahanam.vehicle.service.VehicleService;
import com.nichesolv.evahanam.vehicleModel.dto.VehicleModelColorImageDto;
import com.nichesolv.evahanam.vehicleModel.dto.VehicleModelPartModelDto;
import com.nichesolv.evahanam.vehicleModel.dto.VehicleModelPartModelImageCoordinatesDto;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.jpa.ColorModel;
import com.nichesolv.evahanam.vehicleModel.jpa.PartModel;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModelPartModelImage;
import com.nichesolv.evahanam.vehicleModel.repository.*;
import com.nichesolv.evahanam.vehicleModel.service.ModelService;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfile;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfileImpl;
import com.nichesolv.usermgmt.user.repository.organisation.OrganisationProfileRepository;
import com.nichesolv.usermgmt.user.repository.user.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Slf4j
public class ModelServiceTest {
    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    VehicleService vehicleService;

    @Autowired
    VehicleModelRepository vehicleModelRepository;

    @Autowired
    VehicleRepository vehicleRepository;


    @Autowired
    PartModelRepository partModelRepository;

    @Autowired
    PartRepository partRepository;

    @Autowired
    OrganisationProfileRepository organisationProfileRepository;

    @Autowired
    PartReplacementLogRepository partReplacementLogRepository;

    @Autowired
    UserRepository userTestRepository;

    @Autowired
    ColorModelRepository colorModelRepository;

    @Autowired
    ImageRepository imageRepository;

    @Autowired
    ModelService modelService;

    @Autowired
    VehicleModelPartModelImageRepository vehicleModelPartModelImageRepository;

    CustomOrganisation savedOrg;

    PartModel partModel = new PartModel();

    VehicleModel vehicleModel = new VehicleModel();

    Image image = new Image();

    ColorModel colorModel = new ColorModel();

    @BeforeEach
    public void setup() {
        CustomOrganisation organisation = new CustomOrganisation();
        organisation.setUrlSlug("manufacturerTest3");
        OrganisationProfile organisationProfile = new OrganisationProfileImpl();
        organisationProfile.setName("manufacturerTest3");
        organisationProfile.setEmail("<EMAIL>");
        organisationProfileRepository.save(organisationProfile);
        organisation.setOrganisationProfile(organisationProfile);
        savedOrg = customOrganisationRepository.save(organisation);
        partModel.setPartType(PartType.BATTERY);
        partModel.setName("testModel876");
        partModel.setManufacturer(organisation);
        partModelRepository.save(partModel);
        colorModel.setPartType(PartType.COLOR);
        colorModel.setName("testColorModel876");
        colorModel.setManufacturer(organisation);
        colorModelRepository.save(colorModel);
        vehicleModel.setName("vehicleModel876");
        vehicleModel.setModelNo("vehicleModel876");
        vehicleModel.setManufacturer(savedOrg);
        vehicleModel.setPartModels(new HashSet<>(Arrays.asList(partModel, colorModel)));
        vehicleModelRepository.save(vehicleModel);
        image = new Image("imageUrl", LocalDateTime.now(), "imageTag");
        imageRepository.save(image);
    }

    @Transactional
    @WithMockUser(username = "<EMAIL>")
    @Test
    @Rollback
    public void addColorImagesTest() throws Throwable {
        // adding 1 image
        long imageId = imageRepository.findByUrl(image.getUrl()).get(0).getId();
        VehicleModelPartModelDto vehicleModelColorModel = new VehicleModelPartModelDto(colorModel.getName(), colorModel.getManufacturer().getOrganisationProfile().getName(), vehicleModel.getModelNo(), vehicleModel.getManufacturer().getOrganisationProfile().getName());
        VehicleModelColorImageDto vehicleModelColorImageDto = new VehicleModelColorImageDto(vehicleModelColorModel, Collections.singleton(imageId));
        modelService.addColorImages(vehicleModelColorImageDto);
        assertTrue(vehicleModel.getColorImages().get(colorModel).stream().anyMatch(e -> e == imageId));

        // adding multiple images
        Image image1 = new Image("imageUrl1", LocalDateTime.now(), "imageTag1");
        imageRepository.save(image1);
        Image image2 = new Image("imageUrl2", LocalDateTime.now(), "imageTag2");
        imageRepository.save(image2);
        VehicleModelColorImageDto vehicleModelColorImageDto1 = new VehicleModelColorImageDto(vehicleModelColorModel, new HashSet<>(Arrays.asList(image1.getId(), image2.getId())));
        modelService.addColorImages(vehicleModelColorImageDto1);
        assertTrue(vehicleModel.getColorImages().get(colorModel)
                .containsAll(Arrays.asList(image1.getId(), image2.getId())));

        // image with same tag
        Image image3 = new Image("imageUrl3", LocalDateTime.now(), "imageTag1");
        imageRepository.save(image3);
        VehicleModelColorImageDto vehicleModelColorImageDto2 = new VehicleModelColorImageDto(vehicleModelColorModel, Collections.singleton(image3.getId()));
        assertThrows(ImageException.class, () -> modelService.addColorImages(vehicleModelColorImageDto2));

        // invalid image id
        VehicleModelColorImageDto vehicleModelColorImageDto3 = new VehicleModelColorImageDto(vehicleModelColorModel, Collections.singleton(0L));
        assertThrows(ImageException.class, () -> modelService.addColorImages(vehicleModelColorImageDto3));
    }

    @Test
    @WithMockUser(username = "<EMAIL>")
    @Transactional
    @Rollback
    public void addPartModelImageCoordinatesTest() throws Throwable {
        // adding 1 image coordinate
        long imageId = imageRepository.findByUrl(image.getUrl()).get(0).getId();
        VehicleModelPartModelDto vehicleModelPartModelDto = new VehicleModelPartModelDto(partModel.getName(), partModel.getManufacturer().getOrganisationProfile().getName(), vehicleModel.getModelNo(), vehicleModel.getManufacturer().getOrganisationProfile().getName());
        VehicleModelPartModelImageCoordinatesDto vehicleModelPartModelImageCoordinatesDto = new VehicleModelPartModelImageCoordinatesDto(vehicleModelPartModelDto, Map.of(imageId, "123,456"));
        modelService.addPartModelImageCoordinates(vehicleModelPartModelImageCoordinatesDto);
        List<VehicleModelPartModelImage> vehicleModelPartModelImages = vehicleModelPartModelImageRepository.findByVehicleModelPartModelImageIdxVehicleModelAndVehicleModelPartModelImageIdxPartModelIn(vehicleModel, Collections.singletonList(partModel));
        assertTrue(vehicleModelPartModelImages.stream().anyMatch(e -> e.getCoordinates().equals("(123,456)")));

        // adding multiple image coordinate
        Image image1 = new Image("imageUrl1", LocalDateTime.now(), "imageTag1");
        imageRepository.save(image1);
        Image image2 = new Image("imageUrl2", LocalDateTime.now(), "imageTag2");
        imageRepository.save(image2);
        VehicleModelPartModelImageCoordinatesDto vehicleModelPartModelImageCoordinatesDto1 = new VehicleModelPartModelImageCoordinatesDto(vehicleModelPartModelDto, Map.of(image1.getId(), "54,32", image2.getId(), "98,76"));
        modelService.addPartModelImageCoordinates(vehicleModelPartModelImageCoordinatesDto1);
        List<VehicleModelPartModelImage> vehicleModelPartModelImages1 = vehicleModelPartModelImageRepository.findByVehicleModelPartModelImageIdxVehicleModelAndVehicleModelPartModelImageIdxPartModelIn(vehicleModel, Collections.singletonList(partModel));
        Set<String> actualCoordinates = vehicleModelPartModelImages1.stream()
                .map(VehicleModelPartModelImage::getCoordinates)
                .collect(Collectors.toSet());
        assertTrue(actualCoordinates.containsAll(Arrays.asList("(54,32)", "(98,76)", "(123,456)")));

        // image with same tag
        Image image4 = new Image("imageUrl4", LocalDateTime.now(), "imageTag1");
        imageRepository.save(image4);
        VehicleModelPartModelImageCoordinatesDto vehicleModelPartModelImageCoordinatesDto2 = new VehicleModelPartModelImageCoordinatesDto(vehicleModelPartModelDto, Map.of(image4.getId(), "54,32"));
        assertThrows(ImageException.class, () -> modelService.addPartModelImageCoordinates(vehicleModelPartModelImageCoordinatesDto2));

        // invalid image id
        VehicleModelPartModelImageCoordinatesDto vehicleModelPartModelImageCoordinatesDto3 = new VehicleModelPartModelImageCoordinatesDto(vehicleModelPartModelDto, Map.of(0L, "54,32"));
        assertThrows(ImageException.class, () -> modelService.addPartModelImageCoordinates(vehicleModelPartModelImageCoordinatesDto3));
    }
}
