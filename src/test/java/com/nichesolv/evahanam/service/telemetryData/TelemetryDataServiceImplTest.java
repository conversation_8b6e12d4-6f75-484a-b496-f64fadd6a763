package com.nichesolv.evahanam.service.telemetryData;

import com.nichesolv.evahanam.common.service.DateUtils;
import com.nichesolv.evahanam.telemetryData.dto.TelemetryDataDto;
import com.nichesolv.evahanam.telemetryData.jpa.TelemetryIdx;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleTelemetryData;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.telemetryData.service.telemetryData.TelemetryDataServiceImpl;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@SpringBootTest
@Slf4j
public class TelemetryDataServiceImplTest {

    @Autowired
    TelemetryDataServiceImpl telemetryDataService;

    @Autowired
    VehicleDataRepository vehicleDataRepository;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Test
    @Rollback
    @Transactional
    void addTest() {
        Vehicle vehicle = new Vehicle();
        CustomOrganisation organisation = new CustomOrganisation();
        CustomOrganisation organisationTest = new CustomOrganisation();
        vehicle.setImei("1234567899");
        vehicle.setChassisNumber("CHASSIS9850");
        organisation.setUrlSlug("manufacturerTest1");
        organisationTest.setUrlSlug("ownerTest1");
        customOrganisationRepository.save(organisation);
        customOrganisationRepository.save(organisationTest);
        vehicle.setManufacturer(organisation);
        vehicle.setOwner(organisationTest);
        vehicleRepository.save(vehicle);

        TelemetryIdx idx = new TelemetryIdx(Instant.ofEpochSecond(1718873585L), "1234567899");
        TelemetryDataDto telemetryDataDto = new TelemetryDataDto("1234567899", 1718873585L, "abcxyz", false, true, false, true, false, true, true, true, 1.2f, 2.3f, 1.8f, 2.3f, 3, 4);

        Optional<VehicleTelemetryData> vehicleTelemetryData = vehicleDataRepository.findById(idx);
        assertFalse(vehicleTelemetryData.isPresent());
        log.info("after the first assert");
        telemetryDataService.addData(telemetryDataDto);
        Optional<VehicleTelemetryData> vehicleTelemetryDataOptional = vehicleDataRepository.findById(idx);
        assertTrue(vehicleTelemetryDataOptional.isPresent());
    }
}
