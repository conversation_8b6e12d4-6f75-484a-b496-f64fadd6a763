package com.nichesolv.evahanam.predictions.range;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nichesolv.evahanam.vehicle.predictions.range.dto.VehicleRunningEvent;
import com.nichesolv.evahanam.vehicle.predictions.range.service.RangePredictionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nichesolv.evahanam.vehicle.predictions.dto.PredictionRequest;
import com.nichesolv.evahanam.vehicle.predictions.dto.PredictionResponse;
import com.nichesolv.evahanam.vehicle.predictions.service.PredictionService;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class RangePredictionServiceTest {
    
    @Mock
    WebClient webClient;

    @Mock
    private PredictionService predictionService;

    @Mock
    RangePredictionService rangePredictionService;

    ObjectMapper mapper = new ObjectMapper();


    PredictionResponse predictedResponse;
    PredictionRequest predictionRequest;

    private static final String host = "localhost:8080";
    private static final String hostHeader= "somelocalstuff";
    private static final String modelName = "range-prediction";

    @BeforeEach
    public void setUp() throws JsonProcessingException {
        String responseBody = "{\"model_name\":\"range-prediction\",\"model_version\":null,\"id\":\"d9a59d84-4526-49f3-8011-e3b36e16e1dc\",\"parameters\":null,\"outputs\":[{\"name\":\"output-0\",\"shape\":[3],\"datatype\":\"FP32\",\"parameters\":null,\"data\":[0.1807428002357483,0.1732882559299469,0.17470064759254456]}]}";

        String requestBody = "{\"inputs\":[{\"name\":\"input_1\",\"shape\":[3,7],\"datatype\":\"FP32\",\"data\":[[1,0,1,0,0,2.19,60],[1,0,0,1,0,2.19,60],[1,0,0,0,1,2.19,60]]}]}";


        predictionRequest = mapper.readValue(requestBody, PredictionRequest.class);

        predictedResponse = mapper.readValue(responseBody, PredictionResponse.class);


    }


    @Test
    public void testPredict() {
        // Converting requestBody to PredictRequest

        // Calling the method

        when(predictionService.predict(host,hostHeader,modelName,predictionRequest)).thenReturn(predictedResponse);
        PredictionResponse predictedResponse = predictionService.predict(host,hostHeader,modelName,predictionRequest);
        log.info(predictedResponse.toString());
//        // Verify the response
        assertEquals("range-prediction", predictedResponse.getModelName());
        assertEquals(0.1807428002357483,predictedResponse.getOutputs()[0].getData()[0]);
        assertEquals(0.1732882559299469,predictedResponse.getOutputs()[0].getData()[1]);
    }

    @Test
    public void testPredictRange(){
        VehicleRunningEvent runningEvent = new VehicleRunningEvent("29393",120);
        when(rangePredictionService.predictRange(runningEvent)).thenReturn(predictedResponse);

        PredictionResponse predictedResponse = rangePredictionService.predictRange(runningEvent);
        log.info(predictedResponse.toString());
        assertEquals("range-prediction", predictedResponse.getModelName());
        assertEquals(0.1807428002357483,predictedResponse.getOutputs()[0].getData()[0]);
        assertEquals(0.1732882559299469,predictedResponse.getOutputs()[0].getData()[1]);
    }
}
