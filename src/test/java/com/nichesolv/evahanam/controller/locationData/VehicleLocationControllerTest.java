package com.nichesolv.evahanam.controller.locationData;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nichesolv.evahanam.telemetryData.dto.VehicleLocationDataDto;
import com.nichesolv.evahanam.telemetryData.jpa.TelemetryIdx;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleLocationData;
import com.nichesolv.evahanam.telemetryData.repository.LocationDataRepository;
import com.nichesolv.evahanam.telemetryData.service.locationData.VehicleLocationDataServiceImpl;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest
@AutoConfigureMockMvc
@Slf4j
public class VehicleLocationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    LocationDataRepository locationDataRepository;

    @Autowired
    VehicleLocationDataServiceImpl vehicleLocationDataService;


    @Test
    @Transactional
    @Rollback
    public void testAddLocationData() throws Exception {
        Vehicle vehicle = new Vehicle();
        CustomOrganisation organisation = new CustomOrganisation();
        CustomOrganisation organisationTest = new CustomOrganisation();
        vehicle.setImei("1234567866");
        vehicle.setChassisNumber("CHASSIS9802");
        organisation.setUrlSlug("manufacturerTesting");
        organisationTest.setUrlSlug("ownerTesting");
        customOrganisationRepository.save(organisation);
        customOrganisationRepository.save(organisationTest);
        vehicle.setManufacturer(organisation);
        vehicle.setOwner(organisationTest);
        vehicleRepository.save(vehicle);

        VehicleLocationDataDto vehicleLocationDataDto = new VehicleLocationDataDto("1234567866", 1718950546L, "test", 18.556019f, 73.76563f, 588f, 4f, 18f, 4f, 4f, 1f, 16f, 3f);
        TelemetryIdx idx = new TelemetryIdx(Instant.ofEpochSecond(1718950546L), "1234567866");
        Optional<VehicleLocationData> vehicleLocationData = locationDataRepository.findById(idx);
        assertFalse(vehicleLocationData.isPresent());
        vehicleLocationDataService.add(vehicleLocationDataDto);
        Optional<VehicleLocationData> vehicleLocationDataOptional = locationDataRepository.findById(idx);
        assertTrue(vehicleLocationDataOptional.isPresent());
        String json = objectMapper.writeValueAsString(vehicleLocationDataDto);
        mockMvc.perform(post("/v1/vehicle-location-data")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(json))
                .andExpect(status().isOk());
    }
}
