package com.nichesolv.evahanam.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nichesolv.evahanam.common.jpa.Image;
import com.nichesolv.evahanam.common.repository.ImageRepository;
import com.nichesolv.evahanam.evApp.dto.requests.ConnectDevice;
import com.nichesolv.evahanam.evApp.dto.UserVehicleLastConnectionDetailsDto;
import com.nichesolv.evahanam.telemetryData.jpa.TelemetryIdx;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleLocationData;
import com.nichesolv.evahanam.telemetryData.repository.LocationDataRepository;
import com.nichesolv.evahanam.vehicle.dto.ErpVehicleDto;
import com.nichesolv.evahanam.vehicle.dto.VehicleRegistrationDto;
import com.nichesolv.evahanam.vehicle.enums.FleetStatus;
import com.nichesolv.evahanam.vehicle.jpa.Fleet;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.FleetRepository;
import com.nichesolv.evahanam.vehicle.repository.PartRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.evahanam.vehicleModel.repository.PartModelRepository;
import com.nichesolv.evahanam.vehicleModel.repository.VehicleModelRepository;
import com.nichesolv.nds.component.JwtTokenUtil;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfile;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfileImpl;
import com.nichesolv.usermgmt.user.repository.organisation.OrganisationProfileRepository;
import com.nichesolv.usermgmt.user.repository.user.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.web.servlet.MockMvc;

import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import com.nichesolv.evahanam.evApp.service.UserVehicleConnectionService;
import com.nichesolv.evahanam.vehicle.service.VehicleService;
import com.nichesolv.nds.repository.CustomUserRepository;
import org.springframework.web.context.WebApplicationContext;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest
@AutoConfigureMockMvc(addFilters = true)
public class UserVehicleControllerTest {
    @Autowired
    WebApplicationContext context;
    @Autowired
    MockMvc mockMvc;
    @Autowired
    CustomUserRepository userRepository;


    @Autowired
    UserVehicleConnectionService vehicleConnectService;

    @Autowired
    VehicleService vehicleService;

    @Autowired
    ImageRepository imageRepository;

    @Autowired
    VehicleModelRepository vehicleModelRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    FleetRepository fleetRepository;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    LocationDataRepository locationDataRepository;

    @Autowired
    OrganisationProfileRepository organisationProfileRepository;

    @Autowired
    PartModelRepository partModelRepository;

    @Autowired
    PartRepository partRepository;

    @Autowired
    JwtTokenUtil jwtTokenUtil;

    @Autowired
    UserRepository userTestRepository;

    private static final long DURATION = 24 * 60 * 60 * 1000L;
    private static final String email = "<EMAIL>";
    private static final String[] dataTypes = {"distanceTravelled", "rideDuration", "avgSpeed", "maxSpeed", "chargingTime", "modeRange"};


    @BeforeEach
    public void setup() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(context).build();

        CustomOrganisation organisation = new CustomOrganisation();
        organisation.setUrlSlug("manufacturerTest1");
        OrganisationProfile organisationProfile = new OrganisationProfileImpl();
        organisationProfile.setName("manufacturerTest1");
        organisationProfile.setEmail("<EMAIL>");
        organisationProfileRepository.save(organisationProfile);
        organisation.setOrganisationProfile(organisationProfile);
        customOrganisationRepository.save(organisation);

        VehicleModel vehicleModel = new VehicleModel();
        vehicleModel.setName("vehicleModel876");
        vehicleModelRepository.save(vehicleModel);
        Vehicle vehicle = new Vehicle();
        vehicle.setVehicleModel(vehicleModel);
        vehicle.setImei("test876");
        vehicle.setChassisNumber("CHASSIS9995");
        vehicle.setManufacturer(organisation);
        vehicle.setOwner(organisation);
        vehicleRepository.save(vehicle);

        VehicleRegistrationDto vehicleRegistrationDto = new VehicleRegistrationDto();
        vehicleRegistrationDto.setRegistrationNumber("test876_reg_no");
        vehicleRegistrationDto.setImei("test876");
        vehicleService.updateVehicleRegistrationDetails(vehicleRegistrationDto);


    }

    @Test
    @Transactional
    @Rollback
    @WithMockUser(username="<EMAIL>", password = "dummyuser12345678910")
    public void userVehiclesLastConnectionDetailsTest() throws Exception {

        String url = "/user-vehicles/last-connection/details";

        Optional<CustomUser> user = userRepository.findByEmailIgnoreCase("<EMAIL>");

        imageRepository.save(new Image("test_url_12345", LocalDateTime.now(), null));

        List<Image> image =  imageRepository.findByUrl("test_url_12345");
        Set<Image> images = new HashSet<>();
        images.add(image.get(0));
        Optional<CustomOrganisation> customOrganisation =   customOrganisationRepository.findByUser(user.get()).stream().findFirst();
        VehicleModel vehicleModel = new VehicleModel();
        vehicleModel.setModelNo("TEST_MODEL");
        vehicleModel.setName("TEST_MODEL");
        vehicleModel.setManufacturer(customOrganisation.get());
        vehicleModelRepository.save(vehicleModel);


        ErpVehicleDto newVehicleDto = new ErpVehicleDto();
        newVehicleDto.setImei("test_123456789");
        newVehicleDto.setVehicleModelName("TEST_MODEL");
        newVehicleDto.setColor("TEST_COLOR");
        newVehicleDto.setManufacturedDate(LocalDate.now());
        newVehicleDto.setManufacturerName("NicheSolv");
        newVehicleDto.setOwnerName("NicheSolv");
        vehicleService.createVehicle(newVehicleDto, userTestRepository.findByEmailIgnoreCase("<EMAIL>").get());
        Vehicle vehicle = vehicleRepository.findByImei("test_123456789").get();

        VehicleRegistrationDto vehicleRegistrationDto = new VehicleRegistrationDto();
        vehicleRegistrationDto.setRegistrationNumber("TEST_REG_NO_123456789");
        vehicleRegistrationDto.setImei("test_123456789");
        vehicleService.updateVehicleRegistrationDetails(vehicleRegistrationDto);
        Optional<Fleet> fleet = fleetRepository.findByOrganisationAndStatus(customOrganisation.get().getId(),FleetStatus.ACTIVE.name()).stream().findFirst();
        Instant instant = Instant.now();
        TelemetryIdx telemetryIdx = new TelemetryIdx();
        telemetryIdx.setImei("test_123456789");
        telemetryIdx.setTimestamp(instant);

        VehicleLocationData vehicleLocationData = new VehicleLocationData();

        vehicleLocationData.setTelemetryIdx(telemetryIdx);
        vehicleLocationData.setVehicle(vehicle);
        vehicleLocationData.setOwnerOrg(customOrganisation.get());
        vehicleLocationData.setMfrOrg(customOrganisation.get());
        vehicleLocationData.setLatitude(12.913783732242f);
        vehicleLocationData.setLongitude(77.59459236114665f);

        locationDataRepository.save(vehicleLocationData);

        if (fleet.isEmpty()) {
            fleetRepository.save(new Fleet(99999l, "Test Fleet Nichesolv", "test fleet", customOrganisation.get(), Set.of(user.get()), Set.of(vehicle), FleetStatus.ACTIVE));
        }

        vehicleConnectService.connectVehicle(new ConnectDevice(OrganisationType.B2BCUSTOMER, customOrganisation.get().getId(),"test_123456789","test_123456789",null,12.913783732242f, 77.59459236114665f)
                , user.get());

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get(url))
                .andExpect(status().isOk()).andReturn();
        List<UserVehicleLastConnectionDetailsDto> userVehicleLastConnectionDetailsDtoList = new ObjectMapper().readValue(result.getResponse().getContentAsString(), new TypeReference<List<UserVehicleLastConnectionDetailsDto>>() {
        });
        assertTrue(userVehicleLastConnectionDetailsDtoList.size() > 0);

        for(UserVehicleLastConnectionDetailsDto e :userVehicleLastConnectionDetailsDtoList ){
            if(e.getRegNo().equals("TEST_REG_NO_123456789")){
                assertTrue(Optional.ofNullable(e.getEndDate()).isEmpty());
            }
        }

    }



    @Test
    @Transactional
    @Rollback
    public void staticsAPI() throws Exception {
        Optional<CustomUser> user = userRepository.findByEmailIgnoreCase(email);
        assertTrue(user.isPresent());
        String token = jwtTokenUtil.generateAccessToken(user.get(), DURATION);
        mockMvc.perform(MockMvcRequestBuilders.get("/user-vehicles/insights/statistics")
                        .header("authorization", "Bearer " + token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .param("startTime", "1721024863000")
                        .param("endTime", "1721024963000")
                        .param("period", "day")
                )
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    @Transactional
    @Rollback
    public void staticsDetailsAPI() throws Exception {
        Optional<CustomUser> user = userRepository.findByEmailIgnoreCase(email);
        assertTrue(user.isPresent());
        String token = jwtTokenUtil.generateAccessToken(user.get(), DURATION);

        Arrays.stream(dataTypes).forEach(
                e ->
                {
                    try {
                        mockMvc.perform(MockMvcRequestBuilders.get("/user-vehicles/insights/statistics-details")
                                        .header("authorization", "Bearer " + token)
                                        .contentType(MediaType.APPLICATION_JSON)
                                        .accept(MediaType.APPLICATION_JSON)
                                        .param("startTime", "1721024863000")
                                        .param("endTime", "1721024963000")
                                        .param("dataType", e)
                                        .param("period", "day")
                                )
                                .andExpect(MockMvcResultMatchers.status().isOk());
                    } catch (Exception ex) {
                        throw new RuntimeException(ex);
                    }
                }
        );

    }
}