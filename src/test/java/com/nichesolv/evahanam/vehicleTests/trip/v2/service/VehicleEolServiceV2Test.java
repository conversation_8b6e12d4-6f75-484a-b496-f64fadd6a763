package com.nichesolv.evahanam.vehicleTests.trip.v2.service;

import com.nichesolv.evahanam.common.jpa.ActiveVehicleSubscriptionPlan;
import com.nichesolv.evahanam.common.jpa.ComboPlan;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.jpa.DataParsingPlan;
import com.nichesolv.evahanam.common.repository.ActiveVehicleSubscriptionPlanRepository;
import com.nichesolv.evahanam.common.repository.ComboPlanRepository;
import com.nichesolv.evahanam.common.repository.DataFrequencyPlanRepository;
import com.nichesolv.evahanam.common.repository.DataParsingPlanRepository;
import com.nichesolv.evahanam.telemetryData.dto.BatteryDataDto;
import com.nichesolv.evahanam.telemetryData.dto.TelemetryDataDto;
import com.nichesolv.evahanam.telemetryData.enums.ExportDataType;
import com.nichesolv.evahanam.telemetryData.jpa.TelemetryIdx;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleBatteryData;
import com.nichesolv.evahanam.telemetryData.repository.TelemetryBatteryRepository;
import com.nichesolv.evahanam.telemetryData.service.batteryData.BatteryServiceImpl;
import com.nichesolv.evahanam.telemetryData.service.locationData.VehicleLocationDataServiceImpl;
import com.nichesolv.evahanam.telemetryData.service.telemetryData.TelemetryDataServiceImpl;
import com.nichesolv.evahanam.telemetryData.service.vehicleData.VehicleDataService;
import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.jpa.Part;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.PartRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.jpa.PartModel;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.evahanam.vehicleModel.repository.PartModelRepository;
import com.nichesolv.evahanam.vehicleModel.repository.VehicleModelRepository;
import com.nichesolv.evahanam.vehicleTests.dto.TestResultsResponse;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.jpa.TestType;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTestDetails;
import com.nichesolv.evahanam.vehicleTests.repository.ConnectivityTestSummaryRepository;
import com.nichesolv.evahanam.vehicleTests.repository.TestTypeRepository;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestDetailsRepository;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import com.nichesolv.evahanam.vehicleTests.v2.dto.ResultDto;
import com.nichesolv.evahanam.vehicleTests.v2.dto.VehicleTestResponse;
import com.nichesolv.evahanam.vehicleTests.v2.service.VehicleTestService;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.nds.repository.CustomUserRepository;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.After;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Slf4j
public class VehicleEolServiceV2Test {

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    VehicleTestService vehicleTestService;

    @Autowired
    CustomUserRepository userRepository;

    @Autowired
    VehicleTestRepository vehicleTestRepository;

    @Autowired
    TestTypeRepository testTypeRepository;

    @Autowired
    ActiveVehicleSubscriptionPlanRepository activeVehicleSubscriptionPlanRepository;

    @Autowired
    DataFrequencyPlanRepository dataFrequencyPlanRepository;

    @Autowired
    ComboPlanRepository comboPlanRepository;

    @Autowired
    DataParsingPlanRepository dataParsingPlanRepository;
    @Autowired
    VehicleModelRepository vehicleModelRepository;

    @Autowired
    VehicleTestDetailsRepository vehicleTestDetailsRepository;

    @Autowired
    ConnectivityTestSummaryRepository connectivityTestSummaryRepository;

    private Long testId;

    @Autowired
    BatteryServiceImpl batteryService;

    @Autowired
    VehicleDataService vehicleDataService;

    @Autowired
    TelemetryDataServiceImpl telemetryDataService;




    @BeforeEach
    public void setup() {
        CustomOrganisation organisation = new CustomOrganisation();
        organisation.setUrlSlug("manufacturerTest1");
        customOrganisationRepository.save(organisation);
        Vehicle vehicle = new Vehicle();
        vehicle.setImei("19");
        vehicle.setChassisNumber("CHASSIS19");
        vehicle.setManufacturer(organisation);
        vehicle.setOperationStatus(OperationStatus.ACTIVE);
        VehicleModel vehicleModel = new VehicleModel();
        vehicleModel.setName("testModel");
        vehicleModel.setModelNo("testModel");
        vehicleModel.setManufacturer(organisation);
        vehicleModelRepository.save(vehicleModel);
        vehicle.setVehicleModel(vehicleModel);
        vehicleRepository.save(vehicle);

        Optional<DataFrequencyPlan> dataFrequencyPlan = dataFrequencyPlanRepository.findByName("High Frequency");
        Optional<DataParsingPlan> dataParsingPlan = dataParsingPlanRepository.findByName("Gold");
        Optional<ComboPlan> comboPlan = comboPlanRepository.findByName("Diamond package + High Frequency");
        ActiveVehicleSubscriptionPlan activeVehicleSubscriptionPlan = new ActiveVehicleSubscriptionPlan();
        activeVehicleSubscriptionPlan.setVehicle(vehicleRepository.findByImei("19").get());
        activeVehicleSubscriptionPlan.setComboPlan(comboPlan.get());
        activeVehicleSubscriptionPlan.setDataParsingPlan(dataParsingPlan.get());
        activeVehicleSubscriptionPlan.setDataFrequencyPlan(dataFrequencyPlan.get());
        activeVehicleSubscriptionPlanRepository.save(activeVehicleSubscriptionPlan);
        log.debug(" data frequency plan : {} , data parsing plan : {}  , combo plan : {} , active subscription plan : {}",
                dataFrequencyPlan.get().getName(), dataParsingPlan.get().getName(), comboPlan.get().getName(), activeVehicleSubscriptionPlan.getId());
        Optional<CustomUser> user = userRepository.findByEmailIgnoreCase("<EMAIL>");

        TelemetryIdx idx = new TelemetryIdx(Instant.ofEpochSecond(1738899665L), "19");
        TelemetryIdx idxTwo = new TelemetryIdx(Instant.ofEpochSecond(1738899675L), "19");
        BatteryDataDto batteryDataDto = new BatteryDataDto("19", 1738899665L, UUID.randomUUID().toString(), 5f, 6f, null, 4f, 3f, 7f, 15f, 16f, 1, 2, 5,20f, 20f, 20f, 20f,0);
        batteryService.add(batteryDataDto);
        TelemetryDataDto telemetryDataDto = new TelemetryDataDto("19", 1738899665L, UUID.randomUUID().toString(), null, null, false, true, false, true, true, true, 1.2f, 2.3f, 1.8f, 2.3f, 3, 4);
        TelemetryDataDto telemetryDataDtoTest = new TelemetryDataDto("19", 1738899675L, UUID.randomUUID().toString(),null,null, false, true, false, true, true, true, 1.2f, 2.3f, 1.8f, 2.3f, 3, 4);
        telemetryDataService.addData(telemetryDataDto);
        telemetryDataService.addData(telemetryDataDtoTest);
    }

    @Test
    @Rollback
    @Transactional
    public void startTest() throws InterruptedException {
        Vehicle vehicle = vehicleRepository.findByImei("19").get();
        Optional<CustomUser> user = userRepository.findByEmailIgnoreCase("<EMAIL>");
        Optional<ActiveVehicleSubscriptionPlan> activeVehicleSubscriptionPlan = activeVehicleSubscriptionPlanRepository.findByVehicle(vehicle);
        Instant startTime = Instant.now();
        VehicleTestResponse vehicleTestResponse = vehicleTestService.startTest(vehicle, startTime, TestTypeName.CONNECTIVITY, user.get());
        TestType testType = testTypeRepository.findByTestTypeName(TestTypeName.CONNECTIVITY).get();
        List<VehicleTest> vehicleTestList = vehicleTestRepository.findByVehicleAndTestTypeAndStatus(vehicle, testType, TestStatus.RUNNING);
        assertEquals(vehicleTestList.get(0).getStartTime().toEpochMilli(), vehicleTestResponse.getStartTime());
        assertEquals(vehicleTestList.get(0).getEndTime().toEpochMilli(),vehicleTestResponse.getEndTime());
        assertEquals(TestStatus.RUNNING ,vehicleTestResponse.getStatus());
    }

    @Test
    @Transactional
    @Rollback
    public void resultTest() {
        Vehicle vehicle = vehicleRepository.findByImei("19").get();
        Optional<CustomUser> user = userRepository.findByEmailIgnoreCase("<EMAIL>");
        Instant startTime = Instant.now();
        VehicleTestResponse vehicleTestResponse = vehicleTestService.startTest(vehicle, startTime, TestTypeName.CONNECTIVITY, user.get());
        TestResultsResponse testResultsResponse = vehicleTestService.getVehicleTestResult(vehicleTestResponse.getTestId());
        TestType testType = testTypeRepository.findByTestTypeName(TestTypeName.CONNECTIVITY).get();
        List<VehicleTest> vehicleTestList = vehicleTestRepository.findByVehicleAndTestTypeAndStatus(vehicle, testType, TestStatus.RUNNING);
        assertEquals(vehicleTestList.get(0).getId(), testResultsResponse.getTestId());
        assertEquals("RUNNING", testResultsResponse.getStatus().name());
        assertEquals("OVERALL_DATA_AVAILABILITY", testResultsResponse.getData().get().getConnectivity().get(0).getName().name());
        assertEquals(0.0f, testResultsResponse.getData().get().getConnectivity().get(0).getValue());
        assertEquals("%", testResultsResponse.getData().get().getConnectivity().get(0).getUnit());
        assertEquals("AVAILABILITY", testResultsResponse.getData().get().getConnectivity().get(0).getCategory().name());
    }

    @Test
    public void resultTestTwo() throws InterruptedException {
        Vehicle vehicle = vehicleRepository.findByImei("19").get();
        Optional<CustomUser> user = userRepository.findByEmailIgnoreCase("<EMAIL>");
        Instant startTime = Instant.now();
        VehicleTestResponse vehicleTestResponse = vehicleTestService.startTest(vehicle, startTime, TestTypeName.CONNECTIVITY, user.get());
        Instant endTime = Instant.ofEpochMilli(vehicleTestResponse.getEndTime());
        log.info(" vehicle test id : {} ", vehicleTestResponse.getTestId());
        TestResultsResponse testResultsResponse = vehicleTestService.getVehicleTestResult(vehicleTestResponse.getTestId());
        testId = testResultsResponse.getTestId();
        assertEquals("RUNNING", testResultsResponse.getStatus().name());
        Instant endTimeWithBufferTime = endTime.plus(Duration.ofMinutes(1));
        log.info(" start time :{} , end time :{} , bufferTime  :{}", startTime, endTime, endTimeWithBufferTime);
        while (Instant.now().isBefore(endTimeWithBufferTime)) {
            log.info("inside while loop time :{} , bufferTimeWithEndTime :{}", Instant.now(), endTimeWithBufferTime);
            testResultsResponse = vehicleTestService.getVehicleTestResult(vehicleTestResponse.getTestId());
            if ("COMPLETED".equals(testResultsResponse.getStatus().name())) {
                break;
            }
            Thread.sleep(10000);
        }
        assertEquals("COMPLETED", testResultsResponse.getStatus().name(), "Test did not complete within the expected time.");
        log.info("Test completed at: {}", Instant.now());
    }


    @Test
    @Transactional
    @Rollback
    public void dataTest() {
        Vehicle vehicle = vehicleRepository.findByImei("19").get();
        ResultDto resultDto = vehicleDataService.getData("19", Optional.empty(), Optional.of(1738899660000L), Optional.of(1738899901000L), ExportDataType.BATTERY, null, "1m",true);
        Set<String> missingFieldSet = resultDto.getMissingField();
        assertEquals(1,resultDto.getMissingField().size());
        String columnName = missingFieldSet.stream().findFirst().orElse(null);
        assertEquals("soh",columnName);
        ResultDto resultDtoTelemetry = vehicleDataService.getData("19", Optional.empty(), Optional.of(1738899660000L), Optional.of(1738899901000L), ExportDataType.MOTOR, null, "1m",true);
        Set<String> missingFieldListMotor = resultDtoTelemetry.getMissingField();
        Set<String> expectedSet = Set.of("motor_fault_feedback", "motor_speed", "motor_dc_voltage", "motor_dc_current");
        log.info(" missing Field set {} , expected field set {}", missingFieldListMotor.toString(), expectedSet);
        assertTrue(missingFieldListMotor.equals(expectedSet));
    }

    @AfterEach
    @Transactional
    public void cleanup() {
        if (testId != null) {
            List<VehicleTestDetails> vehicleTestDetails =vehicleTestDetailsRepository.findByVehicleTest(vehicleTestRepository.findById(testId).get());
            vehicleTestDetailsRepository.deleteAll(vehicleTestDetails);
            connectivityTestSummaryRepository.delete(connectivityTestSummaryRepository.findByVehicleTest(vehicleTestRepository.findById(testId).get()).get());
            vehicleTestRepository.deleteById(testId);
        }
        Vehicle vehicle = vehicleRepository.findByImei("19").get();
        log.info("inside the after each vehicle_id {} ", vehicle.getId());
        VehicleModel vehicleModel = vehicle.getVehicleModel();
        Optional<ActiveVehicleSubscriptionPlan> activeVehicleSubscriptionPlan = activeVehicleSubscriptionPlanRepository.findByVehicle(vehicle);
        activeVehicleSubscriptionPlanRepository.delete(activeVehicleSubscriptionPlan.get());
        vehicleRepository.delete(vehicle);
        vehicleModelRepository.delete(vehicleModel);
    }
}
