package com.nichesolv.evahanam.vehicleTests.trip.service;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicleTests.dto.ConnectivityDataPercentageApiResponseDto;
import com.nichesolv.evahanam.vehicleTests.dto.StopTestRequest;
import com.nichesolv.evahanam.vehicleTests.dto.TestResultsResponse;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.jpa.ConnectivityTestSummary;
import com.nichesolv.evahanam.vehicleTests.jpa.TestType;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicleTests.repository.ConnectivityTestSummaryRepository;
import com.nichesolv.evahanam.vehicleTests.repository.TestTypeRepository;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import com.nichesolv.evahanam.vehicleTests.service.IVehicleEolService;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.nds.repository.CustomUserRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
public class VehicleEolServiceTest {


    @Autowired
    VehicleTestRepository vehicleTestRepository;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    IVehicleEolService vehicleEolService;

    private Vehicle savedVehicle;

    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    IVehicleEolService iVehicleEolService;

    @Autowired
    CustomUserRepository customUserRepository;


    @Autowired
    ConnectivityTestSummaryRepository connectivityTestSummaryRepository;

    private CustomOrganisation savedOrganisation;
    private CustomUser savedUser;

    @Autowired
    TestTypeRepository testTypeRepository;

    @BeforeEach
    void setUp() {
        Vehicle vehicle = new Vehicle();
        CustomOrganisation organisation = new CustomOrganisation();
        vehicle.setImei("123456789");
        vehicle.setChassisNumber("CHASSIS9850");
        organisation.setUrlSlug("testSlug");
        savedOrganisation = customOrganisationRepository.save(organisation);
        vehicle.setManufacturer(organisation);
        vehicle.setOwner(organisation);
        savedVehicle = vehicleRepository.save(vehicle);

        CustomUser customUser = new CustomUser();
        customUser.setFirstName("testUser");
        customUser.setLastName("user");
        customUser.setEmail("<EMAIL>");
        customUser.setPassword("userPassword");
        savedUser = customUserRepository.save(customUser);
    }

    @Test
    @Transactional
    @Rollback
    void stopTest_FormattedTest() {
        Optional<TestType> testType = testTypeRepository.findByTestTypeName(TestTypeName.CONNECTIVITY);
        VehicleTest vehicleTest = new VehicleTest(savedVehicle, testType.get(), Instant.now().minusSeconds(10000), Instant.now(), null);
        vehicleTest = vehicleTestRepository.save(vehicleTest);
        TestResultsResponse testResult = vehicleEolService.stopTest_Formatted(new StopTestRequest("123456789", vehicleTest.getId(), Optional.of(Instant.now().toEpochMilli()), 3));
        Assertions.assertEquals(vehicleTest.getId(), testResult.getTestId());
        Assertions.assertEquals(vehicleTest.getTestType(), TestTypeName.CONNECTIVITY);
        Assertions.assertEquals(testResult.getStatus(), TestStatus.COMPLETED);
    }

    @Test
    @Transactional
    @Rollback
    void getConnectivityDataPercentageTest() {
        Optional<TestType> testType = testTypeRepository.findByTestTypeName(TestTypeName.CONNECTIVITY);
        VehicleTest vehicleTest = new VehicleTest(savedVehicle, testType.get(), Instant.now().minusSeconds(10000), Instant.now(), savedUser.getId());
        vehicleTestRepository.save(vehicleTest);
        ConnectivityTestSummary connectivityTestSummary = new ConnectivityTestSummary();
        connectivityTestSummary.setVehicleTest(vehicleTest);
        connectivityTestSummary.setTestType(TestTypeName.CONNECTIVITY);
        connectivityTestSummary.setImei("123456789");
        connectivityTestSummary.setAccelAvailabilityPerc(10.0);
        connectivityTestSummary.setBattAvailabilityPerc(20.0);
        connectivityTestSummaryRepository.save(connectivityTestSummary);

        ConnectivityDataPercentageApiResponseDto connectivityDataAfterTest = iVehicleEolService.getConnectivityDataPercentage(savedOrganisation.getId());
        assertTrue(connectivityDataAfterTest.getOverall().getAvgAccelerometerAvailabilityPercentage() >= 10.0);
        assertTrue(connectivityDataAfterTest.getOverall().getAvgBatteryAvailabilityPercentage() >= 20.0);
    }
}
