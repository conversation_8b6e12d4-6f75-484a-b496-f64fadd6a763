package com.nichesolv.evahanam.vehicleTests.trip.service;
import com.nichesolv.evahanam.common.enums.FeatureName;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import com.nichesolv.evahanam.common.repository.DataFrequencyPlanDetailsRepository;
import com.nichesolv.evahanam.common.repository.DataFrequencyPlanRepository;
import com.nichesolv.evahanam.evApp.jpa.UserVehicleConnection;
import com.nichesolv.evahanam.evApp.repository.UserVehicleConnectionRepository;
import com.nichesolv.evahanam.trip.dto.tripsummary.TripRecalculationTimeWindowDto;
import com.nichesolv.evahanam.trip.dto.tripsummary.TripsToArchiveAndRecalculatedTimeWindows;
import com.nichesolv.evahanam.trip.dto.tripsummary.VehicleStatusTimeWindowProjection;
import com.nichesolv.evahanam.trip.service.TripSummaryService;
import com.nichesolv.evahanam.vehicle.enums.UpdateSource;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatusIdx;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicle.service.VehicleService;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.EventConstants;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.trip.enums.UserVehicleTripEvents;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.jpa.EventDurationConstants;
import com.nichesolv.evahanam.trip.jpa.UserVehicleTrip;
import com.nichesolv.evahanam.trip.jpa.VehicleEventMonitor;
import com.nichesolv.evahanam.trip.rabbitmq.dto.TripHistoryResponse;
import com.nichesolv.evahanam.trip.repository.EventDurationConstantsRepository;
import com.nichesolv.evahanam.trip.repository.TripRepository;
import com.nichesolv.evahanam.trip.repository.UserVehicleTripRepository;
import com.nichesolv.evahanam.trip.repository.VehicleEventMonitorRepository;

import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.nds.repository.CustomUserRepository;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfile;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfileImpl;
import com.nichesolv.usermgmt.user.repository.organisation.OrganisationProfileRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
@Slf4j
class TripSummaryServiceTest {

    @Autowired
    private TripRepository tripRepository;

    @Autowired
    private VehicleRepository vehicleRepository;


    @Autowired
    UserVehicleConnectionRepository vehicleConnectionRepository;

    @Autowired
    private TripSummaryService tripSummaryService;

    @Autowired
    OrganisationProfileRepository organisationProfileRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    EventDurationConstantsRepository tripDurationConstantsRepository;

    @Autowired
    VehicleService vehicleService;

    @Autowired
    VehicleEventMonitorRepository vehicleEventMonitorRepository;

    @Autowired
    CustomUserRepository customUserRepository;

    @Autowired
    UserVehicleConnectionRepository userVehicleConnectionRepository;

    @Autowired
    UserVehicleTripRepository userVehicleTripRepository;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;

    @Autowired
    DataFrequencyPlanDetailsRepository dataFrequencyPlanDetailsRepository;

    @Autowired
    DataFrequencyPlanRepository dataFrequencyPlanRepository;

    CustomUser savedUser;

    Vehicle savedVehicle;

    List<VehicleStatus> savedVehicleStatus;

    List<Trip> savedDataDelayTrips;



    @BeforeEach
    void setUp() {
        //setUp for saveUserVehicleTripTest
        CustomUser customUser = new CustomUser();
        customUser.setFirstName("testUser");
        customUser.setLastName("user");
        customUser.setEmail("<EMAIL>");
        customUser.setPassword("userPassword");
        savedUser = customUserRepository.save(customUser);

        //set up for saveAutomaticTripsTest
        CustomOrganisation organisation = new CustomOrganisation();
        Set<CustomUser> customUserSet = new HashSet<>();
        customUserSet.add(customUser);
        organisation.setUrlSlug("manufacturerTest1");
        OrganisationProfile organisationProfile = new OrganisationProfileImpl();
        organisationProfile.setName("manufacturerTest1");
        organisationProfile.setEmail("<EMAIL>");
        organisationProfileRepository.save(organisationProfile);
        organisation.setOrganisationProfile(organisationProfile);
        organisation.setUsers(customUserSet);
        customOrganisationRepository.save(organisation);

        Vehicle vehicle = new Vehicle();
        vehicle.setImei("test876");
        vehicle.setChassisNumber("CHASSIS9995");
        vehicle.setManufacturer(organisation);
        vehicle.setOwner(organisation);
        savedVehicle = vehicleRepository.save(vehicle);

        List<EventDurationConstants> tripDurationConstantsList = new ArrayList<>();
        tripDurationConstantsList.add(new EventDurationConstants(EventConstants.MIN_TRIP_DURATION, 20, "seconds", organisation));
        tripDurationConstantsList.add(new EventDurationConstants(EventConstants.STOP_DURATION, 40, "seconds", organisation));
        tripDurationConstantsRepository.saveAll(tripDurationConstantsList);

        //setUp for saveUserVehicleTripTest
        Trip trip1 = new Trip();
        trip1.setImei("test876");
        trip1.setVehicle(vehicle);
        trip1.setStartTime(Instant.now().minusSeconds(300));
        trip1.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.IN_PROGRESS);
        trip1.setTripType(TripType.AUTOMATIC);
        tripRepository.save(trip1);



        //setup for saveDataDelayAutomaticTripsTest
        List<DataFrequencyPlan> dataFrequencyPlanList = dataFrequencyPlanRepository.findAll();
        if (dataFrequencyPlanList.isEmpty()) {
            List<DataFrequencyPlan> dataFrequencyPlans = new ArrayList<>();
            dataFrequencyPlans.add(new DataFrequencyPlan("High Frequency",1, ChronoUnit.SECONDS));
            dataFrequencyPlans.add(new DataFrequencyPlan("Medium Frequency",5,ChronoUnit.SECONDS));
            dataFrequencyPlans.add(new DataFrequencyPlan("Low Frequency",10,ChronoUnit.SECONDS));
            dataFrequencyPlanRepository.saveAll(dataFrequencyPlans);
        }
        List<DataFrequencyPlanDetails> dataFrequencyPlanDetailsList = dataFrequencyPlanDetailsRepository.findAll();
        if(dataFrequencyPlanDetailsList.isEmpty()){
            Optional<DataFrequencyPlan> dataFrequencyPlan= dataFrequencyPlanRepository.findByName("High Frequency");
            DataFrequencyPlanDetails dataFrequencyPlanDetails= new DataFrequencyPlanDetails();
            dataFrequencyPlanDetails.setComputationFrequency((short)10);
            dataFrequencyPlanDetails.setFeatureName(FeatureName.TRIP_UPDATION);
            dataFrequencyPlanDetails.setDataFrequencyPlan(dataFrequencyPlan.get());
            dataFrequencyPlanDetails.setUnit(ChronoUnit.HOURS);
            dataFrequencyPlanDetailsRepository.save(dataFrequencyPlanDetails);
        }

        List<VehicleStatus> vehicleStatusList = new ArrayList<>();
        String imei="test876";
        //stopped
        Instant statusTime= Instant.parse("2024-09-29T05:29:00Z");
        for(int i=1;i<=6;i++){
            VehicleStatusIdx stoppedvehicleStatusIdx = new VehicleStatusIdx(statusTime, imei);
            VehicleStatus stoppedVehicleStatus = new VehicleStatus(stoppedvehicleStatusIdx,savedVehicle ,VehicleState.STOPPED, null, null, statusTime, UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON);
            statusTime = statusTime.plusSeconds(10);
            vehicleStatusList.add(stoppedVehicleStatus);
        }
        //running
        for (int i=1;i<=30;i++){
            VehicleStatusIdx runningVehicleStatusIdx = new VehicleStatusIdx(statusTime, imei);
            VehicleStatus runningStatus = new VehicleStatus(runningVehicleStatusIdx, savedVehicle ,VehicleState.RUNNING, null, null, statusTime, UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON);
            statusTime = statusTime.plusSeconds(10);
            vehicleStatusList.add(runningStatus);
        }

        //stopped
        for(int i=1;i<=70;i++){
            VehicleStatusIdx stoppedvehicleStatusIdx = new VehicleStatusIdx(statusTime, imei);
            VehicleStatus stoppedVehicleStatus = new VehicleStatus(stoppedvehicleStatusIdx, savedVehicle ,VehicleState.STOPPED, null, null, statusTime, UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON);
            statusTime = statusTime.plusSeconds(10);
            vehicleStatusList.add(stoppedVehicleStatus);
        }

        //running
        for (int i=1;i<=30;i++){
            VehicleStatusIdx runningVehicleStatusIdx = new VehicleStatusIdx(statusTime, imei);
            VehicleStatus runningStatus = new VehicleStatus(runningVehicleStatusIdx,savedVehicle , VehicleState.RUNNING, null, null, statusTime, UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON);
            statusTime = statusTime.plusSeconds(10);
            vehicleStatusList.add(runningStatus);
        }

        //stopped
        for(int i=1;i<=70;i++){
            VehicleStatusIdx stoppedvehicleStatusIdx = new VehicleStatusIdx(statusTime, imei);
            VehicleStatus stoppedVehicleStatus = new VehicleStatus(stoppedvehicleStatusIdx,savedVehicle , VehicleState.STOPPED, null, null, statusTime, UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON);
            statusTime = statusTime.plusSeconds(10);
            vehicleStatusList.add(stoppedVehicleStatus);
        }
        savedVehicleStatus= vehicleStatusRepository.saveAll(vehicleStatusList);

        //setup for saveDataDelayUserTripsTest
        Instant startOn = Instant.parse("2024-09-29T05:30:10Z");
        Instant endOn = Instant.parse("2024-09-29T05:34:00Z");
        LocalDateTime localStartOn = LocalDateTime.ofInstant(startOn, ZoneId.systemDefault());
        LocalDateTime localEndOn = LocalDateTime.ofInstant(endOn, ZoneId.systemDefault());
        UserVehicleConnection userVehicleConnection = new UserVehicleConnection(savedUser,savedVehicle,10F,null,null,localStartOn,localEndOn);
        vehicleConnectionRepository.saveAndFlush(userVehicleConnection);

        //setUp for getTripsTobeArchivedAndNewTimeWindowsTest
        //timestamps for data-delay intervals
        Instant statusTime1=Instant.parse("2024-10-22T01:10:00.00Z");
        Instant statusTime2=Instant.parse("2024-10-22T01:30:00.00Z");

        List<VehicleStatus> dataDelayStatus = new ArrayList<>();
        for (int i=1;i<=31;i++){
            VehicleStatusIdx runningVehicleStatusIdx = new VehicleStatusIdx(statusTime1, savedVehicle.getImei());
            VehicleStatus runningStatus = new VehicleStatus(runningVehicleStatusIdx, savedVehicle ,VehicleState.RUNNING, null, null, statusTime1, UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON);
            statusTime1 = statusTime1.plusSeconds(10);
            dataDelayStatus.add(runningStatus);
        }
        for (int i=1;i<=31;i++){
            VehicleStatusIdx runningVehicleStatusIdx = new VehicleStatusIdx(statusTime2, savedVehicle.getImei());
            VehicleStatus runningStatus = new VehicleStatus(runningVehicleStatusIdx,savedVehicle , VehicleState.RUNNING, null, null, statusTime2, UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON);
            statusTime2 = statusTime2.plusSeconds(10);
            dataDelayStatus.add(runningStatus);
        }

        vehicleStatusRepository.saveAllAndFlush(dataDelayStatus);
        System.out.println("******************** vehicle_status ***********************");
        List<VehicleStatus> vehicleStatusList2= vehicleStatusRepository.findByVehicleStatusIdxImeiAndUpdateSource(savedVehicle.getImei(),UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON);
        vehicleStatusList2.forEach(e->{
            System.out.println(e.getVehicleStatusIdx().getImei()+" "+e.getVehicleStatusIdx().getTimestamp());
        });
        System.out.println("******************** data delay intervals ************************");
        List<VehicleStatusTimeWindowProjection> vehicleStatusTimeWindowProjectionList = vehicleStatusRepository.getDataDelayTimeWindowsByImeiAndUpdateSourceAndUpdatedOnBetween(savedVehicle.getImei(), UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON.name(), Instant.now().minusSeconds(3600),Instant.now(),600);
        vehicleStatusTimeWindowProjectionList.forEach(e->{
            System.out.println(e.getMinTimestamp()+" "+e.getMaxTimestamp());
        });

        //timestamps for trips
        Instant instant5=Instant.parse("2024-10-22T01:01:00.00Z");
        Instant instant6=Instant.parse("2024-10-22T01:08:00.00Z");
        Instant instant7=Instant.parse("2024-10-22T01:20:00.00Z");
        Instant instant8=Instant.parse("2024-10-22T01:26:00.00Z");
        Instant instant9=Instant.parse("2024-10-22T01:40:00.00Z");
        Instant instant10=Instant.parse("2024-10-22T01:50:00.00Z");

        List<Trip> trips = new ArrayList<>();
        Trip trip4= new Trip();
        trip4.setImei(savedVehicle.getImei());
        trip4.setVehicle(savedVehicle);
        trip4.setStartTime(instant5);
        trip4.setEndTime(instant6);
        trip4.setTripType(TripType.AUTOMATIC);
        trip4.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.COMPLETED);
        trip4.setUpdateSource(UpdateSource.TRIP_CRON);

        Trip trip2= new Trip();
        trip2.setImei(savedVehicle.getImei());
        trip2.setVehicle(savedVehicle);
        trip2.setStartTime(instant7);
        trip2.setEndTime(instant8);
        trip2.setTripType(TripType.AUTOMATIC);
        trip2.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.COMPLETED);
        trip2.setUpdateSource(UpdateSource.TRIP_CRON);

        Trip trip3= new Trip();
        trip3.setImei(savedVehicle.getImei());
        trip3.setVehicle(savedVehicle);
        trip3.setStartTime(instant9);
        trip3.setEndTime(instant10);
        trip3.setTripType(TripType.AUTOMATIC);
        trip3.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.COMPLETED);
        trip3.setUpdateSource(UpdateSource.TRIP_CRON);

        savedDataDelayTrips= tripRepository.saveAll(Arrays.asList(trip4,trip2,trip3));
    }


    @Rollback
    @Transactional
    @Test
    void processAutomatedTrips_ShouldCreateNewTrip_WhenNoExistingTrips() {
        TripHistoryResponse tripHistoryResponse = new TripHistoryResponse();
        tripHistoryResponse.setImei("867461076700851");
        tripHistoryResponse.setTripStarts(Arrays.asList(Instant.now()));
        tripHistoryResponse.setTripEnds(new ArrayList<>());

//        Vehicle vehicle = vehicleRepository.findByImei("867461076700851").orElse(null);
        List<Trip> trip = tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(tripHistoryResponse.getImei(), null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC);


        tripSummaryService.processAutomatedTrips(tripHistoryResponse);
        assertThat(trip.size()+1).isEqualTo(tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(tripHistoryResponse.getImei(), null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size());


    }


    @Rollback
    @Transactional
    @Test
    void processAutomatedTrips_ShouldThrowException_WhenMultipleExistingTrips() {
        String imei = "867461076700851";
//        Vehicle vehicle = new Vehicle(imei, null, null, LocalDate.now());
//        when(vehicleRepository.findByImei(imei)).thenReturn(Optional.of(vehicle));
        Vehicle vehicle = vehicleRepository.findByImei(imei).orElse(null);
        Instant start1 = Instant.now().minus(5, ChronoUnit.MINUTES);
        Instant start2 = Instant.now().minus(3, ChronoUnit.MINUTES);

        Trip newTrip1 = Trip.builder()
                .imei(imei)
                .vehicle(vehicle)
                .startTime(start1)
                .summaryPopulationStatus(TestRideSummaryPopulationStatus.IN_PROGRESS)
                .tripType(TripType.AUTOMATIC)
                .createdOn(Instant.now())
                .updatedOn(Instant.now())
                .build();

        Trip newTrip2 = Trip.builder()
                .imei(imei)
                .vehicle(vehicle)
                .startTime(start2)
                .summaryPopulationStatus(TestRideSummaryPopulationStatus.IN_PROGRESS)
                .tripType(TripType.AUTOMATIC)
                .createdOn(Instant.now())
                .updatedOn(Instant.now())
                .build();
        tripRepository.save(newTrip1);
        tripRepository.save(newTrip2);
//        when(tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(imei, null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC))
//                .thenReturn(Arrays.asList(newTrip1, newTrip2));

        TripHistoryResponse tripHistoryResponse = new TripHistoryResponse();
        tripHistoryResponse.setImei(imei);
        tripHistoryResponse.setTripStarts(Arrays.asList(Instant.now().minus(2, ChronoUnit.MINUTES)));
        tripHistoryResponse.setTripEnds(List.of());

        tripSummaryService.processAutomatedTrips(tripHistoryResponse);

//        verify(tripSummaryService1, times(1)).processAutomatedTrips(tripHistoryResponse);
        assertThat(tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(imei, null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size()).isEqualTo(2);
    }

    @Test
    @Rollback
    @Transactional
    void saveAutomaticTripsTest() {
        String imei = "test876";
        Instant now = Instant.now();
        Vehicle vehicle = vehicleRepository.findByImei(imei).get();
        VehicleStatusIdx vehicleStatusIdx = new VehicleStatusIdx(now, imei);

        VehicleStatus vehicleStatus = new VehicleStatus(vehicleStatusIdx,vehicle ,VehicleState.RUNNING, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);
        Optional<VehicleEventMonitor> vehicleEventMonitor = vehicleEventMonitorRepository.findByVehicle(vehicle);

        //vehicle has to run for 20 secs for the entry in the trip table, as i have setup MIN_TRIP_DURATION as 20 in setUp()
        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(10), imei);

        vehicleStatus = new VehicleStatus(vehicleStatusIdx, vehicle ,VehicleState.RUNNING, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);
        List<Trip> inProgressTrips = tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(imei, null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC);
        assertEquals(0, inProgressTrips.size());


        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(20), imei);
        vehicleStatus = new VehicleStatus(vehicleStatusIdx, vehicle ,VehicleState.RUNNING, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);
        inProgressTrips = tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(imei, null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC);
        assertEquals(1, inProgressTrips.size());

        //vehicle has to be in stopped for 40 secs for the trip to get completed, as i have setup STOP_DURATION as 40 in setUp()
        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(30), imei);
        vehicleStatus = new VehicleStatus(vehicleStatusIdx, vehicle ,VehicleState.STOPPED, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);

        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(40), imei);
        vehicleStatus = new VehicleStatus(vehicleStatusIdx,vehicle , VehicleState.IDLING, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);

        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(50), imei);
        vehicleStatus = new VehicleStatus(vehicleStatusIdx,vehicle , VehicleState.STOPPED, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);

        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(60), imei);
        vehicleStatus = new VehicleStatus(vehicleStatusIdx,vehicle , VehicleState.IDLING, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);

        vehicleStatusIdx = new VehicleStatusIdx(now.plusSeconds(70), imei);
        vehicleStatus = new VehicleStatus(vehicleStatusIdx,vehicle , VehicleState.STOPPED, null, null, now, UpdateSource.VEHICLE_STATUS_CRON);
        vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);

        Optional<Trip> completedTrip = tripRepository.findById(inProgressTrips.get(0).getId());
        assertTrue(completedTrip.isPresent());
        assertEquals(inProgressTrips.get(0).getId(), completedTrip.get().getId());
        assertEquals(TestRideSummaryPopulationStatus.COMPLETED, completedTrip.get().getSummaryPopulationStatus());
    }


    @Test
    @Rollback
    @Transactional
    void saveUserVehicleTripTest() {

        //on event user_connected
        List<Trip> inProgressTrip = tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType("test876", null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC);
        UserVehicleConnection userVehicleConnection = new UserVehicleConnection(savedUser, savedVehicle, 10.00f, 10, 10.0f, LocalDateTime.now().minusSeconds(120), null);
        userVehicleConnection = userVehicleConnectionRepository.save(userVehicleConnection);
        tripSummaryService.saveUserVehicleTrip(null, userVehicleConnection, UserVehicleTripEvents.USER_CONNECTED);
        List<UserVehicleTrip> inProgressUserVehicleTrips = userVehicleTripRepository.findByUserVehicleConnectionAndStatus(userVehicleConnection, TestRideSummaryPopulationStatus.IN_PROGRESS);
        assertEquals(1, inProgressUserVehicleTrips.size());

        //on event user-disconnected
        userVehicleConnection.setEndOn(LocalDateTime.now());
        userVehicleConnection = userVehicleConnectionRepository.save(userVehicleConnection);
        tripSummaryService.saveUserVehicleTrip(null, userVehicleConnection, UserVehicleTripEvents.USER_DISCONNECTED);
        inProgressUserVehicleTrips = userVehicleTripRepository.findByUserVehicleConnectionAndStatus(userVehicleConnection, TestRideSummaryPopulationStatus.IN_PROGRESS);
        assertEquals(0, inProgressUserVehicleTrips.size());

        //on event vehicle trip start
        Trip trip2 = new Trip();
        trip2.setImei("test876");
        trip2.setVehicle(savedVehicle);
        trip2.setStartTime(Instant.now().minusSeconds(300));
        trip2.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.IN_PROGRESS);
        trip2.setTripType(TripType.AUTOMATIC);
        trip2 = tripRepository.save(trip2);
        userVehicleConnection = new UserVehicleConnection(savedUser, savedVehicle, 10.00f, 10, 10.0f, LocalDateTime.now().minusSeconds(110), null);
        userVehicleConnection = userVehicleConnectionRepository.save(userVehicleConnection);
        tripSummaryService.saveUserVehicleTrip(trip2, null, UserVehicleTripEvents.TRIP_START);
        List<UserVehicleTrip> inProgressUserTrips = userVehicleTripRepository.findByTripAndStatus(trip2, TestRideSummaryPopulationStatus.IN_PROGRESS);
        assertEquals(1, inProgressUserTrips.size());

        //on event vehicle trip end
        trip2.setEndTime(Instant.now());
        trip2.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.COMPLETED);
        tripRepository.save(trip2);
        tripSummaryService.saveUserVehicleTrip(trip2, null, UserVehicleTripEvents.TRIP_END);
        inProgressUserTrips = userVehicleTripRepository.findByTripAndStatus(trip2, TestRideSummaryPopulationStatus.IN_PROGRESS);
        assertEquals(0, inProgressUserTrips.size());
    }


    @Test
    @Transactional
    @Rollback
    void saveDataDelayVehicleTripsTest() {
        String imei = "test876";
        Instant currentTime = Instant.parse("2024-09-29T19:00:00Z");
        Instant startTime = currentTime.minusSeconds(93600);
        Optional<DataFrequencyPlan> dataFrequencyPlan= dataFrequencyPlanRepository.findByName("High Frequency");
        Optional<DataFrequencyPlanDetails> dataFrequencyPlanDetails = dataFrequencyPlanDetailsRepository.findByFeatureNameAndDataFrequencyPlan(FeatureName.TRIP_UPDATION,dataFrequencyPlan.get());
        tripSummaryService.saveDataDelayedVehicleTrips(imei, startTime, currentTime, dataFrequencyPlanDetails.get());
        List<Trip> trips = tripRepository.findByImeiAndSummaryPopulationStatusAndTripTypeAndUpdateSourceAndEndTimeBetweenOrderByStartTime(imei, TestRideSummaryPopulationStatus.COMPLETED, TripType.AUTOMATIC, UpdateSource.TRIP_DATA_DELAY_CRON, startTime, currentTime);
        assertEquals(2, trips.size());
    }

    @Test
    @Transactional
    @Rollback
    void saveDataDelayedUserTripsTest() {
        List<UserVehicleTrip> userVehicleTrips = new ArrayList<>();
        String imei = "test876";
        Instant currentTime = Instant.parse("2024-09-29T19:00:00Z");
        Instant startTime = currentTime.minusSeconds(93600);
        Optional<DataFrequencyPlan> dataFrequencyPlan= dataFrequencyPlanRepository.findByName("High Frequency");
        Optional<DataFrequencyPlanDetails> dataFrequencyPlanDetails = dataFrequencyPlanDetailsRepository.findByFeatureNameAndDataFrequencyPlan(FeatureName.TRIP_UPDATION,dataFrequencyPlan.get());
        tripSummaryService.saveDataDelayedVehicleTrips(imei, startTime, currentTime, dataFrequencyPlanDetails.get());
        List<Trip> trips = tripSummaryService.saveDataDelayedVehicleTrips(imei, startTime, currentTime,dataFrequencyPlanDetails.get());
        tripSummaryService.saveDataDelayedUserTrips(trips);
        trips.forEach(e -> {
            userVehicleTrips.addAll(userVehicleTripRepository.findByTrip(e));
        });
        assertTrue(userVehicleTrips.size() > 1);
    }

    @Test
    @Transactional
    @Rollback
    void getTripsTobeArchivedAndNewTimeWindowsTest() {
        List<VehicleStatusTimeWindowProjection> vehicleStatusTimeWindowProjectionList = vehicleStatusRepository.getDataDelayTimeWindowsByImeiAndUpdateSourceAndUpdatedOnBetween(savedVehicle.getImei(), UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON.name(), Instant.now().minusSeconds(3600), Instant.now(), 600);
        TripsToArchiveAndRecalculatedTimeWindows tripsToArchiveAndRecalculatedTimeWindows = tripSummaryService.getTripsTobeArchivedAndNewTimeWindows(savedVehicle.getImei(), vehicleStatusTimeWindowProjectionList, 600);
        List<TripRecalculationTimeWindowDto> tripRecalculationTimeWindowDtos = tripsToArchiveAndRecalculatedTimeWindows.getTimeWindows();
        List<Trip> tripsToArchive = tripRepository.findAllById(tripsToArchiveAndRecalculatedTimeWindows.getTripIdsToArchive());
        assertEquals(savedDataDelayTrips.size(), tripsToArchive.size());
        assertEquals(2, tripRecalculationTimeWindowDtos.size());
    }
}