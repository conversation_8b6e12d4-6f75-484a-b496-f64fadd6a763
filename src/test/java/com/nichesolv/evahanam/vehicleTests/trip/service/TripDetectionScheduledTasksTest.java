package com.nichesolv.evahanam.vehicleTests.trip.service;

import com.nichesolv.evahanam.common.jpa.ActiveVehicleSubscriptionPlan;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.repository.ActiveVehicleSubscriptionPlanRepository;
import com.nichesolv.evahanam.common.repository.DataFrequencyPlanRepository;
import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.enums.UpdateSource;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatusIdx;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicleTests.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.vehicleTests.trip.enums.TripConstants;
import com.nichesolv.evahanam.vehicleTests.trip.enums.TripType;
import com.nichesolv.evahanam.vehicleTests.trip.jpa.Trip;
import com.nichesolv.evahanam.vehicleTests.trip.jpa.TripDurationConstants;
import com.nichesolv.evahanam.vehicleTests.trip.repository.TripDurationConstantRepository;
import com.nichesolv.evahanam.vehicleTests.trip.repository.TripRepository;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@SpringBootTest
public class TripDetectionScheduledTasksTest {

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;

    @Autowired
    ActiveVehicleSubscriptionPlanRepository activeVehicleSubscriptionPlanRepository;

    @Autowired
    TripDurationConstantRepository tripDurationConstantRepository;

    @Autowired
    DataFrequencyPlanRepository dataFrequencyPlanRepository;

    @Autowired
    TripDetectionScheduledTasks tripDetectionScheduledTasks;

    @Autowired
    TripSummaryService tripSummaryService;

    @Autowired
    TripRepository tripRepository;

    Vehicle savedVehicle;

    String vehicleImei="1234567889";

    CustomOrganisation savedOrganisation;

    @BeforeEach
    void setUp(){
        CustomOrganisation customOrganisation= customOrganisationRepository.findByUrlSlug("nds");
        Vehicle testVehicle = new Vehicle();
        testVehicle.setImei(vehicleImei);
        testVehicle.setChassisNumber("CHASSIS99960");
        CustomOrganisation organisation = new CustomOrganisation();
        organisation.setUrlSlug("manufacturerTest1");
        savedOrganisation= customOrganisationRepository.save(organisation);
        testVehicle.setManufacturer(customOrganisation);
        testVehicle.setOperationStatus(OperationStatus.ACTIVE);
        testVehicle.setOwner(organisation);
        savedVehicle= vehicleRepository.save(testVehicle);

        //setting up the trip_duration_constant
        List<TripDurationConstants> tripDurationConstantsList=new ArrayList<>();
        tripDurationConstantsList.add(new TripDurationConstants(TripConstants.MIN_TRIP_DURATION,60,"seconds",organisation));
        tripDurationConstantsList.add(new TripDurationConstants(TripConstants.STOP_DURATION,600,"seconds",organisation));
        tripDurationConstantRepository.saveAllAndFlush(tripDurationConstantsList);

        //getting the data frequency plan
        Optional<DataFrequencyPlan> dataFrequencyPlan= dataFrequencyPlanRepository.findByName("1minute");
        if(dataFrequencyPlan.isEmpty()){
            dataFrequencyPlan= Optional.of(dataFrequencyPlanRepository.save(new DataFrequencyPlan("1minute", 60, ChronoUnit.SECONDS)));
        }

        //setting the active_vehicle_subscription_plan
        activeVehicleSubscriptionPlanRepository.save(new ActiveVehicleSubscriptionPlan(testVehicle,dataFrequencyPlan.get()));

        //setting up the vehicle status to RUNNING
        List<VehicleStatus> vehicleStatusList=new ArrayList<>();
        Instant now= Instant.now();
        vehicleStatusList.add(new VehicleStatus(new VehicleStatusIdx(now.minusSeconds(660),vehicleImei), VehicleState.RUNNING,null,null,null, UpdateSource.VEHICLE_STATUS_CRON));
        vehicleStatusList.add(new VehicleStatus(new VehicleStatusIdx(now.minusSeconds(300),vehicleImei), VehicleState.RUNNING,null,null,null, UpdateSource.VEHICLE_STATUS_CRON));
        vehicleStatusList.add(new VehicleStatus(new VehicleStatusIdx(now.minusSeconds(240),vehicleImei), VehicleState.RUNNING,null,null,null, UpdateSource.VEHICLE_STATUS_CRON));
        vehicleStatusList.add(new VehicleStatus(new VehicleStatusIdx(now.minusSeconds(180),vehicleImei), VehicleState.RUNNING,null,null,null, UpdateSource.VEHICLE_STATUS_CRON));
        vehicleStatusList.add(new VehicleStatus(new VehicleStatusIdx(now.minusSeconds(120),vehicleImei), VehicleState.RUNNING,null,null,null, UpdateSource.VEHICLE_STATUS_CRON));
        vehicleStatusList.add(new VehicleStatus(new VehicleStatusIdx(now.minusSeconds(60),vehicleImei), VehicleState.RUNNING,null,null,null, UpdateSource.VEHICLE_STATUS_CRON));
        vehicleStatusList.add(new VehicleStatus(new VehicleStatusIdx(now,vehicleImei), VehicleState.RUNNING,null,null,null, UpdateSource.VEHICLE_STATUS_CRON));
        vehicleStatusRepository.saveAllAndFlush(vehicleStatusList);

    }

    @Test
    @Transactional
    @Rollback
    void startTripDetectionCronTest(){
        tripDetectionScheduledTasks.startTripDetectionCron();
        List<Trip> trips= tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(vehicleImei,null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC);
        assertEquals(1, trips.size());
    }

    @Test
    @Transactional
    @Rollback
    void endTripDetectionCronTest(){
        tripDetectionScheduledTasks.endTripDetectionCron();
        List<Trip> trips= tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(vehicleImei,null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC);
        assertEquals(0,trips.size());
    }
}
