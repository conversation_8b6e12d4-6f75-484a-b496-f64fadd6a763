package com.nichesolv.evahanam.common.service;

import com.nichesolv.evahanam.common.dto.ComboPlanDto;
import com.nichesolv.evahanam.common.dto.ComboPlanPostRequestDto;
import com.nichesolv.evahanam.common.dto.DataFrequencyPlanDto;
import com.nichesolv.evahanam.common.dto.DataParsingPlanDto;
import com.nichesolv.evahanam.common.enums.ParsingFieldType;
import com.nichesolv.evahanam.common.enums.PlanStatus;
import com.nichesolv.evahanam.common.enums.SubscriptionPlanStatus;
import com.nichesolv.evahanam.common.jpa.ComboPlan;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.jpa.DataParsingField;
import com.nichesolv.evahanam.common.jpa.DataParsingPlan;
import com.nichesolv.evahanam.common.repository.ComboPlanRepository;
import com.nichesolv.evahanam.common.repository.DataFrequencyPlanRepository;
import com.nichesolv.evahanam.common.repository.DataParsingFieldRepository;
import com.nichesolv.evahanam.common.repository.DataParsingPlanRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.temporal.ChronoUnit;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class SubscriptionServiceTest {

    @Autowired
    SubscriptionService subscriptionService;

    @Autowired
    DataParsingFieldRepository dataParsingFieldRepository;

    @Autowired
    DataFrequencyPlanRepository dataFrequencyPlanRepository;

    @Autowired
    ComboPlanRepository comboPlanRepository;

    @Autowired
    DataParsingPlanRepository dataParsingPlanRepository;

    DataParsingPlan savedDataParsingPlan;

    DataFrequencyPlan savedDataFrequencyPlan;

    ComboPlan savedComboPlan;


    @BeforeEach
    void setUp(){
        dataParsingFieldRepository.save(new DataParsingField("testField3", ParsingFieldType.TELEMETRY));
        dataParsingFieldRepository.save(new DataParsingField("testField4", ParsingFieldType.LOCATION));
        dataParsingFieldRepository.save(new DataParsingField("testField5", ParsingFieldType.LOCATION));


        //setup for getDataFrequencyPlanTest
        dataFrequencyPlanRepository.save(new DataFrequencyPlan("Testing Frequency Plan",10, ChronoUnit.SECONDS));

        //setup for getDataParsingPlanTest
        comboPlanRepository.save(new ComboPlan("Testing Combo Plan",null,null, SubscriptionPlanStatus.ACTIVE));

        //setUp for getParsingPlanTest
        savedDataParsingPlan= dataParsingPlanRepository.save(new DataParsingPlan("Test-2","Test-plan-2",PlanStatus.ACTIVE,new HashSet<>()));

        savedDataFrequencyPlan= dataFrequencyPlanRepository.save(new DataFrequencyPlan("Testing Frequency Plan",10, ChronoUnit.SECONDS));

        //setUp for getParsingPlanTest
        savedDataParsingPlan= dataParsingPlanRepository.save(new DataParsingPlan("Test-2","Test-plan-2",PlanStatus.ACTIVE,new HashSet<>()));

        //setUp for getComboPlanTest
        savedComboPlan = comboPlanRepository.save(new ComboPlan("Testing Combo Plan",savedDataFrequencyPlan,savedDataParsingPlan,SubscriptionPlanStatus.ACTIVE));
    }

    @Test
    @Transactional
    @Rollback
    void getAllSupportedParsingFieldsTest() {
        Map<String, List<String>> dataParsingFields = subscriptionService.getAllSupportedParsingFields(Optional.of(ParsingFieldType.TELEMETRY));
        List<String> telemetryDataParsingFieldsBefore = dataParsingFields.get(ParsingFieldType.TELEMETRY.name());
        dataParsingFieldRepository.save(new DataParsingField("testField1", ParsingFieldType.TELEMETRY));
        List<String> telemetryDataParsingFieldsAfter = subscriptionService.getAllSupportedParsingFields(Optional.of(ParsingFieldType.TELEMETRY)).get(ParsingFieldType.TELEMETRY.name());
        assertTrue(telemetryDataParsingFieldsAfter.size() > telemetryDataParsingFieldsBefore.size());
        assertTrue(dataParsingFields.containsKey(ParsingFieldType.TELEMETRY.name()));
        assertFalse(dataParsingFields.containsKey(ParsingFieldType.LOCATION.name()));

        dataParsingFieldRepository.save(new DataParsingField("testField2", ParsingFieldType.TELEMETRY));
        dataParsingFieldRepository.save(new DataParsingField("testField2", ParsingFieldType.LOCATION));
        dataParsingFieldRepository.save(new DataParsingField("testField2", ParsingFieldType.LOCATION));
        Map<String, List<String>> allDataParsingFields = subscriptionService.getAllSupportedParsingFields(Optional.empty());
        assertTrue(allDataParsingFields.size() > 1);
        assertTrue(allDataParsingFields.containsKey(ParsingFieldType.TELEMETRY.name()));
        assertTrue(allDataParsingFields.containsKey(ParsingFieldType.LOCATION.name()));
    }

    @Test
    @Rollback
    @Transactional
    void getParsingPlanTest() {
        List<DataParsingPlanDto> dataParsingPlans = subscriptionService.getParsingPlan(Optional.of(savedDataParsingPlan.getId()));
        assertFalse(dataParsingPlans.isEmpty());
    }

    @Transactional
    @Rollback
    @Test
    void saveParsingPlanTest() {
        subscriptionService.saveParsingPlan(new DataParsingPlanDto("Test-1", "testing plan", PlanStatus.ACTIVE, new HashSet<>()));
        Optional<DataParsingPlan> dataParsingPlan= dataParsingPlanRepository.findByName("Test-1");
        assertFalse(dataParsingPlan.isEmpty());
        assertEquals("Test-1", dataParsingPlan.get().getName());
    }

    @Test
    @Transactional
    @Rollback
    void saveDataFrequencyPlanTest() {
        Optional<DataFrequencyPlan> dataFrequencyPlan = dataFrequencyPlanRepository.findByName("Data Frequency Plan Test");
        assertFalse(dataFrequencyPlan.isPresent());
        subscriptionService.saveDataFrequencyPlan(new DataFrequencyPlanDto("Data Frequency Plan Test", 10, "SECONDS"));
        dataFrequencyPlan = dataFrequencyPlanRepository.findByName("Data Frequency Plan Test");
        assertTrue(dataFrequencyPlan.isPresent());
        assertEquals("Data Frequency Plan Test", dataFrequencyPlan.get().getName());
    }

    @Test
    @Transactional
    @Rollback
    void getDataFrequencyPlanTest() {
        List<DataFrequencyPlanDto> dataFrequencyPlanDtosList = subscriptionService.getDataFrequencyPlan(Optional.empty());
        assertFalse(dataFrequencyPlanDtosList.isEmpty());
        List<DataFrequencyPlanDto> dataParsingPlanDto = subscriptionService.getDataFrequencyPlan(Optional.of(savedDataFrequencyPlan.getId()));
        assertEquals("Testing Frequency Plan", dataParsingPlanDto.get(0).getName());
    }

    @Test
    @Transactional
    @Rollback
    void getComboPlanTest() {
        List<ComboPlanDto> comboPlanDtoList = subscriptionService.getComboPlan(Optional.empty());
        assertFalse(comboPlanDtoList.isEmpty());
        List<ComboPlanDto> comboPlanDto = subscriptionService.getComboPlan(Optional.of(savedComboPlan.getId()));
        assertEquals("Testing Combo Plan", comboPlanDto.get(0).getName());
    }

    @Test
    @Transactional
    @Rollback
    void saveComboPlanTest() {
        subscriptionService.saveComboPlan(new ComboPlanPostRequestDto("Testing Combo Plan", SubscriptionPlanStatus.ACTIVE, null, null));
        Optional<ComboPlan> comboPlan = comboPlanRepository.findByName("Testing Combo Plan");
        assertTrue(comboPlan.isPresent());
        assertEquals("Testing Combo Plan", comboPlan.get().getName());
    }

}
