PART_MFR_NOT_FOUND=part manufacture not found 
BATTERY_MFR_NOT_FOUND=Battery Manufacturer not found
VEHICLE_NOT_FOUND=Vehicle not found
VEHICLE_NOT_ACTIVE=Vehicle operation status inactive
VEHICLE_NOT_FOUND_WITH_IMEI=Vehicle with imei {0} not found
VEHICLE_AND_CHASSIS_EMPTY=given chassis number and imei both are empty
VEHICLE_NOT_FOUND_WITH_INFO=Vehicle not found {0}
PART_IS_ALREADY_ASSOCIATED=part {0} is already associated with another vehicle
ENCRYPTION_KEY_EMPTY=Encryption key is empty {0}
ORG_NOT_FOUND=Organisation not found
REQUIRED_ORG_NOT_FOUND=organisation {0} is not found for user
ORG_TYPE_NOT_FOUND=organisation type not correct
ORG_NOT_FOUND_WITH_ORG_INFO=Organisation not found {0}
DEALER_NOT_FOUND=Dealer with name {0} not found
USER_NOT_FOUND=User does not exist
PART_NOT_FOUND=Part not found
IMAGE_NOT_FOUND=Image does not exist with id  {0}
IMAGE_TAG_ALREADY_EXISTS_FOR_COLOR=Image with {0} tag already exists for the given vehicle model-color model combination
IMAGE_TAG_ALREADY_EXISTS_FOR_PART=Image coordinates with {0} tag already exists for the given vehicle model-part model combination
PART_SERIAL_NOT_FOUND=Part with SerialNo {0} not found
PART_BATCH_NOT_FOUND=Part with BatchNo {0} not found
PART_SERIAL_BATCH_NOT_FOUND=Part with SerialNo {0} & BatchNo {1} not found
SERIAL_BATCH_EMPTY=At least one of BatchNo or SerialNo is mandatory
PART_ALREADY_EXIST=Part already exists
MFR_NOT_FOUND=Manufacturer not found
ALARM_TYPE_NOT_FOUND=Alarm type not found for the organisation {0}
TEMP_MAX_ALERT_LOW_NOT_FOUND=Part model attributes tempMaxAlertLow is not exists for vehicle {0}
TEMP_MAX_ALERT_MEDIUM_NOT_FOUND=Part model attributes tempMaxAlertMedium is not exists for vehicle {0}
TEMP_MAX_ALERT_HIGH_NOT_FOUND=Part model attributes tempMaxAlertHigh is not exists for vehicle {0}
2W_REAR_TYRE_LOW_TYRE_PRESSURE_NOT_FOUND=Part model attribute 2wRearTyreLowTyrePressure is not present for vehicle {0}
2W_REAR_TYRE_HIGH_TYRE_PRESSURE_NOT_FOUND=Part model attribute 2wRearTyreHighTyrePressure is not present for vehicle {0}
2W_REAR_TYRE_DISTANCE_RATIO_NOT_FOUND=Part model attribute 2wRearTyreDistanceRatio is not present for vehicle {0)
REAR_TYRE_DIAMETER_NOT_FOUND=Part model attribute rearTyreDiameter is not present for vehicle {0}
FLEET_ASSOCIATED_WITH_ORG=Fleet already associated with an organisation
FLEET_ASSOCIATED_WITH_DIFF_ORG=Fleet already associated with a different organisation
FLEET_NOT_FOUND=Fleet does not exist
VEHICLE_ASSIGNED_WITH_OTHER_FLEET=Vehicle already assigned with another fleet
FLEET_DO_NOT_BELONGS_TO_ORG=Fleet does not belong to the organisation
PART_MODEL_NOT_FOUND=Part model does not exist
VEHICLE_MODEL_NOT_FOUND=vehicle model not exist with name {0}
COLOR_MODEL_NOT_FOUND=color model not exist with name  {0}
PART_MODEL_NOT_FOUND_IN_VEHICLE_MODEL=part model is not found in vehicle model
PROVIDE_IMEI_CHASSIS_ID=Expecting IMEI or Chassis or ID or RegNo as request
PART_ATTRIBUTE_NOT_FOUND_WITH_INFO=vehicle attribute not found with attributeName: {0}, partType: {1} and IMEI:{2}
DRIVE_MODEL_EXISTS_FOR_ORG=Drive mode already exists for this organisation {0}
PARENT_ID_NOT_FOUND=Parent id does not exist
PART_EXISTS_WITH_NAME_AND_MFR=part is already exist with name: {0} and manufacturer: {1} 
PART_MODEL_MFR_NOT_FOUND=Part model manufacturer {0} not found 
PART_MODEL_NOT_FOUND_WITH_NAME_AND_MFR_INFO=Part model is not exist with part model name {0} and manufacturer {1}
VEHICLE_MODEL_NOT_FOUND_WITH_NAME_AND_MFR_INFO=Vehicle model is not exist with vehicle model name {0} and manufacturer {1}
VEHICLE_MODEL_MFR_NOT_FOUND=Vehicle model manufacturer {0} not found
PART_MODEL_ID_NOT_FOUND=The given part model id does not exist {0}
PART_MODEL_DO_NOT_EXISTS_WITH_PART_MODEL_NAME_AND_MFR=Part model is not exist with part model name {0} and manufacturer {0}
PART_ALREADY_ADDED_IN_VEHICLE=The given part is already added to the same vehicle {0}
PART_ALREADY_ADDED_IN_DIFFERENT_VEHICLE=The given part is already added to different vehicle {0}
PROVIDE_PART_TYPE_MOTOR=Expecting part type motor
PROVIDE_PART_TYPE_COLOR=Expecting part type color
TEST_RUNNING=Test already running for vehicle {0}
TEST_NOT_FOUND=Test not found
TEST_COMPLETED=Test already completed
PROVIDE_PARAMETERS=Expected Parameters not supplied
INCORRECT_TEST_TYPE=Test type not supported
TRIP_DETAILS_NOT_FOUND=Could not find the trip details for test id {0}
TRIP_NOT_FOUND=Trip does not exists {0}
TRIP_NOT_FOUND_FOR_FILTER=Could not find trips for selected filter
USER_ALREADY_PAIRED=user is already paired to vehicle
VEHICLE_IMEI_EMPTY=vehicle imei code is empty or null
VEHICLE_NOT_CONNECTED=user is not connected to any vehicle
LAT_OR_LONG_NULL=latitude or longitude is null
VEHICLE_NOT_FOUND_IN_ORGANISATION=vehicle not found in organisation
VEHICLE_LOCATION_FORMAT_INCORRECT=format of latitude and longitude are not correct
CURRENT_TIME_LIMIT_EXCEED=current time duration is exceed the limit
CRITICAL_BATTERY_ALERT_MESSAGE=Critical Battery Alert
CRITICAL_BATTERY_ALERT_MESSAGE_BODY=Battery Temperature critically high. Please STOP THE VEHICLE and MOVE AWAY to a safe distance immediately. Wait until the Battery cools down to {0} °C.
BATTERY_OVERHEATING_WARNING_MESSAGE=Battery Overheat Warning
BATTERY_OVERHEATING_WARNING_MESSAGE_BODY=Stop your scooter and move it to a cool, shaded area immediately.
BATTERY_WARNING_MESSAGE=Battery Warning
BATTERY_WARNING_MESSAGE_BODY=Elevated temperature detected. Please move to a cooler area and allow the battery to cool.
CHASSIS_NUMBER_ALREADY_EXISTS=Chassis number {0} already exists.
VEHICLE_ALREADY_EXISTS=Vehicle with IMEI {0} already exists.
FLEET_NOT_FOUND_IN_ORGANISATION=Fleet with id {0} is not present in the organisation
ORG_FLEET_VALIDATION=organisation with id {0} do not contain fleet with id {1}
ORG_EXISTS_FOR_PHONE_EMAIL=Organisation already exists for email {0} or phone number {1}
ORG_TYPE_VALIDATION=OrganisationType must not be empty
ORG_EXIST_IN_GRAFANA=Organisation exist in grafana
ROLE_NOT_FOUND=Role does not exists
EMAIL_VALIDATION=Email can not be empty or null
USER_DETAILS_VALIDATION=User details cannot be empty
USER_EXISTS_FOR_MAIL=user already exists with email {0}
USER_ADDED_TO_ORG=User added to organisation
COULD_NOT_ADD_USR_TO_ORG=Could not add user {0} to organisation {1}
ORG_ADDED=Organisation added successfully
COULD_NOT_ADD_ORG=Could not add organisation
URL_SLUG_LENGTH_VALIDATION=Slug length must be greater than 1
URL_SLUG_VALIDATION=Url slug must not be empty
LAST_NAME_EMPTY_VALIDATION=lastName cannot be empty
PHONE_NO_EMPTY_VALIDATION=phone number cannot be empty
FIRST_NAME_EMPTY_VALIDATION=firstName cannot be empty
LINKED_ORGANISATION_VALIDATION=Linked organisation must not be null
PART_TYPE_VALIDATION=Part type cannot be empty
PART_NAME_VALIDATION=Part name cannot be empty
VEHICLE_MODEL_NAME_VALIDATION=Vehicle model name cannot be empty
VEHICLE_MODEL_NO_VALIDATION=Vehicle model no. cannot be empty
PART_MFR_VALIDATION=Part Manufacture Name cannot be empty
VEHICLE_MFR_VALIDATION=Vehicle Manufacture Name cannot be empty
IMEI_VALIDATION=imei cannot be empty
IMEI_LIST_VALIDATION=IMEI list cannot be empty
IMEI_NULL_VALIDATION=imei cannot be null
TEST_ID_NULL_VALIDATION=test id cannot be null or empty
TEST_ID_VALIDATION=testId cannot be less than 1
RIDER_NAME_NULL_VALIDATION=riderName cannot be null
RIDER_WEIGHT_NULL_VALIDATION=riderWeigth cannot be null
TOTAL_WEIGHT_NULL_VALIDATION=total weight cannot be null
RIDER_START_PLACE_NULL_VALIDATION=rideStartPlaceName cannot be null
RIDER_END_PLACE_NULL_VALIDATION=rideEndPlaceName cannot be null
BATTERY_MFR_NULL_VALIDATION=batteryManufacturer cannot be null
VEHICLE_ID_VALIDATION=Vehicle id cannot be null or empty
INPUT_SIZE_VALIDATION=Character length must be greater than 1
IMEI_VEHICLE_MODEL_ID_VALIDATION=both the fields imei and vehicle_model_id can not be null
VEHICLE_VISIBILITY_RANGE_EXCEEDED=vehicle visibility range should be between {0}km and {1}km
DRIVE_MODE_NOT_FOUND_WITH_DRIVE_MODE_INFO=drive mode not found for drive mode {0}
B2B_ORG_NOT_FOUND=user is not belong to b2b org
B2B_OR_MANUFACTURER_ORG_NOT_FOUND=user is not belong to b2b org or manufacturer org
B2B_VEHICLE_NOT_FOUND=vehicle is not associated with b2b org
DATA_TYPE_NOT_MATCHED=provided data-type is not matched
DATA_FREQUENCY_PLAN_NOT_FOUND=Data Frequency plan {0} not found
TRIP_CONSTANT_NOT_FOUND=Trip constant {0} not found for the organisation {1}
AGGREGATE_NOT_FOUND=Aggregate not found to update the vehicleStatus
PREDICTION_MODEL_NOT_FOUND=prediction model not found for vehicle model {0}
INVALID_FULL_CAPACITY_VALUE=full capacity value {0} should be greater than 0
FULL_CAPACITY_NOT_FOUND=battery full capacity not found for vehicle {0}
INVALID_IDENTIFIER_TYPE=Invalid vehicle identifier found {0}
MULTIPLE_VEHICLE_IDENTIFIER_FOUND=Multiple vehicle identifiers detected
PARSING_PLAN_EXCEPTION=Parsing plan {0} already exists
PARSING_PLAN_NOT_FOUND=Parsing plan {0} not found
COMBO_PLAN_NOT_FOUND=Combo Plan {0} not found
VEHICLE_MODEL_NOT_PRESENT=Vehicle model with id {0} not found
VEHICLE_MODEL_SUBSCRIPTION_NOT_FOUND=Vehicle Model Subscription {0} not found
ORGANISATION_SUBSCRIPTION_NOT_FOUND=Organisation subscription {0} not found
ORGANISATION_SUBSCRIPTION_ALREADY_EXISTS=Organisation subscription already exists for organisation {0} with combo plan id {1}
VEHICLE_MODEL_SUBSCRIPTION_ALREADY_EXISTS=Vehicle model subscription already exists for vehicle model {0} with combo plan id {1}
NO_SUBSCRIPTION_PLAN_FOUND=No subscription found for vehicle model or manufacturer
ACTIVE_SUBSCRIPTION_PLAN_NOT_FOUND=Active Subscription plan not found for vehicle {0}
DATA_FREQUENCY_PLAN_NOT_PRESENT=Data frequency plan details not found
VEHICLE_TEST_DURATION_NOT_FOUND=Vehicle test duration not found
PART_HEALTH_LIMIT_NOT_FOUND=Part health limit not found
VEHICLE_RIDER_ALREADY_EXIST=vehicle rider association already exist
B2C_VEHICLE_NOT_FOUND=b2c vehicle not found
PHONE_NUMBER_MATCH_WITH_OWNER=Rider's phone number cannot match the owner's. please enter a different number.
RIDER_ACCESSED_VEHICLE_NOT_FOUND=rider accessed vehicle not found
VERIFICATION_STATUS_INVALID_WITH_INFO=given verification status invalid with status {0}
ALREADY_CONNECTED_TO_VEHICLE_WITH_INFO=user is already connected to accessed vehicle {0}
EPOCH_TIME_NULL=epoch time can't be null for {0}
PROMOTION_NOT_FOUND=promotion not found
USER_ACTIVITY_SAVED=User Activity Saved Successfully
USER_ACTIVITY_SAVED_WITH_DETAILS="{0} :user "{1}" with activity type "{2}" and value {3}
V2_ANALYTICS_INVALID_RESPONSE=Failed to connect to ev-servables for tripId: {0}
MULTIPLE_IMEI_B2C=multiple imei given for b2c
USER_ALREADY_ASSOCIATED_WITH_OTHER=user is already associated with other b2c vehicle
IMEI_LIST_EMPTY=given imei list is empty
VEHICLE_RIDER_ASSOCIATION_NOT_FOUND=vehicle to rider association not found.
VEHICLE_IDENFITIER_PATTERN_EMPTY=search parameters are empty
INVALID_PATTERN=Invalid search pattern {0}
DATA_ALREADY_EXISTS=Data is Already Exists
FLEET_SAVE_ERROR=Fleet data Already Exists is other Fleet
CHARGING_EVENT_NOT_FOUND=Charging event {0} not found
CHARGE_CONSTANT_NOT_FOUND=Charge constant {0} not found for the organisation {1}
EMERGENCY_EVENT_NOT_FOUND=Emergency Event Not Present
EMERGENCY_TYPE_NOT_FOUND=Emergency type {0} not found for organization {1}
RABBITMQ_CONNECTION_ISSUE=Unable to connect to message queue service
EMERGENCY_TYPE_NOT_PRESENT=Emergency type not present for id {0}
DATE_RANGE_REQUIRED=A valid date range is required.
VEHICLE_RUNNING_METRICS_UPDATE_ERROR=Vehicle running metrics update error for vehicle {0} with error {1}
