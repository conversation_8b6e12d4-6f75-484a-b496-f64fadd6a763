--Continuous Aggregate for battery_cell min,max,median values of cell voltages by imei,cell_id across 1 minute window
DROP MATERIALIZED VIEW IF EXISTS battery_cell_voltage_1m_aggregate;
CREATE MATERIALIZED VIEW IF NOT EXISTS battery_cell_voltage_1m_aggregate
WITH (timescaledb.continuous) AS
select imei,
    cell_id,
    mfr_org_id,
    time_bucket ('1m',timestamp) as time_bucket,
    max(cell_voltage) as max_cell_volt,
    min(cell_voltage) as min_cell_volt,
    avg(cell_voltage) as avg_cell_volt,
    round(CAST(approx_percentile(0.5, percentile_agg(cell_voltage)) AS numeric), 2) AS median_cell_volt
from battery_cell
where timestamp >'2024-01-01'
and timestamp < now()
group by imei,cell_id,mfr_org_id,time_bucket ('1m',timestamp)  WITH NO DATA;


--Refresh the continous aggregate every minute
SELECT add_continuous_aggregate_policy('battery_cell_voltage_1m_aggregate',
  start_offset => INTERVAL '1 month',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');
