--Continuous Aggregate for vehicle_telemetry_data(motor) across 1 minute window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_motor_first_aggregate_1m
WITH (timescaledb.continuous) AS
SELECT
    vtd.vehicle_id AS vehicle_id,
    vtd.imei AS imei,
    vtd.mfr_org_id AS mfr_org_id,
    vtd.owner_org_id AS owner_org_id,
    time_bucket('1m', vtd.timestamp) AS bucket,
    first(vtd.motor_brake, vtd.timestamp) FILTER (WHERE vtd.motor_brake IS NOT NULL) AS motor_brake,
    first(vtd.motor_cruise, vtd.timestamp) FILTER (WHERE vtd.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vtd.motor_dc_current, vtd.timestamp) FILTER (WHERE vtd.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vtd.motor_dc_voltage, vtd.timestamp) FILTER (WHERE vtd.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vtd.motor_mcs_temperature, vtd.timestamp) FILTER (WHERE vtd.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vtd.motor_parking_sign, vtd.timestamp) FILTER (WHERE vtd.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vtd.motor_ready_sign, vtd.timestamp) FILTER (WHERE vtd.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vtd.motor_regeneration, vtd.timestamp) FILTER (WHERE vtd.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vtd.motor_reverse, vtd.timestamp) FILTER (WHERE vtd.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vtd.motor_side_stand, vtd.timestamp) FILTER (WHERE vtd.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vtd.motor_speed, vtd.timestamp) FILTER (WHERE vtd.motor_speed IS NOT NULL) AS motor_speed,
    first(vtd.motor_temperature, vtd.timestamp) FILTER (WHERE vtd.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vtd.motor_throttle, vtd.timestamp) FILTER (WHERE vtd.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vtd.motor_driving_mode, vtd.timestamp) FILTER (WHERE vtd.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vtd.motor_fault_feedback, vtd.timestamp) FILTER (WHERE vtd.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback
FROM vehicle_telemetry_data vtd
GROUP BY bucket, vtd.vehicle_id, vtd.imei, vtd.mfr_org_id, vtd.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1m every minute
SELECT add_continuous_aggregate_policy('vehicle_motor_first_aggregate_1m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');


--Continuous Aggregate for vehicle_telemetry_data(motor) across 10 minutes window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_motor_first_aggregate_10m
WITH (timescaledb.continuous) AS
SELECT
    vmf1m.vehicle_id AS vehicle_id,
    vmf1m.imei AS imei,
    vmf1m.mfr_org_id AS mfr_org_id,
    vmf1m.owner_org_id AS owner_org_id,
    time_bucket('10m', vmf1m.bucket) AS bucket,
    first(vmf1m.motor_brake, vmf1m.bucket) FILTER (WHERE vmf1m.motor_brake IS NOT NULL) AS motor_brake,
    first(vmf1m.motor_cruise, vmf1m.bucket) FILTER (WHERE vmf1m.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vmf1m.motor_dc_current, vmf1m.bucket) FILTER (WHERE vmf1m.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vmf1m.motor_dc_voltage, vmf1m.bucket) FILTER (WHERE vmf1m.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vmf1m.motor_mcs_temperature, vmf1m.bucket) FILTER (WHERE vmf1m.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vmf1m.motor_parking_sign, vmf1m.bucket) FILTER (WHERE vmf1m.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vmf1m.motor_ready_sign, vmf1m.bucket) FILTER (WHERE vmf1m.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vmf1m.motor_regeneration, vmf1m.bucket) FILTER (WHERE vmf1m.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vmf1m.motor_reverse, vmf1m.bucket) FILTER (WHERE vmf1m.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vmf1m.motor_side_stand, vmf1m.bucket) FILTER (WHERE vmf1m.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vmf1m.motor_speed, vmf1m.bucket) FILTER (WHERE vmf1m.motor_speed IS NOT NULL) AS motor_speed,
    first(vmf1m.motor_temperature, vmf1m.bucket) FILTER (WHERE vmf1m.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vmf1m.motor_throttle, vmf1m.bucket) FILTER (WHERE vmf1m.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vmf1m.motor_driving_mode, vmf1m.bucket) FILTER (WHERE vmf1m.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vmf1m.motor_fault_feedback, vmf1m.bucket) FILTER (WHERE vmf1m.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback
FROM vehicle_motor_first_aggregate_1m vmf1m
GROUP BY time_bucket('10m', vmf1m.bucket), vmf1m.vehicle_id, vmf1m.imei, vmf1m.mfr_org_id, vmf1m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1m_1h every 10 minutes
SELECT add_continuous_aggregate_policy('vehicle_motor_first_aggregate_10m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '10 minute');


--Continuous Aggregate for vehicle_telemetry_data(motor) across 1 hour window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_motor_first_aggregate_1h
WITH (timescaledb.continuous) AS
SELECT
    vmf10m.vehicle_id AS vehicle_id,
    vmf10m.imei AS imei,
    vmf10m.mfr_org_id AS mfr_org_id,
    vmf10m.owner_org_id AS owner_org_id,
    time_bucket('1h', vmf10m.bucket) AS bucket,
    first(vmf10m.motor_brake, vmf10m.bucket) FILTER (WHERE vmf10m.motor_brake IS NOT NULL) AS motor_brake,
    first(vmf10m.motor_cruise, vmf10m.bucket) FILTER (WHERE vmf10m.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vmf10m.motor_dc_current, vmf10m.bucket) FILTER (WHERE vmf10m.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vmf10m.motor_dc_voltage, vmf10m.bucket) FILTER (WHERE vmf10m.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vmf10m.motor_mcs_temperature, vmf10m.bucket) FILTER (WHERE vmf10m.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vmf10m.motor_parking_sign, vmf10m.bucket) FILTER (WHERE vmf10m.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vmf10m.motor_ready_sign, vmf10m.bucket) FILTER (WHERE vmf10m.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vmf10m.motor_regeneration, vmf10m.bucket) FILTER (WHERE vmf10m.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vmf10m.motor_reverse, vmf10m.bucket) FILTER (WHERE vmf10m.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vmf10m.motor_side_stand, vmf10m.bucket) FILTER (WHERE vmf10m.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vmf10m.motor_speed, vmf10m.bucket) FILTER (WHERE vmf10m.motor_speed IS NOT NULL) AS motor_speed,
    first(vmf10m.motor_temperature, vmf10m.bucket) FILTER (WHERE vmf10m.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vmf10m.motor_throttle, vmf10m.bucket) FILTER (WHERE vmf10m.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vmf10m.motor_driving_mode, vmf10m.bucket) FILTER (WHERE vmf10m.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vmf10m.motor_fault_feedback, vmf10m.bucket) FILTER (WHERE vmf10m.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback
FROM vehicle_motor_first_aggregate_10m vmf10m
GROUP BY time_bucket('1h', vmf10m.bucket), vmf10m.vehicle_id, vmf10m.imei, vmf10m.mfr_org_id, vmf10m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1h every hour
SELECT add_continuous_aggregate_policy('vehicle_motor_first_aggregate_1h',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 hour');



