DO $$
DECLARE
    v_organisation_profile_id BIGINT;
    v_organisation_id BIGINT;
    v_user_id BIGINT;
    v_next_id BIGINT;
BEGIN
    -- Drop the existing constraint if it exists
    ALTER TABLE evusers.setting DROP CONSTRAINT IF EXISTS setting_name_check;

    -- Add the new constraint with updated allowed values
    ALTER TABLE evusers.setting
    ADD CONSTRAINT setting_name_check CHECK (
        name = ANY (
            ARRAY[
                'PRIVACY_POLICY'::character varying::text,
                'TERMS_AND_CONDITION'::character varying::text,
                'SMS_PROVIDER'::character varying::text,
                'B2B_TEMPLATE_ID'::character varying::text,
                'B2C_TEMPLATE_ID'::character varying::text,
                'WEB_APP_TEMPLATE_ID'::character varying::text,
                'DEFAULT_TEMPLATE_ID'::character varying::text,
                'MSG91_API_URL'::character varying::text,
                'MSG91_AUTH_KEY'::character varying::text,
                'REGISTRATION_SMS'::character varying::text -- New entry added here
            ]
        )
    );

    -- Step 1: Fetch organisation_profile_id from organisation_profiles based on email
    SELECT op.id
    INTO v_organisation_profile_id
    FROM evusers.organisation_profiles op
    WHERE op.email = '<EMAIL>';

    -- Proceed only if organisation_profile_id exists
    IF v_organisation_profile_id IS NOT NULL THEN
        -- Step 2: Fetch organisation_id based on organisation_profile_id
        SELECT o.id
        INTO v_organisation_id
        FROM evusers.organisations o
        WHERE o.organisation_profile_id = v_organisation_profile_id;

        -- Step 3: Fetch user_id from users table based on email
        SELECT u.id
        INTO v_user_id
        FROM evusers.users u
        WHERE u.email = '<EMAIL>';

        -- Proceed only if organisation_id and user_id exist
        IF v_organisation_id IS NOT NULL AND v_user_id IS NOT NULL THEN
            -- Step 4: Get next value for id if id column is not auto-generated
            v_next_id := nextval('evusers.setting_seq'); -- Replace with your sequence name if different

            -- Insert new record into Setting table with generated id
            INSERT INTO evusers.setting (id, name, value, type, message, last_modified_date, last_modified_by, organisation_id)
            VALUES (v_next_id,
                    'REGISTRATION_SMS',
                    'Registration successfully completed, Welcome!',
                    'String',
                    'Registration successfully completed, Welcome!',
                    NOW(),
                    v_user_id,
                    v_organisation_id);
        END IF;
    END IF;
END $$;
