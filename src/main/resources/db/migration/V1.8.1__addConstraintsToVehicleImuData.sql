-- create constraints
ALTER TABLE evdata.vehicle_imu_data
ADD CONSTRAINT vehicle_imu_data_pk PRIMARY KEY (timestamp, imei);

ALTER TABLE evdata.vehicle_imu_data
ADD CONSTRAINT vehicle_imu_data_mfr_org_fk FOREIGN KEY (mfr_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_imu_data
ADD CONSTRAINT vehicle_imu_data_owner_org_fk FOREIGN KEY (owner_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_imu_data
ADD CONSTRAINT vehicle_imu_data_vehicle_fk FOREIGN KEY (vehicle_id)
REFERENCES evdata.vehicle (id);



CREATE INDEX vehicle_imu_data_imei_idx ON evdata.vehicle_imu_data (imei);
CREATE INDEX vehicle_imu_data_id_idx ON evdata.vehicle_imu_data (imei, timestamp);
CREATE INDEX vehicle_imu_data_ts_idx ON evdata.vehicle_imu_data (timestamp);


SELECT create_hypertable('vehicle_imu_data', 'timestamp', migrate_data => true);