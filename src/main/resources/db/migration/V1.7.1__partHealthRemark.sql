CREATE SEQUENCE IF NOT EXISTS  part_type_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS part_health_remark_id_seq START WITH 1 INCREMENT BY 1;


CREATE TABLE IF NOT EXISTS part_type (
    id BIGINT PRIMARY KEY DEFAULT nextval('part_type_id_seq'),
    part_name VARCHAR(255) NOT NULL
);


CREATE TABLE IF NOT EXISTS part_health_remark (
    id BIGINT PRIMARY KEY DEFAULT nextval('part_health_remark_id_seq'),
    part_type_id BIGINT NOT NULL,
    remark VARCHAR(255) NOT NULL,
    value INTEGER NOT NULL,
    FOREIGN KEY (part_type_id) REFERENCES part_type(id)
);

INSERT INTO part_type (part_name) VALUES
    ('MOTOR'),
    ('BATTERY'),
    ('REAR_TYRE');

    INSERT INTO part_health_remark (part_type_id, remark, value) VALUES
        ((SELECT id FROM part_type WHERE part_name = 'MOTOR'), 'EXCELLENT', 0),
        ((SELECT id FROM part_type WHERE part_name = 'MOTOR'), 'GOOD', 1),
        ((SELECT id FROM part_type WHERE part_name = 'MOTOR'), 'AVERAGE', 2),
        ((SELECT id FROM part_type WHERE part_name = 'MOTOR'), 'POOR', 3);

    -- Insert remark mappings for BATTERY
    INSERT INTO part_health_remark (part_type_id, remark, value) VALUES
        ((SELECT id FROM part_type WHERE part_name = 'BATTERY'), 'EXCELLENT', 0),
        ((SELECT id FROM part_type WHERE part_name = 'BATTERY'), 'GOOD', 1),
        ((SELECT id FROM part_type WHERE part_name = 'BATTERY'), 'AVERAGE', 2),
        ((SELECT id FROM part_type WHERE part_name = 'BATTERY'), 'POOR', 3);

    -- Insert remark mappings for TYRE
    INSERT INTO part_health_remark (part_type_id, remark, value) VALUES
        ((SELECT id FROM part_type WHERE part_name = 'REAR_TYRE'), 'LOW', 0),
        ((SELECT id FROM part_type WHERE part_name = 'REAR_TYRE'), 'NORMAL', 1),
        ((SELECT id FROM part_type WHERE part_name = 'REAR_TYRE'), 'HIGH', 2);

