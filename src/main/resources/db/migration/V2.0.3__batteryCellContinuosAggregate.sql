-- drop materialized view before drop
DROP MATERIALIZED VIEW battery_cell_voltage_1m_aggregate;

CREATE MATERIALIZED VIEW IF NOT EXISTS battery_cell_voltage_1m_aggregate
WITH (timescaledb.continuous) AS
SELECT
    bc.imei AS imei,
    bc.vehicle_id AS vehicle_id,
    bc.cell_id AS cell_id,
    bc.mfr_org_id AS mfr_org_id,
    time_bucket('1m', bc.timestamp) AS bucket,
    MAX(bc.cell_voltage) AS max_cell_volt,
    MIN(bc.cell_voltage) AS min_cell_volt,
    AVG(bc.cell_voltage) AS avg_cell_volt,
    ROUND(CAST(approx_percentile(0.5, percentile_agg(bc.cell_voltage)) AS numeric), 2) AS median_cell_volt,
    FIRST(bc.cell_voltage, bc.timestamp)
        FILTER (WHERE bc.cell_voltage IS NOT NULL) AS cell_voltage
FROM battery_cell AS bc
GROUP BY
    bc.imei,
    bc.vehicle_id,
    bc.cell_id,
    bc.mfr_org_id,
    time_bucket('1m', bc.timestamp)
WITH NO DATA;


SELECT add_continuous_aggregate_policy('battery_cell_voltage_1m_aggregate',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');


CREATE MATERIALIZED VIEW IF NOT EXISTS battery_cell_voltage_10m_aggregate
WITH (timescaledb.continuous) AS
SELECT
    bc1m.imei AS imei,
    bc1m.vehicle_id AS vehicle_id,
    bc1m.cell_id AS cell_id,
    bc1m.mfr_org_id AS mfr_org_id,
    time_bucket('10m', bc1m.bucket) AS bucket,
    MAX(bc1m.max_cell_volt) AS max_cell_volt,
    MIN(bc1m.min_cell_volt) AS min_cell_volt,
    AVG(bc1m.avg_cell_volt) AS avg_cell_volt,
    ROUND(CAST(approx_percentile(0.5, percentile_agg(bc1m.median_cell_volt)) AS numeric), 2) AS median_cell_volt,
    FIRST(bc1m.cell_voltage, bc1m.bucket) FILTER (WHERE bc1m.cell_voltage IS NOT NULL) AS cell_voltage
FROM battery_cell_voltage_1m_aggregate bc1m
GROUP BY
    bc1m.imei,
    bc1m.vehicle_id,
    bc1m.cell_id,
    bc1m.mfr_org_id,
    time_bucket('10m', bc1m.bucket)
WITH NO DATA;

SELECT add_continuous_aggregate_policy('battery_cell_voltage_10m_aggregate',
   start_offset => INTERVAL '1 day',
   end_offset => INTERVAL '1 minute',
   schedule_interval => INTERVAL '10 minute');


CREATE MATERIALIZED VIEW IF NOT EXISTS battery_cell_voltage_1h_aggregate
WITH (timescaledb.continuous) AS
SELECT
    bc10m.imei AS imei,
    bc10m.vehicle_id AS vehicle_id,
    bc10m.cell_id AS cell_id,
    bc10m.mfr_org_id AS mfr_org_id,
    time_bucket('1 hour', bc10m.bucket) AS bucket,
    MAX(bc10m.max_cell_volt) AS max_cell_volt,
    MIN(bc10m.min_cell_volt) AS min_cell_volt,
    AVG(bc10m.avg_cell_volt) AS avg_cell_volt,
    ROUND(CAST(approx_percentile(0.5, percentile_agg(bc10m.median_cell_volt)) AS numeric), 2) AS median_cell_volt,
    FIRST(bc10m.cell_voltage, bc10m.bucket) FILTER (WHERE bc10m.cell_voltage IS NOT NULL) AS cell_voltage
FROM battery_cell_voltage_10m_aggregate bc10m
GROUP BY
    bc10m.imei,
    bc10m.vehicle_id,
    bc10m.cell_id,
    bc10m.mfr_org_id,
    time_bucket('1 hour', bc10m.bucket)
WITH NO DATA;

SELECT add_continuous_aggregate_policy('battery_cell_voltage_1h_aggregate',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 hour');
