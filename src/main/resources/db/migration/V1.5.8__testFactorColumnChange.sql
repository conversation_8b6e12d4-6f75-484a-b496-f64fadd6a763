-- Insert new rows into the test_factor table
INSERT INTO test_factor (test_type, test_condition) VALUES
    ('CONNECTIVITY', 'AI_VOLTAGE_IN_RANGE'),
    ('CONNECTIVITY', 'BATTERY_CURRENT_IN_RANGE'),
    ('CONNECTIVITY', 'BATTERY_VOLTAGE_IN_RANGE');

-- Insert into test_factor_limit table
INSERT INTO test_factor_limit (test_factor_id, range_label, min_value, max_value, data_type) VALUES
          ((SELECT id FROM test_factor WHERE test_condition = 'BATTERY_CURRENT_IN_RANGE'), 'UPPER_LIMIT', 100, 1000, 'NUMBER'),
          ((SELECT id FROM test_factor WHERE test_condition = 'BATTERY_CURRENT_IN_RANGE'), 'LOWER_LIMIT', -1000, 0, 'NUMBER'),
          ((SELECT id FROM test_factor WHERE test_condition = 'BATTERY_CURRENT_IN_RANGE'), 'IN_LIMIT', 0, 100, 'NUMBER'),
          ((SELECT id FROM test_factor WHERE test_condition = 'BATTERY_VOLTAGE_IN_RANGE'), 'UPPER_LIMIT', 84, 1000, 'NUMBER'),
          ((SELECT id FROM test_factor WHERE test_condition = 'BATTERY_VOLTAGE_IN_RANGE'), 'LOWER_LIMIT', 0, 64, 'NUMBER'),
          ((SELECT id FROM test_factor WHERE test_condition = 'BATTERY_VOLTAGE_IN_RANGE'), 'IN_LIMIT', 64, 84, 'NUMBER'),
          ((SELECT id FROM test_factor WHERE test_condition = 'AI_VOLTAGE_IN_RANGE'), 'UPPER_LIMIT', 84, 1000, 'NUMBER'),
          ((SELECT id FROM test_factor WHERE test_condition = 'AI_VOLTAGE_IN_RANGE'), 'LOWER_LIMIT', 0, 64, 'NUMBER'),
          ((SELECT id FROM test_factor WHERE test_condition = 'AI_VOLTAGE_IN_RANGE'), 'IN_LIMIT', 64, 84, 'NUMBER');

-- Rename column 'code_module' to 'test_category'
ALTER TABLE test_factor
    RENAME COLUMN code_module TO test_category;

-- Add new column 'unit' to the table
ALTER TABLE test_factor
    ADD COLUMN IF NOT EXISTS unit VARCHAR(255);

UPDATE test_factor
SET
    test_category = 'AVAILABILITY',
    unit = '%'
WHERE test_type = 'CONNECTIVITY'
  AND test_condition IN (
      'OVERALL_DATA_AVAILABILITY',
      'TCU_DATA_AVAILABILITY',
      'MOTOR_DATA_AVAILABILITY',
      'LOCATION_DATA_AVAILABILITY',
      'BATTERY_DATA_AVAILABILITY'
  );

UPDATE test_factor
SET
    test_category = 'RANGE',
    unit = 'A'
WHERE test_type = 'CONNECTIVITY'
  AND test_condition = 'MOTOR_CURRENT_IN_RANGE';

UPDATE test_factor
SET
    test_category = 'RANGE',
    unit = 'A'
WHERE test_type = 'CONNECTIVITY'
  AND test_condition = 'BATTERY_CURRENT_IN_RANGE';

UPDATE test_factor
SET
    test_category = 'RANGE',
    unit = 'V'
WHERE test_type = 'CONNECTIVITY'
  AND test_condition = 'BATTERY_VOLTAGE_IN_RANGE';

UPDATE test_factor
SET
    test_category = 'RANGE',
    unit = 'V'
WHERE test_type = 'CONNECTIVITY'
  AND test_condition = 'AI_VOLTAGE_IN_RANGE';

UPDATE test_factor
SET
    test_category = 'RANGE',
    unit = 'V'
WHERE test_type = 'CONNECTIVITY'
  AND test_condition = 'MOTOR_VOLTAGE_IN_RANGE';

UPDATE test_factor
SET
    test_category = 'SINGLE_VALUE',
    unit = '%'
WHERE test_type = 'CONNECTIVITY'
  AND test_condition = 'LATEST_SOC';

UPDATE test_factor
SET
    test_category = 'SINGLE_VALUE',
    unit = 's'
WHERE test_type = 'CONNECTIVITY'
  AND test_condition = 'DATA_LAG';


CREATE INDEX vehicle_test_test_type_idx ON vehicle_test (test_type);
CREATE INDEX vehicle_test_status_idx ON vehicle_test (status);
CREATE INDEX idx_test_factor_test_category ON test_factor (test_category);
CREATE INDEX test_factor_limit_test_factor_idx ON test_factor_limit (test_factor_id);




