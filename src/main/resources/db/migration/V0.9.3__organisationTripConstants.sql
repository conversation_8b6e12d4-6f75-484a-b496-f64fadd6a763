CREATE SEQUENCE IF NOT EXISTS trip_duration_constants_seq start with 1 increment by 50;

CREATE TABLE IF NOT EXISTS trip_duration_constants(
    id bigint PRIMARY KEY DEFAULT nextval('trip_duration_constants_seq'),
    trip_constant character varying(255),
    duration int NOT NULL,
	unit character varying(255),
    org_id bigint NOT NULL,
    FOREIGN KEY (org_id) REFERENCES organisations(id),
	CONSTRAINT uk_trip_constant_org_id UNIQUE (trip_constant, org_id),
	CONSTRAINT trip_constant_check CHECK (trip_constant::text = ANY (ARRAY['STOP_DURATION'::character varying::text, 'MIN_TRIP_DURATION'::character varying::text]))
);