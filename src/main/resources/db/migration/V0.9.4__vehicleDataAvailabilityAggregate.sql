--vehicle_telemetry_data_availability_aggregate_daily
CREATE MATERIALIZED VIEW vehicle_telemetry_data_availability_aggregate_daily
WITH (timescaledb.continuous) AS
SELECT
    vehicle_id,
    imei,
    time_bucket('1 day', timestamp) AS day,
    COUNT(*) AS telemetry_data_availability,
    COUNT(CASE WHEN motor_ready_sign IS NOT NULL THEN 1 END) AS motor_data_availability
FROM
    vehicle_telemetry_data
GROUP BY
    vehicle_id,
    imei,
    day
WITH NO DATA;

CREATE INDEX telemetry_availability_idx_imei ON vehicle_telemetry_data_availability_aggregate_daily (imei);
CREATE INDEX telemetry_availability_idx_vehicle_id ON vehicle_telemetry_data_availability_aggregate_daily (vehicle_id);
CREATE INDEX telemetry_availability_idx_day_imei_telemetry ON vehicle_telemetry_data_availability_aggregate_daily (day, imei, telemetry_data_availability);
CREATE INDEX telemetry_availability_idx_day_imei_motor ON vehicle_telemetry_data_availability_aggregate_daily (day, imei, motor_data_availability);

SELECT add_continuous_aggregate_policy('vehicle_telemetry_data_availability_aggregate_daily',
    start_offset => INTERVAL '2 day',
    end_offset => INTERVAL '0 day',
    schedule_interval => INTERVAL '5 minutes');

--vehicle_battery_data_availability_aggregate_daily
CREATE MATERIALIZED VIEW vehicle_battery_data_availability_aggregate_daily
WITH (timescaledb.continuous) AS
SELECT
    vehicle_id,
    imei,
    time_bucket('1 day', timestamp) AS day,
    COUNT(*) AS battery_data_availability
FROM
    vehicle_battery_data
GROUP BY
    vehicle_id,
    imei,
    day
WITH NO DATA;

CREATE INDEX battery_availability_idx_imei ON vehicle_battery_data_availability_aggregate_daily (imei);
CREATE INDEX battery_availability_idx_vehicle_id ON vehicle_battery_data_availability_aggregate_daily (vehicle_id);
CREATE INDEX battery_availability_idx_day_imei_telemetry ON vehicle_battery_data_availability_aggregate_daily (day, imei, battery_data_availability);

SELECT add_continuous_aggregate_policy('vehicle_battery_data_availability_aggregate_daily',
    start_offset => INTERVAL '2 day',
    end_offset => INTERVAL '0 day',
    schedule_interval => INTERVAL '5 minutes');

--vehicle_location_data_availability_aggregate_daily
CREATE MATERIALIZED VIEW vehicle_location_data_availability_aggregate_daily
WITH (timescaledb.continuous) AS
SELECT
    vehicle_id,
    imei,
    time_bucket('1 day', timestamp) AS day,
    COUNT(*) AS location_data_availability
FROM
    vehicle_location_data
GROUP BY
    vehicle_id,
    imei,
    day
WITH NO DATA;

CREATE INDEX location_availability_idx_imei ON vehicle_location_data_availability_aggregate_daily (imei);
CREATE INDEX location_availability_idx_vehicle_id ON vehicle_location_data_availability_aggregate_daily (vehicle_id);
CREATE INDEX location_availability_idx_day_imei_telemetry ON vehicle_location_data_availability_aggregate_daily (day, imei, location_data_availability);

SELECT add_continuous_aggregate_policy('vehicle_location_data_availability_aggregate_daily',
    start_offset => INTERVAL '2 day',
    end_offset => INTERVAL '0 day',
    schedule_interval => INTERVAL '5 minutes');