-- alter vehicle event monitor to add charging event start time and counter
ALTER TABLE vehicle_event_monitor add column charging_time bigint,add column charging_time_last_updated_on timestamp with time zone;

CREATE SEQUENCE IF NOT EXISTS charging_event_seq START WITH 1 INCREMENT BY 1;

-- create charging event table
CREATE TABLE charging_event (
    id BIGINT PRIMARY KEY DEFAULT nextval('charging_event_seq'),
    user_id BIGINT,
    imei <PERSON>RCHAR(20),
    vehicle_id BIGINT NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(255),
    owner_org_id BIGINT,
    mfr_org_id BIGINT,
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_source VARCHAR(255),
    location geography(PointZM,4326),
    neighbourhood VARCHAR(255),
    suburb VARCHAR(255),
    city VARCHAR(255),
    state VARCHAR(255),
    charge_type VARCHAR(255),
    CONSTRAINT fk_user_id FOREIGN KEY (user_id) REFERENCES evusers.users(id),
    CONSTRAINT fk_vehicle_id FOREIGN KEY (vehicle_id) REFERENCES evdata.vehicle(id),
    CONSTRAINT fk_owner_org_id FOREIGN KEY (owner_org_id) REFERENCES evusers.organisations(id),
    CONSTRAINT fk_mfr_org_id FOREIGN KEY (mfr_org_id) REFERENCES evusers.organisations(id),
    CONSTRAINT chk_charging_status CHECK (status IN ('IN_PROGRESS', 'COMPLETED', 'FAILED')),
    CONSTRAINT chk_charge_type CHECK (charge_type IN ('FAST','SLOW','NORMAL','EMERGENCY','WIRELESS','BATTERY_SWAP'))
);

CREATE INDEX charging_event_search_idx ON charging_event(vehicle_id,status,imei,charge_type,start_time);