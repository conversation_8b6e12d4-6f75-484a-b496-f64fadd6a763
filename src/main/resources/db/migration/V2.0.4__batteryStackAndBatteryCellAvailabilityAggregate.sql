-- Battery Stack Data Availability Daily Aggregate
CREATE MATERIALIZED VIEW evdata.battery_stack_data_availability_aggregate_daily
  WITH (timescaledb.continuous) AS
  SELECT
      vehicle_id,
      imei,
      time_bucket('1 day', timestamp) AS day,
      COUNT(*) AS stack_data_availability
  FROM
      evdata.battery_stack
  GROUP BY
      vehicle_id,
      imei,
      day
  WITH NO DATA;

CREATE INDEX stack_availability_idx_vehicle_id
ON evdata.battery_stack_data_availability_aggregate_daily (vehicle_id);

CREATE INDEX stack_availability_idx_day_imei_telemetry
ON evdata.battery_stack_data_availability_aggregate_daily (day, imei, stack_data_availability);

SELECT add_continuous_aggregate_policy('evdata.battery_stack_data_availability_aggregate_daily',
    start_offset => INTERVAL '2 day',
    end_offset => INTERVAL '0 day',
    schedule_interval => INTERVAL '5 minutes');



-- Battery Cell Data Availability Daily Aggregate
CREATE MATERIALIZED VIEW evdata.battery_cell_data_availability_aggregate_daily
  WITH (timescaledb.continuous) AS
  SELECT
      vehicle_id,
      imei,
      time_bucket('1 day', timestamp) AS day,
      COUNT(*) AS cell_data_availability
  FROM
      evdata.battery_cell
  GROUP BY
      vehicle_id,
      imei,
      day
  WITH NO DATA;

CREATE INDEX cell_availability_idx_vehicle_id
ON evdata.battery_cell_data_availability_aggregate_daily (vehicle_id);

CREATE INDEX cell_availability_idx_day_imei_telemetry
ON evdata.battery_cell_data_availability_aggregate_daily (day, imei, cell_data_availability);

SELECT add_continuous_aggregate_policy('evdata.battery_cell_data_availability_aggregate_daily',
    start_offset => INTERVAL '2 day',
    end_offset => INTERVAL '0 day',
    schedule_interval => INTERVAL '5 minutes');