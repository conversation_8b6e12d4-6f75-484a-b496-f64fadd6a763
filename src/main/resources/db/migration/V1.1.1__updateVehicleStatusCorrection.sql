alter table data_frequency_plan_details
drop constraint if exists feature_name_check;
update data_frequency_plan_details set feature_name='VEHICLE_STATUS_UPDATION' where feature_name='VEHICLE_STATUS_UPDATE';
alter table data_frequency_plan_details
add CONSTRAINT feature_name_check CHECK (feature_name::text = ANY (ARRAY['VEHICLE_STATUS'::character varying::text, 'VEHICLE_STATUS_UPDATION'::character varying::text]));


alter table vehicle_status
drop constraint if exists update_source_check;
update vehicle_status
set update_source = 'VEHICLE_STATUS_CRON' where update_source='STATUS_CRON';
update vehicle_status
set update_source = 'VEHICLE_STATUS_DATA_DELAY_CRON' where update_source='DATA_DELAY_CRON';
alter table vehicle_status
add CONSTRAINT update_source_check CHECK (update_source::text = ANY (ARRAY['VEHICLE_STATUS_CRON'::character varying::text, 'VEHICLE_STATUS_DATA_DELAY_CRON'::character varying::text]));


