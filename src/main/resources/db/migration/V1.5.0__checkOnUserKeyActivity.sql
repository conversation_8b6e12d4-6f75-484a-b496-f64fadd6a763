-- Drop the existing constraint if it exists
ALTER TABLE user_key_activity
DROP CONSTRAINT IF EXISTS valid_activity_type;

-- Add the constraint again
ALTER TABLE user_key_activity
ADD CONSTRAINT valid_activity_type
CHECK (
    activity_type IN (
        'PRIVACY_POLICY_ACCEPTANCE',
        'TERMS_CONDITIONS_ACCEPTANCE',
        'LOGI<PERSON>',
        'LOGOUT',
        'RIDER_INVITE_SENT',
        'RIDER_INVITE_VERIFIED',
        'RIDER_INVITE_REJECTED',
        'RIDER_PERMISSION_GRANTED',
        'RIDER_PERMISSION_REVOKED',
        'RIDER_PERMISSION_REMOVED',
        'RIDER_VEHICLE_CONNECTED',
        'RIDER_VEHICLE_DISCONNECTED'
    )
);
