-- Create sequence for emergency_event table
CREATE SEQUENCE IF NOT EXISTS evdata.emergency_event_id_seq START 1 INCREMENT 1;

-- Create emergency_event table
CREATE TABLE IF NOT EXISTS evdata.emergency_event (
    id BIGINT PRIMARY KEY DEFAULT nextval('evdata.emergency_event_id_seq'),
    vehicle_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    mfr_org_id BIGINT NOT NULL,
    emergency_type_id BIGINT NOT NULL,
    vehicle_last_location GEOGRAPHY(POINTZM, 4326),
    user_location GEOGRAPHY(POINTZM, 4326),
    text VARCHAR(500),
    status VARCHAR(50) NOT NULL,
    registered_on TIMESTAMP(6) WITH TIME ZONE,
    fixed_on TIMESTAMP(6) WITH TIME ZONE,
    
    CONSTRAINT fk_emergency_event_vehicle_id FOREIGN KEY (vehicle_id) REFERENCES evdata.vehicle(id),
    CONSTRAINT fk_emergency_event_user_id FOREI<PERSON><PERSON>EY (user_id) REFERENCES evusers.users(id),
    CONSTRAINT fk_emergency_event_mfr_org_id FOREIGN KEY (mfr_org_id) REFERENCES evusers.organisations(id),
    CONSTRAINT fk_emergency_event_emergency_type_id FOREIGN KEY (emergency_type_id) REFERENCES evdata.emergency_type(id),
    CONSTRAINT chk_emergency_event_status CHECK (status IN ('REGISTERED', 'ASSIGNED', 'IN_PROGRESS', 'FIXED', 'CANCELLED'))
);

CREATE SEQUENCE IF NOT EXISTS evdata.emergency_event_assignment_history_id_seq START 1 INCREMENT 1;

-- Create emergency_event_assignment_history table
CREATE TABLE IF NOT EXISTS evdata.emergency_event_assignment_history (
    id BIGINT PRIMARY KEY DEFAULT nextval('evdata.emergency_event_assignment_history_id_seq'),
    emergency_event_id BIGINT NOT NULL,
    assigned_to BIGINT NOT NULL,
    assigned_by BIGINT NOT NULL,
    assigned_on TIMESTAMP(6) WITH TIME ZONE,
    comment VARCHAR(500),
    
    CONSTRAINT fk_assignment_history_emergency_event_id FOREIGN KEY (emergency_event_id) REFERENCES evdata.emergency_event(id),
    CONSTRAINT fk_assignment_history_assigned_to FOREIGN KEY (assigned_to) REFERENCES evusers.users(id),
    CONSTRAINT fk_assignment_history_assigned_by FOREIGN KEY (assigned_by) REFERENCES evusers.users(id)
);
