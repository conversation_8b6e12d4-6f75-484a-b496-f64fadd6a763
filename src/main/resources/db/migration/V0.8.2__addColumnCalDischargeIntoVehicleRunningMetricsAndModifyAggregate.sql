--adding extra column cal_discharge to vehicle_running_metrics table
ALTER TABLE vehicle_running_metrics
add column cal_discharge real;

DROP MATERIALIZED VIEW IF EXISTS vehicle_running_metrics_aggregate;

--creating MATERIALIZED VIEW for vehicle_running_metrics
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_running_metrics_aggregate
WITH (timescaledb.continuous) AS
select
vrm.imei as imei,
time_bucket ('10s',vrm.timestamp) as time_bucket,
mode() within group (order by vrm.distance_travelled) as distance_travelled,
mode() within group (order by vrm.cal_soc) as cal_soc,
mode() within group (order by vrm.soc) as soc,
mode() within group (order by vrm.discharge) as discharge,
mode() within group (order by vrm.cal_discharge) as cal_discharge,
AVG(vrm.avg_speed) as avg_speed,
MAX(vrm.max_speed) as max_speed,
mode() within group (order by vrm.drive_mode_id) as drive_mode_id
from vehicle_running_metrics vrm
where vrm.timestamp > '2024-01-01'
group by vrm.imei,time_bucket,time_bucket('10s',vrm.timestamp) WITH NO DATA;


--adding policy with taking start_offset from 2024-01-01 00:00:00
DO $$
DECLARE
    fixed_start_date TIMESTAMP WITH TIME ZONE := '2024-01-01 00:00:00'::TIMESTAMP WITH TIME ZONE;
BEGIN
    EXECUTE format(
        'SELECT add_continuous_aggregate_policy(''%I'', start_offset => INTERVAL ''%s'', end_offset => INTERVAL ''0 seconds'', schedule_interval => INTERVAL ''1 day'')',
        'vehicle_running_metrics_aggregate',
        now() AT TIME ZONE 'UTC' - fixed_start_date AT TIME ZONE 'UTC'
    );
END $$;

--removing policy
SELECT remove_continuous_aggregate_policy('vehicle_running_metrics_aggregate');

--again adding policy with refresh view at midnight
SELECT add_continuous_aggregate_policy(
    'vehicle_running_metrics_aggregate',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '0 seconds',
    schedule_interval => INTERVAL '1 day',
    initial_start => now() + interval '1 day' - (now() - date_trunc('day', now()))
);


