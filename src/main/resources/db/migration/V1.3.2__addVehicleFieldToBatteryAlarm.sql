alter table battery_alarm
add column vehicle_id bigint,
add column owner_org_id bigint,
add column mfr_org_id bigint,
add constraint battery_alarm_org_fk foreign key (owner_org_id) references organisations (id) ,
add constraint battery_alarm_veh_fk foreign key (vehicle_id) references vehicle (id) ,
add constraint battery_alarm_imei_fk foreign key (imei) references vehicle (imei) ;


alter table battery_alarm_error
add column vehicle_id bigint,
add column owner_org_id bigint,
add column mfr_org_id bigint,
add constraint battery_alarm_error_org_fk foreign key (owner_org_id) references organisations (id) ,
add constraint battery_alarm_error_veh_fk foreign key (vehicle_id) references vehicle (id) ,
add constraint battery_alarm_error_imei_fk foreign key (imei) references vehicle (imei) ;

DROP INDEX IF EXISTS battery_alarm_timestamp_idx;
CREATE INDEX battery_alarm_timestamp_idx on battery_alarm using BRIN (timestamp);
create index if not exists battery_alarm_org_idx on battery_alarm(owner_org_id);
create index if not exists battery_alarm_imei_idx on battery_alarm(imei);
create index if not exists battery_alarm_veh_idx on battery_alarm(vehicle_id);

CREATE INDEX IF NOT EXISTS battery_alarm_timestamp_error_idx on battery_alarm_error using BRIN (timestamp);
create index if not exists battery_alarm_error_org_idx on battery_alarm_error(owner_org_id);
create index if not exists battery_alarm_error_imei_idx on battery_alarm_error(imei);
create index if not exists battery_alarm_error_veh_idx on battery_alarm_error(vehicle_id);


--  Update the vehicle_id column based on matching imei in battery_alarm and vehicle tables
DO $$
DECLARE
    start_date TIMESTAMP := '2024-01-01 00:00:00';
    end_date TIMESTAMP;
BEGIN
    WHILE start_date < CURRENT_TIMESTAMP LOOP
        end_date := start_date + INTERVAL '5 days';

        -- Run the update for the current 5-day batch within a transaction
        BEGIN
            -- Start a new transaction for each batch
            UPDATE battery_alarm er
            SET vehicle_id = v.id ,owner_org_id =v.owner_org_id,mfr_org_id=v.mfr_org_id
            FROM vehicle v
            WHERE er.imei = v.imei
            AND er."timestamp" >= start_date
            AND er."timestamp" < end_date;
        EXCEPTION
            WHEN OTHERS THEN
                -- Optionally, log or raise the error
                RAISE NOTICE 'Error in batch: % to %, rolled back.', start_date, end_date;
        END;

        -- Move to the next batch
        start_date := end_date;
    END LOOP;
END $$;
