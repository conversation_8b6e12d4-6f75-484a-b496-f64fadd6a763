-- Step 1: Delete from vehicle_test_details
DELETE FROM vehicle_test_details
WHERE vehicle_test_id IN (
    SELECT id
    FROM vehicle_test
    WHERE test_type = 'CONNECTIVITY'
    AND status = 'COMPLETED'
    AND start_time > (SELECT timestamp FROM vehicle_telemetry_data ORDER BY timestamp ASC LIMIT 1)
);

-- Step 2: Delete from connectivity_test_summary
DELETE FROM connectivity_test_summary
WHERE vehicle_test_id IN (
    SELECT id
    FROM vehicle_test
    WHERE test_type = 'CONNECTIVITY'
    AND status = 'COMPLETED'
    AND start_time > (SELECT timestamp FROM vehicle_telemetry_data ORDER BY timestamp ASC LIMIT 1)
);

-- Step 3: Update vehicle_test status
UPDATE vehicle_test
SET status = 'RUNNING'
WHERE test_type = 'CONNECTIVITY'
AND status = 'COMPLETED'
AND start_time > (SELECT timestamp FROM vehicle_telemetry_data ORDER BY timestamp ASC LIMIT 1);