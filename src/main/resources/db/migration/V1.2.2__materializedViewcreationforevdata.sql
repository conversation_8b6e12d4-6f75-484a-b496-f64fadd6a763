CREATE MATERIALIZED VIEW evdata.view_vehicle_model_part_model_attribute AS
 SELECT concat(vm.model_no, '.', pm.mfr_org_id, '.', pm.part_type, '.', pm.id) AS vehicle_model_org_part_type_part_model_id,
    pmd.name AS attr_name,
    pmd.value AS attr_value
   FROM ((((evdata.vehicle_model vm
     JOIN evdata.vehicle_model_parts vmp ON ((vm.id = vmp.model_id)))
     JOIN evdata.part_model pm ON ((pm.id = vmp.part_model_id)))
     JOIN evdata.part_model_attributes pma ON ((pm.id = pma.part_model_id)))
     JOIN evdata.part_model_details pmd ON ((pmd.id = pma.part_model_attribute_id)));


CREATE MATERIALIZED VIEW evdata.view_vehicle_model_drive_mode_dyno_test_pass_percentage AS
 SELECT pm.id AS part_model_id,
    pm.name AS part_model_name,
    dm.name,
    pm.mfr_org_id AS org_id,
    round((((count(dydmd.speed_test_result) FILTER (WHERE ((dydmd.speed_test_result)::text = 'PASS'::text)) * 100))::numeric / (( SELECT count(*) AS count
           FROM evdata.vehicle_test v_test
          WHERE (((v_test.test_type)::text = 'DYNO'::text) AND (v_test.mfr_org_id = pm.mfr_org_id))))::numeric), 2) AS pass_percentage
   FROM (((((evdata.dyno_test_summary_drive_mode_details dydmd
     JOIN evdata.vehicle_test vt ON ((dydmd.vehicle_test_id = vt.id)))
     JOIN evdata.vehicle v ON ((vt.vehicle_id = v.id)))
     JOIN evdata.vehicle_model vm ON ((vm.id = v.vehicle_model_id)))
     JOIN evdata.part_model pm ON ((vm.id = pm.id)))
     JOIN evdata.drive_mode dm ON ((dydmd.drive_mode_id = dm.id)))
  GROUP BY pm.name, dm.name, pm.id, pm.mfr_org_id;




CREATE MATERIALIZED VIEW evdata.view_vehicle_daily_usage AS
 SELECT vrm.date,
    vrm.imei,
    vrm.range,
    vrm.usage_time,
    vrm.distance,
    vs.charging_time
   FROM (( SELECT date(vrm_1."timestamp") AS date,
            vrm_1.imei,
            round((sum(vrm_1.distance_travelled))::numeric, 2) AS distance,
            round(((sum(vrm_1.distance_travelled) / NULLIF(sum(
                CASE
                    WHEN (vrm_1.discharge > (0)::double precision) THEN vrm_1.discharge
                    ELSE NULL::real
                END), (0)::double precision)))::numeric, 2) AS range,
            (count(*) * 10) AS usage_time
           FROM evdata.vehicle_running_metrics vrm_1
          GROUP BY (date(vrm_1."timestamp")), vrm_1.imei) vrm
     LEFT JOIN ( SELECT date(vs_1."timestamp") AS date,
            vs_1.imei,
            (count(*) * 10) AS charging_time
           FROM evdata.vehicle_status vs_1
          WHERE ((vs_1.vehicle_state)::text = 'CHARGING'::text)
          GROUP BY (date(vs_1."timestamp")), vs_1.imei) vs ON (((vrm.date = vs.date) AND ((vrm.imei)::text = vs.imei))));


CREATE MATERIALIZED VIEW evdata.view_drive_mode_range_and_speed AS
 SELECT concat(pm.name, '.', dm.organisation_id) AS vehicle_model_org_id,
    dm.name AS drive_mode,
    dms.min,
    dms.max,
    dms.range,
    dms.unit,
    dms.hex_color
   FROM (((evdata.part_model pm
     JOIN evdata.vehicle_model_drive_mode vmdm ON ((pm.id = vmdm.model_id)))
     JOIN evdata.drive_mode dm ON ((vmdm.mode_id = dm.id)))
     JOIN evdata.drive_mode_speed dms ON ((dm.id = dms.drive_mode_id)))
  WHERE ((pm.part_type)::text = 'VEHICLE'::text);