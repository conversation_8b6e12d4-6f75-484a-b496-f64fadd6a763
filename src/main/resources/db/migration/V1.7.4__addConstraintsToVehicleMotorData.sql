-- create constraints
ALTER TABLE evdata.vehicle_motor_data
ADD CONSTRAINT vehicle_motor_data_pk PRIMARY KEY (timestamp, imei, motor_id);

ALTER TABLE evdata.vehicle_motor_data
ADD CONSTRAINT vehicle_motor_data_motor_fk FOREIGN KEY (motor_id)
REFERENCES evdata.part (id);

ALTER TABLE evdata.vehicle_motor_data
ADD CONSTRAINT vehicle_motor_data_mfr_org_fk FOREIGN KEY (mfr_org_id)
REFERENCES evusers.organisations (id);


ALTER TABLE evdata.vehicle_motor_data
ADD CONSTRAINT vehicle_motor_data_owner_org_fk FOREIGN KEY (owner_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_motor_data
ADD CONSTRAINT vehicle_motor_data_vehicle_fk FOREIGN KEY (vehicle_id)
REFERENCES evdata.vehicle (id);



CREATE INDEX vehicle_motor_data_imei_idx ON evdata.vehicle_motor_data (imei);
CREATE INDEX vehicle_motor_data_id_idx ON evdata.vehicle_motor_data (motor_id, imei, timestamp);
CREATE INDEX vehicle_motor_data_ts_idx ON evdata.vehicle_motor_data (timestamp);


SELECT create_hypertable('vehicle_motor_data', 'timestamp', migrate_data => true);