--motor threshold view--
CREATE VIEW motor_thresholds AS
SELECT *
FROM crosstab(
    $$
        SELECT v.imei, pmd.name, pmd.value
        FROM vehicle v
        JOIN vehicle_parts vp ON v.id = vp.vehicle_id
        JOIN part p ON vp.part_id = p.id
        JOIN part_model_attributes pma ON p.part_model_id = pma.part_model_id
        JOIN part_model_details pmd ON pma.part_model_attribute_id = pmd.id
        WHERE (p.part_type = 'MOTOR' or p.part_type='MCU') AND pmd.name LIKE '%nominal%'
        ORDER BY 1, 2
    $$,
    $$
        SELECT DISTINCT pmd.name
        FROM vehicle v
        JOIN vehicle_parts vp ON v.id = vp.vehicle_id
        JOIN part p ON vp.part_id = p.id
        JOIN part_model_attributes pma ON p.part_model_id = pma.part_model_id
        JOIN part_model_details pmd ON pma.part_model_attribute_id = pmd.id
        WHERE (p.part_type = 'MOTOR' OR p.part_type='MCU')AND pmd.name LIKE '%nominal%'
        ORDER BY 1
    $$
) AS ct(
    imei text,
	mcs_temperature_above_nominal_critical float,
	mcs_temperature_above_nominal_info float,
	mcs_temperature_above_nominal_warning float,
	mcs_temperature_below_nominal_critical float,
	mcs_temperature_below_nominal_info float,
	mcs_temperature_below_nominal_warning float,
    motor_dc_current_above_nominal_critical float,
    motor_dc_current_above_nominal_info float,
    motor_dc_current_above_nominal_warning float,
    motor_dc_voltage_above_nominal_critical float,
    motor_dc_voltage_above_nominal_info float,
    motor_dc_voltage_above_nominal_warning float,
    motor_dc_voltage_below_nominal_critical float,
    motor_dc_voltage_below_nominal_info float,
    motor_dc_voltage_below_nominal_warning float
);

--battery threshold view --
CREATE VIEW vehicle_battery_data_thresholds AS
SELECT *
FROM crosstab(
    $$
        SELECT v.imei, pmd.name, pmd.value
        FROM vehicle v
        JOIN vehicle_parts vp ON v.id = vp.vehicle_id
        JOIN part p ON vp.part_id = p.id
        JOIN part_model_attributes pma ON p.part_model_id = pma.part_model_id
        JOIN part_model_details pmd ON pma.part_model_attribute_id = pmd.id
        WHERE p.part_type = 'BATTERY' AND pmd.name LIKE '%nominal%'
        ORDER BY 1, 2
    $$,
    $$
        SELECT DISTINCT pmd.name
        FROM vehicle v
        JOIN vehicle_parts vp ON v.id = vp.vehicle_id
        JOIN part p ON vp.part_id = p.id
        JOIN part_model_attributes pma ON p.part_model_id = pma.part_model_id
        JOIN part_model_details pmd ON pma.part_model_attribute_id = pmd.id
        WHERE p.part_type = 'BATTERY' AND pmd.name LIKE '%nominal%'
        ORDER BY 1
    $$
) AS ct(
    imei text,
    battery_volt_above_nominal_critical float,
    battery_volt_above_nominal_info float,
    battery_volt_above_nominal_warning float,
    battery_volt_below_nominal_critical float,
    battery_volt_below_nominal_info float,
    battery_volt_below_nominal_warning float,
    current_above_nominal_critical float,
    current_above_nominal_info float,
    current_above_nominal_warning float,
    current_below_nominal_critical float,
    current_below_nominal_info float,
    current_below_nominal_warning float,
    temperature_above_nominal_critical float,
    temperature_above_nominal_info float,
    temperature_above_nominal_warning float,
    temperature_below_nominal_critical float,
    temperature_below_nominal_info float,
    temperature_below_nominal_warning float
);

-- rangeAlert table--
CREATE TABLE IF NOT EXISTS range_alert
(
    id bigint NOT NULL,
    imei character varying(255),
    vehicle_id bigint,
    time_bucket_minute timestamp(6) with time zone,
    threshold_value real,
    current_value real,
    created_on timestamp(6) with time zone,
    metric character varying(255) ,
    part_type character varying(255) ,
    category character varying(255) ,
    CONSTRAINT range_alert_pkey PRIMARY KEY (id),
    CONSTRAINT uq_imei_time_metric_part_category UNIQUE (imei, time_bucket_minute, metric, part_type, category),
    CONSTRAINT range_alert_metric_check CHECK (metric::text = ANY (ARRAY['MOTOR_DC_CURRENT'::character varying, 'MOTOR_DC_VOLTAGE'::character varying, 'MOTOR_MCS_TEMPERATURE'::character varying, 'BATTERY_VOLT'::character varying, 'CURRENT'::character varying, 'TEMPERATURE_MAX'::character varying,'TEMPERATURE_MIN'::character varying]::text[])),
    CONSTRAINT range_alert_part_type_check CHECK (part_type::text = ANY (ARRAY['MOTOR'::character varying, 'BATTERY'::character varying, 'MCU'::character varying]::text[]))
);

CREATE SEQUENCE IF NOT EXISTS range_alert_seq start with 1 increment by 50;