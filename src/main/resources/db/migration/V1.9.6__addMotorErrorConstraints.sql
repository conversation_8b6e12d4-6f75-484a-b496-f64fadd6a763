-- create hypertable for motor data
SELECT create_hypertable('vehicle_motor_data_error', 'timestamp', migrate_data => true);

-- set chunk time interval for motor hypertables
SELECT set_chunk_time_interval('vehicle_motor_data_error', INTERVAL '7 days');

--set retention policy for motor hypertables
SELECT add_retention_policy('vehicle_motor_data_error', INTERVAL '1 months');
-- enable compression for motor hypertables
ALTER TABLE vehicle_motor_data_error
SET (
	timescaledb.compress,
	timescaledb.compress_segmentby = 'imei,motor_id,vehicle_id,mfr_org_id,owner_org_id',
	timescaledb.compress_orderby='timestamp'
);

-- enable compression policies for motor hypertables
SELECT add_compression_policy('vehicle_motor_data_error', INTERVAL '1 days');
