ALTER TABLE active_vehicle_subscription_plan SET SCHEMA evdata;
ALTER TABLE aggregate_name SET SCHEMA evdata;
ALTER TABLE alarm_type SET SCHEMA evdata;
ALTER TABLE battery_alarm SET SCHEMA evdata;
ALTER TABLE battery_alarm_error SET SCHEMA evdata;
ALTER TABLE battery_cell SET SCHEMA evdata;
ALTER TABLE battery_cell_error SET SCHEMA evdata;
ALTER TABLE battery_health_monitor SET SCHEMA evdata;
ALTER TABLE battery_model SET SCHEMA evdata;
ALTER TABLE battery_stack SET SCHEMA evdata;
ALTER TABLE battery_stack_error SET SCHEMA evdata;
ALTER TABLE bms SET SCHEMA evdata;
ALTER TABLE charger_model SET SCHEMA evdata;
ALTER TABLE city_locations SET SCHEMA evdata;
ALTER TABLE color_model SET SCHEMA evdata;
ALTER TABLE combo_plan SET SCHEMA evdata;
ALTER TABLE comment SET SCHEMA evdata;
ALTER TABLE connectivity_test_summary SET SCHEMA evdata;
ALTER TABLE cron_frequency SET SCHEMA evdata;
ALTER TABLE cron_frequency_data_frequency_plan_details SET SCHEMA evdata;
ALTER TABLE data_frequency_plan SET SCHEMA evdata;
<<<<<<<< HEAD:src/main/resources/db/migration/V1.0.7__MigrateTableToevdataSchema.sql
========
ALTER TABLE data_frequency_plan_details SET SCHEMA evdata;
ALTER TABLE data_frequency_plan_details_aggregate_names SET SCHEMA evdata;
ALTER TABLE data_parsing_field SET SCHEMA evdata;
ALTER TABLE data_parsing_plan SET SCHEMA evdata;
ALTER TABLE data_parsing_plan_fields SET SCHEMA evdata;
--ALTER TABLE deleted_count SET SCHEMA evdata;
>>>>>>>> deploy-1830-api-changes:src/main/resources/db/migration/V1.1.7__MigrateTableToevdataSchema.sql
ALTER TABLE drive_mode SET SCHEMA evdata;
ALTER TABLE drive_mode_max_range SET SCHEMA evdata;
ALTER TABLE drive_mode_speed SET SCHEMA evdata;
ALTER TABLE dyno_test_summary_drive_mode_details SET SCHEMA evdata;
ALTER TABLE export_request SET SCHEMA evdata;
ALTER TABLE fleet SET SCHEMA evdata;
ALTER TABLE fleet_users SET SCHEMA evdata;
ALTER TABLE fleet_vehicle SET SCHEMA evdata;
ALTER TABLE gsm_model SET SCHEMA evdata;
ALTER TABLE image SET SCHEMA evdata;
ALTER TABLE mcu_model SET SCHEMA evdata;
ALTER TABLE motor_health_monitor SET SCHEMA evdata;
ALTER TABLE motor_model SET SCHEMA evdata;
ALTER TABLE organisation_subscription SET SCHEMA evdata;
ALTER TABLE part SET SCHEMA evdata;
ALTER TABLE part_model SET SCHEMA evdata;
ALTER TABLE part_model_attributes SET SCHEMA evdata;
ALTER TABLE part_model_details SET SCHEMA evdata;
ALTER TABLE part_replacement_log SET SCHEMA evdata;
ALTER TABLE prediction_model SET SCHEMA evdata;
ALTER TABLE range_alert SET SCHEMA evdata;
ALTER TABLE rear_tyre_model SET SCHEMA evdata;
ALTER TABLE tcu_model SET SCHEMA evdata;
ALTER TABLE trip SET SCHEMA evdata;
ALTER TABLE trip_acceleration_deceleration SET SCHEMA evdata;
ALTER TABLE trip_brakes SET SCHEMA evdata;
ALTER TABLE trip_details SET SCHEMA evdata;
ALTER TABLE trip_detection_cron_history SET SCHEMA evdata;
ALTER TABLE trip_duration_constants SET SCHEMA evdata;
ALTER TABLE trip_energy_consumption SET SCHEMA evdata;
ALTER TABLE trip_stop_interval SET SCHEMA evdata;
ALTER TABLE trip_turn SET SCHEMA evdata;
ALTER TABLE user_vehicle_connections SET SCHEMA evdata;
ALTER TABLE user_vehicle_trip SET SCHEMA evdata;
ALTER TABLE user_vehicle_trip_details SET SCHEMA evdata;
ALTER TABLE vehicle SET SCHEMA evdata;
ALTER TABLE vehicle_battery_data SET SCHEMA evdata;
ALTER TABLE vehicle_battery_data_error SET SCHEMA evdata;
ALTER TABLE vehicle_charger_data SET SCHEMA evdata;
ALTER TABLE vehicle_charger_data_error SET SCHEMA evdata;
ALTER TABLE vehicle_cluster_data_error SET SCHEMA evdata;
ALTER TABLE vehicle_cluster_data SET SCHEMA evdata;
ALTER TABLE vehicle_color_images SET SCHEMA evdata;
ALTER TABLE vehicle_event_monitor SET SCHEMA evdata;
ALTER TABLE vehicle_latest_data SET SCHEMA evdata;
ALTER TABLE vehicle_location_data SET SCHEMA evdata;
ALTER TABLE vehicle_location_data_error SET SCHEMA evdata;
ALTER TABLE vehicle_model SET SCHEMA evdata;
ALTER TABLE vehicle_model_drive_mode SET SCHEMA evdata;
ALTER TABLE vehicle_model_part_model_image SET SCHEMA evdata;
ALTER TABLE vehicle_model_prediction_mapping SET SCHEMA evdata;
ALTER TABLE vehicle_model_subscription SET SCHEMA evdata;
ALTER TABLE vehicle_model_parts SET SCHEMA evdata;
ALTER TABLE vehicle_parts SET SCHEMA evdata;
ALTER TABLE vehicle_range SET SCHEMA evdata;
ALTER TABLE vehicle_registration_details SET SCHEMA evdata;
ALTER TABLE vehicle_running_metrics SET SCHEMA evdata;
ALTER TABLE vehicle_sale_log SET SCHEMA evdata;
ALTER TABLE vehicle_status SET SCHEMA evdata;
ALTER TABLE vehicle_telemetry_data SET SCHEMA evdata;
ALTER TABLE vehicle_telemetry_data_error SET SCHEMA evdata;
ALTER TABLE vehicle_test SET SCHEMA evdata;
ALTER TABLE vehicle_test_details SET SCHEMA evdata;
ALTER TABLE vehicle_test_ride_form SET SCHEMA evdata;
ALTER TABLE vehicle_test_ride_form_climate_condition SET SCHEMA evdata;
ALTER TABLE vehicle_test_ride_form_issue SET SCHEMA evdata;
ALTER TABLE vehicle_test_ride_form_observation_parameter SET SCHEMA evdata;
ALTER TABLE vehicle_test_ride_form_road_condition SET SCHEMA evdata;
ALTER TABLE vehicle_test_ride_form_traffic SET SCHEMA evdata;
ALTER TABLE vehicle_test_ride_form_vehicle_parameters SET SCHEMA evdata;




