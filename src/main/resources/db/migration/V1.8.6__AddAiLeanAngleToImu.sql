SELECT create_hypertable('evdata.vehicle_imu_data_error', 'timestamp', migrate_data => true);

ALTER TABLE evdata.vehicle_imu_data
ADD COLUMN ai_lean_angle INTEGER;

ALTER TABLE evdata.vehicle_imu_data_raw
ADD COLUMN ai_lean_angle INTEGER;

ALTER TABLE evdata.vehicle_imu_data_error
ADD COLUMN ai_lean_angle INTEGER;

UPDATE evdata.vehicle_imu_data imu
SET ai_lean_angle = vtd.ai_lean_angle
FROM evdata.vehicle_telemetry_data vtd
WHERE
    imu.timestamp = vtd.timestamp AND
    imu.vehicle_id = vtd.vehicle_id AND
    imu.imei = vtd.imei AND
    imu.ai_lean_angle IS NULL;

UPDATE evdata.vehicle_imu_data_raw imu
SET ai_lean_angle = vtd.ai_lean_angle
FROM evdata.vehicle_telemetry_data_raw vtd
WHERE
    imu.timestamp = vtd.timestamp AND
    imu.vehicle_id = vtd.vehicle_id AND
    imu.imei = vtd.imei AND
    imu.ai_lean_angle IS NULL;

UPDATE evdata.vehicle_imu_data_error imu
SET ai_lean_angle = vtd.ai_lean_angle
FROM evdata.vehicle_telemetry_data_error vtd
WHERE
    imu.timestamp = vtd.timestamp AND
    imu.vehicle_id = vtd.vehicle_id AND
    imu.imei = vtd.imei AND
    imu.ai_lean_angle IS NULL;