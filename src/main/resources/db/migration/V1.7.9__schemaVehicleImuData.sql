CREATE TABLE evdata.vehicle_imu_data (
    accel_x_axis         FLOAT4,
    accel_y_axis         FLOAT4,
    accel_z_axis         FLOAT4,
    grv_x_axis           FLOAT4,
    grv_y_axis           FLOAT4,
    grv_z_axis           FLOAT4,
    gyro_x_axis          FLOAT4,
    gyro_y_axis          FLOAT4,
    gyro_z_axis          FLOAT4,
    ac_x_speed		 FLOAT4,
    ac_x_distance        FLOAT4,
    created_on          TIMESTAMP(6) WITH TIME ZONE,
    mfr_org_id          BIGINT,
    owner_org_id        BIGINT,
    packet_received_on  TIMESTAMP(6) WITH TIME ZONE,
    timestamp           TIMESTAMP(6) WITH TIME ZONE NOT NULL,
    vehicle_id          BIGINT,
    co_relation_id      UUID,
    imei                TEXT NOT NULL,
    di_motion BOOLEAN,
    di_ignition BOOLEAN
);