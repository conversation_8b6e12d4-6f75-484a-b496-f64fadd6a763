CREATE TABLE evdata.vehicle_imu_data_error (
    accel_x_axis         FLOAT4,
    accel_y_axis         FLOAT4,
    accel_z_axis         FLOAT4,
    grv_x_axis           FLOAT4,
    grv_y_axis           FLOAT4,
    grv_z_axis           FLOAT4,
    gyro_x_axis          FLOAT4,
    gyro_y_axis          FLOAT4,
    gyro_z_axis          FLOAT4,
    ac_x_speed		 FLOAT4,
    ac_x_distance        FLOAT4,
    created_on          TIMESTAMP(6) WITH TIME ZONE,
    mfr_org_id          BIGINT,
    owner_org_id        BIGINT,
    packet_received_on  TIMESTAMP(6) WITH TIME ZONE,
    timestamp           TIMESTAMP(6) WITH TIME ZONE NOT NULL,
    vehicle_id          BIGINT,
    co_relation_id      UUID,
    imei                TEXT NOT NULL,
    di_motion BOOLEAN,
    di_ignition BOOLEAN
);


DO $$
DECLARE
    start_date TIMESTAMP;
    end_date TIMESTAMP;
    current_start TIMESTAMP;
    current_end TIMESTAMP;
BEGIN
    -- Get date range
    SELECT MIN(timestamp), MAX(timestamp)
    INTO start_date, end_date
    FROM evdata.vehicle_telemetry_data_error;

    -- Initialize batch range
    current_start := start_date;
    current_end := start_date + INTERVAL '3 days';

    -- Loop through date range in 3-day batches
    WHILE current_start <= end_date LOOP
        -- Insert batch
        INSERT INTO evdata.vehicle_imu_data_error (
			accel_x_axis,
    		accel_y_axis,
    		accel_z_axis,
			grv_x_axis,
    		grv_y_axis,
    		grv_z_axis,
			gyro_x_axis,
    		gyro_y_axis,
    		gyro_z_axis,
    		timestamp,
            imei,
            vehicle_id,
            created_on,
            mfr_org_id,
            owner_org_id,
            packet_received_on,
            co_relation_id,
            di_ignition,
            di_motion
        )
        SELECT
            vtd.accel_x_axis,
    		vtd.accel_y_axis,
    		vtd.accel_z_axis,
			vtd.grv_x_axis,
    		vtd.grv_y_axis,
    		vtd.grv_z_axis,
			vtd.gyro_x_axis,
    		vtd.gyro_y_axis,
    		vtd.gyro_z_axis,
    		vtd.timestamp,
            vtd.imei,
            vtd.vehicle_id,
            vtd.created_on,
            vtd.mfr_org_id,
            vtd.owner_org_id,
            vtd.packet_received_on,
            vtd.co_relation_id,
            di_ignition,
            di_motion
        FROM
            evdata.vehicle_telemetry_data_error vtd
        WHERE
            vtd.timestamp >= current_start
            AND vtd.timestamp < current_end;

        -- Move to next 3-day batch
        current_start := current_end;
        current_end := current_end + INTERVAL '3 days';

    END LOOP;
END $$;


-- create constraints
ALTER TABLE evdata.vehicle_imu_data_error
ADD CONSTRAINT vehicle_imu_data_error_pk PRIMARY KEY (timestamp, imei);

ALTER TABLE evdata.vehicle_imu_data_error
ADD CONSTRAINT vehicle_imu_data_error_mfr_org_fk FOREIGN KEY (mfr_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_imu_data_error
ADD CONSTRAINT vehicle_imu_data_error_owner_org_fk FOREIGN KEY (owner_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_imu_data_error
ADD CONSTRAINT vehicle_imu_data_error_vehicle_fk FOREIGN KEY (vehicle_id)
REFERENCES evdata.vehicle (id);

CREATE INDEX vehicle_imu_data_error_imei_idx ON evdata.vehicle_imu_data_error (imei);
CREATE INDEX vehicle_imu_data_error_id_idx ON evdata.vehicle_imu_data_error (imei, timestamp);
CREATE INDEX vehicle_imu_data_error_ts_idx ON evdata.vehicle_imu_data_error (timestamp);


ALTER TABLE evdata.vehicle_telemetry_data_error
    DROP COLUMN accel_x_axis,
    DROP COLUMN accel_y_axis,
    DROP COLUMN accel_z_axis,
    DROP COLUMN gyro_x_axis,
    DROP COLUMN gyro_y_axis,
    DROP COLUMN gyro_z_axis,
    DROP COLUMN grv_x_axis,
    DROP COLUMN grv_y_axis,
    DROP COLUMN grv_z_axis;

