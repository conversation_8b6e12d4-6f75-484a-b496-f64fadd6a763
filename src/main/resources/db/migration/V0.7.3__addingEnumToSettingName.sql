ALTER TABLE setting drop constraint IF EXISTS setting_name_check;

ALTER TABLE setting
ADD CONSTRAINT setting_name_check CHECK
(name::text = ANY (ARRAY['PRIVACY_POLICY'::character varying::text, 'TERMS_AND_CONDITION'::character varying::text,
								'SMS_PROVIDER'::character varying::text, 'B2B_TEMPLATE_ID'::character varying::text,
						'B2C_TEMPLATE_ID'::character varying::text,'WEB_APP_TEMPLATE_ID'::character varying::text
						,'DEFAULT_TEMPLATE_ID'::character varying::text,'MSG91_API_URL'::character varying::text,
						 'MSG91_AUTH_KEY'::character varying::text]));