-- Create the materialized view for 10-sec aggregate
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.view_motor_data_aggregate_per_10sec
WITH (timescaledb.continuous) AS
SELECT
    md.motor_id,
    md.mfr_org_id,
    md.owner_org_id,
    md.imei,
    md.vehicle_id,
    time_bucket('10s', md.timestamp) AS time_bucket,
    MAX(md.motor_dc_current) AS max_motor_dc_current,
    first(md.motor_dc_current, md.timestamp) AS first_motor_dc_current,
    MIN(md.motor_dc_current) AS min_motor_dc_current,
    MAX(md.motor_dc_voltage) AS max_motor_dc_voltage,
    first(md.motor_dc_voltage, md.timestamp) AS first_motor_dc_voltage,
    MIN(md.motor_dc_voltage) AS min_motor_dc_voltage,
    MAX(md.motor_mcs_temperature) AS max_motor_mcs_temperature,
    first(md.motor_mcs_temperature, md.timestamp) AS first_motor_mcs_temperature,
    MIN(md.motor_mcs_temperature) AS min_motor_mcs_temperature
FROM
    evdata.vehicle_motor_data md
GROUP BY
    md.motor_id,
    md.mfr_org_id,
    md.owner_org_id,
    md.imei,
    md.vehicle_id,
    time_bucket('10s', md.timestamp)
WITH NO DATA;

-- Add continuous aggregate policy
SELECT add_continuous_aggregate_policy('evdata.view_motor_data_aggregate_per_10sec',
 start_offset => INTERVAL '1 day',
 end_offset => INTERVAL '1 second',
 schedule_interval => INTERVAL '10 second');



CREATE MATERIALIZED VIEW evdata.view_motor_data
WITH (timescaledb.continuous) AS
    SELECT
        imei,
        time_bucket('1s', timestamp) AS bucket,
        motor_dc_voltage ,
        motor_dc_current ,
        motor_temperature ,
        motor_mcs_temperature
    FROM evdata.vehicle_motor_data
    GROUP BY bucket,
    imei,
        motor_dc_voltage ,
        motor_dc_current ,
        motor_temperature ,
        motor_mcs_temperature WITH NO DATA;





-- Create the materialized view per min aggregate
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.view_motor_data_aggregate_per_1min
WITH (timescaledb.continuous) AS
SELECT
    v1.motor_id,
    v1.imei,
    v1.mfr_org_id,
    v1.owner_org_id,
    v1.vehicle_id,
    time_bucket('1 minute', v1.time_bucket) AS time_bucket_1min,
    MAX(v1.max_motor_dc_current) AS max_motor_dc_current,
    MIN(v1.min_motor_dc_current) AS min_motor_dc_current,
    MAX(v1.max_motor_dc_voltage) AS max_motor_dc_voltage,
    MIN(v1.min_motor_dc_voltage) AS min_motor_dc_voltage,
    MAX(v1.max_motor_mcs_temperature) AS max_motor_mcs_temperature,
    MIN(v1.min_motor_mcs_temperature) AS min_motor_mcs_temperature
FROM
    evdata.view_motor_data_aggregate_per_10sec v1
GROUP BY
    v1.motor_id, v1.imei, v1.mfr_org_id, v1.owner_org_id, v1.vehicle_id, time_bucket('1 minute', v1.time_bucket)
WITH NO DATA;

-- Add continuous aggregate policy for 1-minute view
SELECT add_continuous_aggregate_policy(
    'evdata.view_motor_data_aggregate_per_1min',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '1 minute',
    schedule_interval => INTERVAL '1 minute'
);

CREATE MATERIALIZED VIEW evdata.view_motor_aggregate_data
WITH (timescaledb.continuous) AS
    SELECT
        imei,
        time_bucket('1s', timestamp) AS bucket,
        round(coalesce(cast(MAX(motor_dc_voltage) as decimal), 0), 2) AS maxDcVoltage,
        round(coalesce(cast(MIN(motor_dc_voltage) as decimal), 0), 2) AS minDcVoltage,
        round(coalesce(cast(AVG(motor_dc_voltage) as decimal), 0), 2) AS avgDcVoltage,
        round(coalesce(cast(MAX(motor_dc_current) as decimal), 0), 2) AS maxDcCurrent,
        round(coalesce(cast(MIN(motor_dc_current) as decimal), 0), 2) AS minDcCurrent,
        round(coalesce(cast(AVG(motor_dc_current) as decimal), 0), 2) AS avgDcCurrent,
        round(coalesce(cast(MAX(motor_temperature) as decimal), 0), 2) AS maxMotorTemp,
        round(coalesce(cast(MIN(motor_temperature) as decimal), 0), 2) AS minMotorTemp,
        round(coalesce(cast(AVG(motor_temperature) as decimal), 0), 2) AS avgMotorTemp,
        round(coalesce(cast(MAX(motor_mcs_temperature) as decimal), 0), 2) AS maxMcsTemp,
        round(coalesce(cast(MIN(motor_mcs_temperature) as decimal), 0), 2) AS minMcsTemp,
        round(coalesce(cast(AVG(motor_mcs_temperature) as decimal), 0), 2) AS avgMcsTemp
    FROM evdata.vehicle_motor_data
    GROUP BY bucket, imei WITH NO DATA;



-- Create the materialized view vehicle motor aggregate data
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.view_vehicle_motor_aggregate_data
WITH (timescaledb.continuous) AS
select
vtd.motor_id,
vtd.imei as imei,
vtd.vehicle_id,
vtd.mfr_org_id as mfr_org_id,
vtd.owner_org_id as owner_org_id,
time_bucket('2m',vtd.timestamp) as bucket,
round(avg(vtd.motor_dc_voltage)::numeric,2) as avg_motor_dc_voltage,
round(avg(vtd.motor_dc_current)::numeric,2) as avg_motor_dc_current,
round(avg(vtd.motor_temperature)::numeric,2) as avg_motor_temperature,
round(avg(vtd.motor_mcs_temperature)::numeric,2) as avg_motor_mcs_temperature,
round(max(vtd.motor_dc_voltage)::numeric, 2) AS max_motor_dc_voltage,
round(max(vtd.motor_dc_current)::numeric, 2) AS max_motor_dc_current,
round(max(vtd.motor_temperature)::numeric, 2) AS max_motor_temperature,
round(max(vtd.motor_mcs_temperature)::numeric, 2) AS max_motor_mcs_temperature,
round(min(vtd.motor_dc_voltage)::numeric, 2) AS min_motor_dc_voltage,
round(min(vtd.motor_dc_current)::numeric, 2) AS min_motor_dc_current,
round(min(vtd.motor_temperature)::numeric, 2) AS min_motor_temperature,
round(min(vtd.motor_mcs_temperature)::numeric, 2) AS min_motor_mcs_temperature
from evdata.vehicle_motor_data vtd
where vtd.timestamp >'2024-01-01'
group by
vtd.motor_id,
vtd.imei,
vtd.vehicle_id,
vtd.mfr_org_id,
vtd.owner_org_id,
time_bucket('2m',timestamp) WITH NO DATA;

--adding aggregate policy for view_vehicle_motor_aggregate_data
SELECT add_continuous_aggregate_policy('evdata.view_vehicle_motor_aggregate_data',
 start_offset => INTERVAL '1 day',
 end_offset => INTERVAL '2 minutes',
 schedule_interval => INTERVAL '1 minute');



-- Create the materialized vehicle_telemetry_data_availability_aggregate_daily
CREATE MATERIALIZED VIEW evdata.vehicle_telemetry_data_availability_aggregate_daily
WITH (timescaledb.continuous) AS
SELECT
    vehicle_id,
    imei,
    time_bucket('1 day', timestamp) AS day,
    COUNT(*) AS telemetry_data_availability
FROM
    evdata.vehicle_telemetry_data
GROUP BY
    vehicle_id,
    imei,
    day
WITH NO DATA;

CREATE INDEX telemetry_availability_idx_imei
ON evdata.vehicle_telemetry_data_availability_aggregate_daily (imei);

CREATE INDEX telemetry_availability_idx_vehicle_id
ON evdata.vehicle_telemetry_data_availability_aggregate_daily (vehicle_id);

CREATE INDEX telemetry_availability_idx_day_imei_telemetry
ON evdata.vehicle_telemetry_data_availability_aggregate_daily (day, imei, telemetry_data_availability);

SELECT add_continuous_aggregate_policy('evdata.vehicle_telemetry_data_availability_aggregate_daily',
    start_offset => INTERVAL '2 day',
    end_offset => INTERVAL '0 day',
    schedule_interval => INTERVAL '5 minutes');



-- Create the materialized vehicle_motor_data_availability_aggregate_daily
CREATE MATERIALIZED VIEW evdata.vehicle_motor_data_availability_aggregate_daily
WITH (timescaledb.continuous) AS
SELECT
    motor_id,
    vehicle_id,
    imei,
    time_bucket('1 day', timestamp) AS day,
    COUNT(*) AS motor_data_availability
FROM
    evdata.vehicle_motor_data
GROUP BY
    motor_id,
    vehicle_id,
    imei,
    day
WITH NO DATA;

CREATE INDEX motor_availability_idx_imei
ON evdata.vehicle_motor_data_availability_aggregate_daily (imei);

CREATE INDEX motor_availability_idx_motor_id
ON evdata.vehicle_motor_data_availability_aggregate_daily (motor_id);

CREATE INDEX motor_availability_idx_day_imei_motor
ON evdata.vehicle_motor_data_availability_aggregate_daily (day, imei, motor_data_availability);

SELECT add_continuous_aggregate_policy('evdata.vehicle_motor_data_availability_aggregate_daily',
    start_offset => INTERVAL '2 day',
    end_offset => INTERVAL '0 day',
    schedule_interval => INTERVAL '5 minutes');






--Continuous Aggregate for vehicle_telemetry_data(motor) across 1 minute window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_motor_first_aggregate_1m
WITH (timescaledb.continuous) AS
SELECT
    vtd.motor_id,
    vtd.vehicle_id AS vehicle_id,
    vtd.imei AS imei,
    vtd.mfr_org_id AS mfr_org_id,
    vtd.owner_org_id AS owner_org_id,
    time_bucket('1m', vtd.timestamp) AS bucket,
    first(vtd.motor_brake, vtd.timestamp) FILTER (WHERE vtd.motor_brake IS NOT NULL) AS motor_brake,
    first(vtd.motor_cruise, vtd.timestamp) FILTER (WHERE vtd.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vtd.motor_dc_current, vtd.timestamp) FILTER (WHERE vtd.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vtd.motor_dc_voltage, vtd.timestamp) FILTER (WHERE vtd.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vtd.motor_mcs_temperature, vtd.timestamp) FILTER (WHERE vtd.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vtd.motor_parking_sign, vtd.timestamp) FILTER (WHERE vtd.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vtd.motor_ready_sign, vtd.timestamp) FILTER (WHERE vtd.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vtd.motor_regeneration, vtd.timestamp) FILTER (WHERE vtd.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vtd.motor_reverse, vtd.timestamp) FILTER (WHERE vtd.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vtd.motor_side_stand, vtd.timestamp) FILTER (WHERE vtd.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vtd.motor_speed, vtd.timestamp) FILTER (WHERE vtd.motor_speed IS NOT NULL) AS motor_speed,
    first(vtd.motor_temperature, vtd.timestamp) FILTER (WHERE vtd.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vtd.motor_throttle, vtd.timestamp) FILTER (WHERE vtd.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vtd.motor_driving_mode, vtd.timestamp) FILTER (WHERE vtd.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vtd.motor_fault_feedback, vtd.timestamp) FILTER (WHERE vtd.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback
FROM evdata.vehicle_motor_data vtd
GROUP BY bucket,vtd.motor_id, vtd.vehicle_id, vtd.imei, vtd.mfr_org_id, vtd.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1m every minute
SELECT add_continuous_aggregate_policy('evdata.vehicle_motor_first_aggregate_1m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');


--Continuous Aggregate for vehicle_telemetry_data(motor) across 10 minutes window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_motor_first_aggregate_10m
WITH (timescaledb.continuous) AS
SELECT
    vmf1m.motor_id,
    vmf1m.vehicle_id AS vehicle_id,
    vmf1m.imei AS imei,
    vmf1m.mfr_org_id AS mfr_org_id,
    vmf1m.owner_org_id AS owner_org_id,
    time_bucket('10m', vmf1m.bucket) AS bucket,
    first(vmf1m.motor_brake, vmf1m.bucket) FILTER (WHERE vmf1m.motor_brake IS NOT NULL) AS motor_brake,
    first(vmf1m.motor_cruise, vmf1m.bucket) FILTER (WHERE vmf1m.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vmf1m.motor_dc_current, vmf1m.bucket) FILTER (WHERE vmf1m.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vmf1m.motor_dc_voltage, vmf1m.bucket) FILTER (WHERE vmf1m.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vmf1m.motor_mcs_temperature, vmf1m.bucket) FILTER (WHERE vmf1m.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vmf1m.motor_parking_sign, vmf1m.bucket) FILTER (WHERE vmf1m.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vmf1m.motor_ready_sign, vmf1m.bucket) FILTER (WHERE vmf1m.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vmf1m.motor_regeneration, vmf1m.bucket) FILTER (WHERE vmf1m.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vmf1m.motor_reverse, vmf1m.bucket) FILTER (WHERE vmf1m.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vmf1m.motor_side_stand, vmf1m.bucket) FILTER (WHERE vmf1m.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vmf1m.motor_speed, vmf1m.bucket) FILTER (WHERE vmf1m.motor_speed IS NOT NULL) AS motor_speed,
    first(vmf1m.motor_temperature, vmf1m.bucket) FILTER (WHERE vmf1m.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vmf1m.motor_throttle, vmf1m.bucket) FILTER (WHERE vmf1m.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vmf1m.motor_driving_mode, vmf1m.bucket) FILTER (WHERE vmf1m.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vmf1m.motor_fault_feedback, vmf1m.bucket) FILTER (WHERE vmf1m.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback
FROM evdata.vehicle_motor_first_aggregate_1m vmf1m
GROUP BY time_bucket('10m', vmf1m.bucket), vmf1m.motor_id, vmf1m.vehicle_id, vmf1m.imei, vmf1m.mfr_org_id, vmf1m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1m_1h every 10 minutes
SELECT add_continuous_aggregate_policy('evdata.vehicle_motor_first_aggregate_10m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '10 minute');


--Continuous Aggregate for vehicle_telemetry_data(motor) across 1 hour window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_motor_first_aggregate_1h
WITH (timescaledb.continuous) AS
SELECT
    vmf10m.motor_id,
    vmf10m.vehicle_id AS vehicle_id,
    vmf10m.imei AS imei,
    vmf10m.mfr_org_id AS mfr_org_id,
    vmf10m.owner_org_id AS owner_org_id,
    time_bucket('1h', vmf10m.bucket) AS bucket,
    first(vmf10m.motor_brake, vmf10m.bucket) FILTER (WHERE vmf10m.motor_brake IS NOT NULL) AS motor_brake,
    first(vmf10m.motor_cruise, vmf10m.bucket) FILTER (WHERE vmf10m.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vmf10m.motor_dc_current, vmf10m.bucket) FILTER (WHERE vmf10m.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vmf10m.motor_dc_voltage, vmf10m.bucket) FILTER (WHERE vmf10m.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vmf10m.motor_mcs_temperature, vmf10m.bucket) FILTER (WHERE vmf10m.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vmf10m.motor_parking_sign, vmf10m.bucket) FILTER (WHERE vmf10m.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vmf10m.motor_ready_sign, vmf10m.bucket) FILTER (WHERE vmf10m.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vmf10m.motor_regeneration, vmf10m.bucket) FILTER (WHERE vmf10m.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vmf10m.motor_reverse, vmf10m.bucket) FILTER (WHERE vmf10m.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vmf10m.motor_side_stand, vmf10m.bucket) FILTER (WHERE vmf10m.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vmf10m.motor_speed, vmf10m.bucket) FILTER (WHERE vmf10m.motor_speed IS NOT NULL) AS motor_speed,
    first(vmf10m.motor_temperature, vmf10m.bucket) FILTER (WHERE vmf10m.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vmf10m.motor_throttle, vmf10m.bucket) FILTER (WHERE vmf10m.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vmf10m.motor_driving_mode, vmf10m.bucket) FILTER (WHERE vmf10m.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vmf10m.motor_fault_feedback, vmf10m.bucket) FILTER (WHERE vmf10m.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback
FROM evdata.vehicle_motor_first_aggregate_10m vmf10m
GROUP BY time_bucket('1h', vmf10m.bucket), vmf10m.motor_id, vmf10m.vehicle_id, vmf10m.imei, vmf10m.mfr_org_id, vmf10m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1h every hour
SELECT add_continuous_aggregate_policy('evdata.vehicle_motor_first_aggregate_1h',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 hour');


CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_motor_first_aggregate_1d
WITH (timescaledb.continuous) AS
SELECT
    vmf1h.motor_id,
    vmf1h.vehicle_id AS vehicle_id,
    vmf1h.imei AS imei,
    vmf1h.mfr_org_id AS mfr_org_id,
    vmf1h.owner_org_id AS owner_org_id,
    time_bucket('1d', vmf1h.bucket) AS bucket,
    first(vmf1h.motor_brake, vmf1h.bucket) FILTER (WHERE vmf1h.motor_brake IS NOT NULL) AS motor_brake,
    first(vmf1h.motor_cruise, vmf1h.bucket) FILTER (WHERE vmf1h.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vmf1h.motor_dc_current, vmf1h.bucket) FILTER (WHERE vmf1h.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vmf1h.motor_dc_voltage, vmf1h.bucket) FILTER (WHERE vmf1h.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vmf1h.motor_mcs_temperature, vmf1h.bucket) FILTER (WHERE vmf1h.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vmf1h.motor_parking_sign, vmf1h.bucket) FILTER (WHERE vmf1h.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vmf1h.motor_ready_sign, vmf1h.bucket) FILTER (WHERE vmf1h.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vmf1h.motor_regeneration, vmf1h.bucket) FILTER (WHERE vmf1h.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vmf1h.motor_reverse, vmf1h.bucket) FILTER (WHERE vmf1h.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vmf1h.motor_side_stand, vmf1h.bucket) FILTER (WHERE vmf1h.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vmf1h.motor_speed, vmf1h.bucket) FILTER (WHERE vmf1h.motor_speed IS NOT NULL) AS motor_speed,
    first(vmf1h.motor_temperature, vmf1h.bucket) FILTER (WHERE vmf1h.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vmf1h.motor_throttle, vmf1h.bucket) FILTER (WHERE vmf1h.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vmf1h.motor_driving_mode, vmf1h.bucket) FILTER (WHERE vmf1h.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vmf1h.motor_fault_feedback, vmf1h.bucket) FILTER (WHERE vmf1h.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback
FROM evdata.vehicle_motor_first_aggregate_1h vmf1h
GROUP BY time_bucket('1d', vmf1h.bucket), vmf1h.motor_id, vmf1h.vehicle_id, vmf1h.imei, vmf1h.mfr_org_id, vmf1h.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1d every day
SELECT add_continuous_aggregate_policy('evdata.vehicle_motor_first_aggregate_1d',
  start_offset => INTERVAL '3 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 day');



