-- create new table vehicle_motor_data
CREATE TABLE  if not exists  evdata.vehicle_motor_data (
    motor_id BIGINT NOT NULL,
    motor_brake BOOLEAN,
    motor_cruise BOOLEAN,
    motor_dc_current FLOAT4,
    motor_dc_voltage FLOAT4,
    motor_mcs_temperature FLOAT4,
    motor_parking_sign <PERSON><PERSON><PERSON><PERSON><PERSON>,
    motor_ready_sign <PERSON><PERSON><PERSON><PERSON><PERSON>,
    motor_regeneration BOOLEAN,
    motor_reverse BOOLEAN,
    motor_side_stand BOOLEAN,
    motor_speed FLOAT4,
    motor_temperature FLOAT4,
    motor_throttle FLOAT4,
    motor_driving_mode varchar(255) check (motor_driving_mode in ('ECO','POWER','CITY','REVERSE','NULL_DRIVE_SELECTION','MODE_S','MODE_M','MODE_H')),
    motor_fault_feedback VARCHAR(255),
    timestamp timestamp(6) with time zone NOT NULL,
    imei TEXT NOT NULL,
    vehicle_id BIGINT NOT NULL,
    created_on timestamp(6) with time zone,
    mfr_org_id BIGINT,
    owner_org_id BIGINT,
    packet_received_on timestamp(6) with time zone,
    co_relation_id UUID,
    di_motion BOOLEAN,
    di_ignition BOOLEAN,
    motor_speed_kmph FLOAT4,
    motor_distance FLOAT4,
    motor_acceleration FLOAT4,
    odometer FLOAT4
);

-- create new table vehicle_motor_data_error
CREATE TABLE  if not exists evdata.vehicle_motor_data_error (
    motor_id BIGINT NOT NULL,
    motor_brake BOOLEAN,
    motor_cruise BOOLEAN,
    motor_dc_current FLOAT4,
    motor_dc_voltage FLOAT4,
    motor_mcs_temperature FLOAT4,
    motor_parking_sign BOOLEAN,
    motor_ready_sign BOOLEAN,
    motor_regeneration BOOLEAN,
    motor_reverse BOOLEAN,
    motor_side_stand BOOLEAN,
    motor_speed FLOAT4,
    motor_temperature FLOAT4,
    motor_throttle FLOAT4,
    motor_driving_mode varchar(255) check (motor_driving_mode in ('ECO','POWER','CITY','REVERSE','NULL_DRIVE_SELECTION','MODE_S','MODE_M','MODE_H')),
    motor_fault_feedback VARCHAR(255),
    timestamp timestamp(6) with time zone NOT NULL,
    imei TEXT NOT NULL,
    vehicle_id BIGINT NOT NULL,
    created_on timestamp(6) with time zone,
    mfr_org_id BIGINT,
    owner_org_id BIGINT,
    packet_received_on timestamp(6) with time zone,
    co_relation_id UUID,
    di_ignition BOOLEAN,
    di_motion BOOLEAN,
    motor_speed_kmph FLOAT4,
    motor_distance FLOAT4,
    motor_acceleration FLOAT4,
    odometer FLOAT4
);

-- create imu data table
CREATE TABLE  if not exists evdata.vehicle_imu_data (
    accel_x_axis         FLOAT4,
    accel_y_axis         FLOAT4,
    accel_z_axis         FLOAT4,
    grv_x_axis           FLOAT4,
    grv_y_axis           FLOAT4,
    grv_z_axis           FLOAT4,
    gyro_x_axis          FLOAT4,
    gyro_y_axis          FLOAT4,
    gyro_z_axis          FLOAT4,
    ac_x_speed		 FLOAT4,
    ac_x_distance        FLOAT4,
    created_on          TIMESTAMP(6) WITH TIME ZONE,
    mfr_org_id          BIGINT,
    owner_org_id        BIGINT,
    packet_received_on  TIMESTAMP(6) WITH TIME ZONE,
    timestamp           TIMESTAMP(6) WITH TIME ZONE NOT NULL,
    vehicle_id          BIGINT,
    co_relation_id      UUID,
    imei                TEXT NOT NULL,
    di_motion BOOLEAN,
    di_ignition BOOLEAN,
    ai_lean_angle INTEGER
);

-- create imu raw data table
CREATE TABLE  if not exists evdata.vehicle_imu_data_raw (
    accel_x_axis         FLOAT4,
    accel_y_axis         FLOAT4,
    accel_z_axis         FLOAT4,
    grv_x_axis           FLOAT4,
    grv_y_axis           FLOAT4,
    grv_z_axis           FLOAT4,
    gyro_x_axis          FLOAT4,
    gyro_y_axis          FLOAT4,
    gyro_z_axis          FLOAT4,
    ac_x_speed		 FLOAT4,
    ac_x_distance        FLOAT4,
    created_on          TIMESTAMP(6) WITH TIME ZONE,
    mfr_org_id          BIGINT,
    owner_org_id        BIGINT,
    packet_received_on  TIMESTAMP(6) WITH TIME ZONE,
    timestamp           TIMESTAMP(6) WITH TIME ZONE NOT NULL,
    vehicle_id          BIGINT,
    co_relation_id      UUID,
    imei                TEXT NOT NULL,
    di_motion BOOLEAN,
    di_ignition BOOLEAN,
    ai_lean_angle INTEGER
);


-- create new table vehicle_imu_data_error
CREATE TABLE if not exists evdata.vehicle_imu_data_error (
    accel_x_axis         FLOAT4,
    accel_y_axis         FLOAT4,
    accel_z_axis         FLOAT4,
    grv_x_axis           FLOAT4,
    grv_y_axis           FLOAT4,
    grv_z_axis           FLOAT4,
    gyro_x_axis          FLOAT4,
    gyro_y_axis          FLOAT4,
    gyro_z_axis          FLOAT4,
    ac_x_speed		 FLOAT4,
    ac_x_distance        FLOAT4,
    created_on          TIMESTAMP(6) WITH TIME ZONE,
    mfr_org_id          BIGINT,
    owner_org_id        BIGINT,
    packet_received_on  TIMESTAMP(6) WITH TIME ZONE,
    timestamp           TIMESTAMP(6) WITH TIME ZONE NOT NULL,
    vehicle_id          BIGINT,
    co_relation_id      UUID,
    imei                TEXT NOT NULL,
    di_motion BOOLEAN,
    di_ignition BOOLEAN,
    ai_lean_angle INTEGER
);

