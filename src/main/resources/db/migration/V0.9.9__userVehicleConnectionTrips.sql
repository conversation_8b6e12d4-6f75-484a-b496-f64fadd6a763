-- user_vehicle_trip table creation
CREATE SEQUENCE IF NOT EXISTS user_vehicle_trip_seq start with 1 increment by 50;

CREATE TABLE IF NOT EXISTS user_vehicle_trip(
	id bigint PRIMARY KEY DEFAULT nextval('user_vehicle_trip_seq'),
	connection_id bigint NOT NULL,
	start_time timestamp (6) with time zone,
	end_time timestamp (6) with time zone,
	status character varying(255),
	trip_id bigint NOT NULL,
	trip_end_city character varying (255),
	trip_end_neighbourhood character varying (255),
	trip_end_state character varying (255),
	trip_end_suburb character varying (255),
	trip_start_city character varying (255),
	trip_start_neighbourhood character varying (255),
	trip_start_state character varying (255),
	trip_start_suburb character varying (255),
	created_on timestamp (6) with time zone,
	updated_on timestamp (6) with time zone,
	CONSTRAINT fk_user_vehicle_trip FOREIGN KEY (connection_id)
	REFERENCES user_vehicle_connections (id),
	CONSTRAINT fk__trip FOREIGN KEY (trip_id)
	REFERENCES trip (id),
	CONSTRAINT status_check CHECK (status::text = ANY (ARRAY['IN_PROGRESS'::character varying::text, 'COMPLETED'::character varying::text, 'FAILED'::character varying::text]))
);
create index if not exists user_vehicle_trip_connection_status_idx on user_vehicle_trip(connection_id, status);

-- user_vehicle_trip_details table creation
CREATE TABLE IF NOT EXISTS user_vehicle_trip_details
(
    "timestamp" timestamp(6) with time zone NOT NULL,
    user_vehicle_trip_id bigint NOT NULL,
    data_type character varying(255),
    field_name character varying (255) NOT NULL,
    field_value character varying(255),
    imei character varying(255) NOT NULL,
    CONSTRAINT user_vehicle_trip_details_pkey PRIMARY KEY ("timestamp", user_vehicle_trip_id, field_name, imei),
    CONSTRAINT fk_user_vehicle_trip_id FOREIGN KEY (user_vehicle_trip_id)
    REFERENCES user_vehicle_trip (id)
);
create index if not exists user_vehicle_trip_details_imei_fieldName_idx on user_vehicle_trip_details(imei, field_name);
create index if not exists user_vehicle_trip_details_user_trip_idx on user_vehicle_trip_details(user_vehicle_trip_id);

