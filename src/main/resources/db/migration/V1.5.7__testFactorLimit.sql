-- Create sequence for test_factor_limit table
CREATE SEQUENCE IF NOT EXISTS test_factor_limit_seq START WITH 1 INCREMENT BY 1;

-- Create table test_factor_limit
CREATE TABLE IF NOT EXISTS test_factor_limit (
    id BIGINT PRIMARY KEY DEFAULT nextval('test_factor_limit_seq'),
    test_factor_id BIGINT,
    range_label VARCHAR(255) NOT NULL,
    min_value FLOAT,
    max_value FLOAT,
    data_type VARCHAR(255),
    CONSTRAINT fk_test_factor FOREIGN KEY (test_factor_id) REFERENCES test_factor (id),
    CONSTRAINT range_label_check CHECK (
        range_label::text = ANY (ARRAY[
            'POOR'::character varying::text,
            'AVERAGE'::character varying::text,
            'GOOD'::character varying::text,
            'UPPER_LIMIT'::character varying::text,
            'LOWER_LIMIT'::character varying::text,
            'IN_LIMIT'::character varying::text
        ])
    )
);

-- Insert values into test_factor_limit table
INSERT INTO test_factor_limit (test_factor_id, range_label, min_value, max_value, data_type)
VALUES
    ((SELECT id FROM test_factor WHERE test_condition = 'OVERALL_DATA_AVAILABILITY'), 'POOR', 0, 20, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'OVERALL_DATA_AVAILABILITY'), 'AVERAGE', 20, 80, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'OVERALL_DATA_AVAILABILITY'), 'GOOD', 80, 100, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'TCU_DATA_AVAILABILITY'), 'POOR', 0, 20, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'TCU_DATA_AVAILABILITY'), 'AVERAGE', 20, 80, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'TCU_DATA_AVAILABILITY'), 'GOOD', 80, 100, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'MOTOR_DATA_AVAILABILITY'), 'POOR', 0, 20, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'MOTOR_DATA_AVAILABILITY'), 'AVERAGE', 20, 80, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'MOTOR_DATA_AVAILABILITY'), 'GOOD', 80, 100, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'LOCATION_DATA_AVAILABILITY'), 'POOR', 0, 20, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'LOCATION_DATA_AVAILABILITY'), 'AVERAGE', 20, 80, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'LOCATION_DATA_AVAILABILITY'), 'GOOD', 80, 100, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'BATTERY_DATA_AVAILABILITY'), 'POOR', 0, 20, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'BATTERY_DATA_AVAILABILITY'), 'AVERAGE', 20, 80, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'BATTERY_DATA_AVAILABILITY'), 'GOOD', 80, 100, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'MOTOR_CURRENT_IN_RANGE'), 'UPPER_LIMIT', 100, 1000, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'MOTOR_CURRENT_IN_RANGE'), 'LOWER_LIMIT', -1000, 0, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'MOTOR_CURRENT_IN_RANGE'), 'IN_LIMIT', 0, 100, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'MOTOR_VOLTAGE_IN_RANGE'), 'UPPER_LIMIT', 84, 1000, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'MOTOR_VOLTAGE_IN_RANGE'), 'LOWER_LIMIT', 0, 64, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'MOTOR_VOLTAGE_IN_RANGE'), 'IN_LIMIT', 64, 84, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'LATEST_SOC'), 'UPPER_LIMIT', 100, 1000, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'LATEST_SOC'), 'LOWER_LIMIT', 0, 35, 'NUMBER'),
    ((SELECT id FROM test_factor WHERE test_condition = 'LATEST_SOC'), 'IN_LIMIT', 35, 100, 'NUMBER');
