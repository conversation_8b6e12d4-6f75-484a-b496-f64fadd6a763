--Continuous Aggregate for battery_cell min,max,median values of cell voltages by imei,stack_id across 1 minute window
DROP MATERIALIZED VIEW IF EXISTS battery_stack_temperature_1m_aggregate;
CREATE MATERIALIZED VIEW IF NOT EXISTS battery_stack_temperature_1m_aggregate
WITH (timescaledb.continuous) AS
select imei,
    stack_id,
    mfr_org_id,
    time_bucket ('1m',timestamp) as time_bucket,
    max(temperature) as max_temp,
    min(temperature) as min_temp,
    avg(temperature) as avg_temp,
    round(CAST(approx_percentile(0.5, percentile_agg(temperature)) AS numeric), 2) AS median_temp
from battery_stack
where timestamp >'2024-01-01'
and timestamp < now()
group by imei,stack_id,mfr_org_id,time_bucket ('1m',timestamp)  WITH NO DATA;

--Refresh the continous aggregate every minute
SELECT add_continuous_aggregate_policy('battery_stack_temperature_1m_aggregate',
  start_offset => INTERVAL '1 month',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');



