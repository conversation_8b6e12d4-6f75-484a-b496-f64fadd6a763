alter table data_frequency_plan_details
drop constraint IF EXISTS feature_name_check;

--add vehicle_running_metrics feature into data_frequency_plan_details
alter table data_frequency_plan_details
add CONSTRAINT feature_name_check CHECK (feature_name::text = ANY (ARRAY['VEHICLE_STATUS'::character varying::text, 'VEHICLE_STATUS_UPDATION'::character varying::text, 'TRIP_UPDATION'::character varying::text, 'VEHICLE_RUNNING_METRICS'::character varying::text]));

--data insertion into data_frequency_plan_details
INSERT INTO data_frequency_plan_details (feature_name, data_frequency_plan_id, status, computation_frequency, unit)
SELECT 'VEHICLE_RUNNING_METRICS', id, 'ACTIVE', 10, 'SECONDS'
FROM data_frequency_plan
WHERE name = 'High Frequency'
UNION ALL
SELECT 'VEHICLE_RUNNING_METRICS', id, 'ACTIVE', 30, 'SECONDS'
FROM data_frequency_plan
WHERE name = 'Medium Frequency'
UNION ALL
SELECT 'VEHICLE_RUNNING_METRICS', id, 'ACTIVE', 60, 'SECONDS'
FROM data_frequency_plan
WHERE name = 'Low Frequency';


-- data insertion into data_frequency_plan_details_cron_frequencies
INSERT INTO cron_frequency_data_frequency_plan_details (cron_frequency_id, data_frequency_plan_details_id)
SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'VEHICLE_RUNNING_METRICS'
  AND dfpd.computation_frequency = 10
  AND cf.cron_time IN (0, 10, 20, 30, 40, 50)
  AND cf.unit = 'SECONDS'

UNION ALL

SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'VEHICLE_RUNNING_METRICS'
  AND dfpd.computation_frequency = 30
  AND cf.cron_time IN (0, 30)
  AND cf.unit = 'SECONDS'

UNION ALL

SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'VEHICLE_RUNNING_METRICS'
  AND dfpd.computation_frequency = 60
  AND cf.cron_time IN (0)
  AND cf.unit = 'SECONDS';