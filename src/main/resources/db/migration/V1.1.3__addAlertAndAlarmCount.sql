
INSERT INTO motor_health_monitor (part_id, part_type, alarm_count, alert_count, updated_on)
--SELECT p.id, 'MCU', 0, 0, CAST('2024-09-15 17:19:00' AS timestamp with time zone)
--FROM vehicle v
--JOIN vehicle_parts vp ON v.id = vp.vehicle_id
--JOIN part p ON vp.part_id = p.id
--WHERE v.imei = '868960069670699'
--  AND p.part_type = 'MCU'
--
--UNION ALL

--SELECT p.id, 'MCU', 0, 0, CAST('2024-09-15 17:20:00' AS timestamp with time zone)
--FROM vehicle v
--JOIN vehicle_parts vp ON v.id = vp.vehicle_id
--JOIN part p ON vp.part_id = p.id
--WHERE v.imei = '861100065561264'
--  AND p.part_type = 'MCU'
--
--UNION ALL

SELECT p.id, 'MCU', 623, 2, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069652994'
  AND p.part_type = 'MCU'

UNION ALL

SELECT p.id, 'MCU', 361, 2, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069670616'
  AND p.part_type = 'MCU'

UNION ALL

SELECT p.id, 'MCU', 0, 0, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069661110'
  AND p.part_type = 'MCU'

UNION ALL

SELECT p.id, 'MCU', 1082, 0 , CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069671127'
  AND p.part_type = 'MCU'

UNION ALL

SELECT p.id, 'MCU', 79866, 500, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076615885'
  AND p.part_type = 'MCU'

UNION ALL

SELECT p.id, 'MCU', 9846, 0, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076643572'
  AND p.part_type = 'MCU'


UNION ALL

SELECT p.id, 'MCU', 2404, 358, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076660410'
  AND p.part_type = 'MCU'

UNION ALL

SELECT p.id, 'MCU', 13593, 524, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076660485'
  AND p.part_type = 'MCU'


UNION ALL

SELECT p.id, 'MCU', 0, 0, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '861100065561520'
  AND p.part_type = 'MCU'

UNION ALL

SELECT p.id, 'MCU', 0, 0, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076700851'
  AND p.part_type = 'MCU'

UNION ALL

SELECT p.id, 'MCU', 0, 0, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069670533'
  AND p.part_type = 'MCU';


---------------------------------

INSERT INTO motor_health_monitor (part_id, part_type, alarm_count, alert_count, updated_on)
--SELECT p.id, 'MOTOR', 0, 0, CAST('2024-09-15 17:19:00' AS timestamp with time zone)
--FROM vehicle v
--JOIN vehicle_parts vp ON v.id = vp.vehicle_id
--JOIN part p ON vp.part_id = p.id
--WHERE v.imei = '868960069670699'
--  AND p.part_type = 'MOTOR'
--
--UNION ALL

--SELECT p.id, 'MOTOR', 0, 0, CAST('2024-09-15 17:20:00' AS timestamp with time zone)
--FROM vehicle v
--JOIN vehicle_parts vp ON v.id = vp.vehicle_id
--JOIN part p ON vp.part_id = p.id
--WHERE v.imei = '861100065561264'
--  AND p.part_type = 'MOTOR'
--
--UNION ALL

SELECT p.id, 'MOTOR', 0, 2377, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069652994'
  AND p.part_type = 'MOTOR'

UNION ALL

SELECT p.id, 'MOTOR', 0, 1506, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069670616'
  AND p.part_type = 'MOTOR'

UNION ALL

SELECT p.id, 'MOTOR', 0, 0, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069661110'
  AND p.part_type = 'MOTOR'

UNION ALL

SELECT p.id, 'MOTOR', 0, 2584 , CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069671127'
  AND p.part_type = 'MOTOR'

UNION ALL

SELECT p.id, 'MOTOR', 0, 4485, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076615885'
  AND p.part_type = 'MOTOR'

UNION ALL

SELECT p.id, 'MOTOR', 0, 6492, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076643572'
  AND p.part_type = 'MOTOR'


UNION ALL

SELECT p.id, 'MOTOR', 0, 7148, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076660410'
  AND p.part_type = 'MOTOR'

UNION ALL

SELECT p.id, 'MOTOR', 0, 3927, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076660485'
  AND p.part_type = 'MOTOR'


UNION ALL

SELECT p.id, 'MOTOR', 0, 0, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '861100065561520'
  AND p.part_type = 'MOTOR'

UNION ALL

SELECT p.id, 'MOTOR', 0, 64, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076700851'
  AND p.part_type = 'MOTOR'

UNION ALL

SELECT p.id, 'MOTOR', 0, 0, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069670533'
  AND p.part_type = 'MOTOR';

--battery--
INSERT INTO battery_health_monitor (part_id, part_type, alarm_count, alert_count, updated_on)
SELECT p.id, 'BATTERY', 0, 5525, CAST('2024-09-15 17:19:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069670616'
  AND p.part_type = 'BATTERY'

UNION ALL

SELECT p.id, 'BATTERY', 0, 2781, CAST('2024-09-15 17:20:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069652994'
  AND p.part_type = 'BATTERY'

UNION ALL

SELECT p.id, 'BATTERY', 0, 6059, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '868960069671127'
  AND p.part_type = 'BATTERY'

UNION ALL

SELECT p.id, 'BATTERY', 38783, 25548, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076660410'
  AND p.part_type = 'BATTERY'

UNION ALL

SELECT p.id, 'BATTERY', 7461, 13290, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076643572'
  AND p.part_type = 'BATTERY'

UNION ALL

SELECT p.id, 'BATTERY', 29416, 17031 , CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076615885'
  AND p.part_type = 'BATTERY'

UNION ALL

SELECT p.id, 'BATTERY', 19406, 11685, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076660485'
  AND p.part_type = 'BATTERY'

UNION ALL

SELECT p.id, 'BATTERY', 264, 196, CAST('2024-09-15 17:21:00' AS timestamp with time zone)
FROM vehicle v
JOIN vehicle_parts vp ON v.id = vp.vehicle_id
JOIN part p ON vp.part_id = p.id
WHERE v.imei = '867461076700851'
  AND p.part_type = 'BATTERY'







