-- drop materialized view before drop
DROP MATERIALIZED VIEW battery_stack_temperature_1m_aggregate;

CREATE MATERIALIZED VIEW IF NOT EXISTS battery_stack_temperature_1m_aggregate
WITH (timescaledb.continuous) AS
SELECT
    bs.imei AS imei,
    bs.vehicle_id AS vehicle_id,
    bs.stack_id AS stack_id,
    bs.mfr_org_id AS mfr_org_id,
    time_bucket('1m', bs.timestamp) AS bucket,
    MAX(bs.temperature) AS max_temp,
    MIN(bs.temperature) AS min_temp,
    AVG(bs.temperature) AS avg_temp,
    ROUND(CAST(approx_percentile(0.5, percentile_agg(bs.temperature)) AS numeric), 2) AS median_temp,
    FIRST(bs.temperature, bs.timestamp) FILTER (WHERE bs.temperature IS NOT NULL) AS temperature
FROM battery_stack AS bs
GROUP BY
    bs.imei,
    bs.vehicle_id,
    bs.stack_id,
    bs.mfr_org_id,
    time_bucket('1m', bs.timestamp)
WITH NO DATA;


SELECT add_continuous_aggregate_policy('battery_stack_temperature_1m_aggregate',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');


CREATE MATERIALIZED VIEW IF NOT EXISTS battery_stack_temperature_10m_aggregate
WITH (timescaledb.continuous) AS
SELECT
    bs1m.imei AS imei,
    bs1m.vehicle_id AS vehicle_id,
    bs1m.stack_id AS stack_id,
    bs1m.mfr_org_id AS mfr_org_id,
    time_bucket('10m', bs1m.bucket) AS bucket,
    MAX(bs1m.max_temp) AS max_temp,
    MIN(bs1m.min_temp) AS min_temp,
    AVG(bs1m.avg_temp) AS avg_temp,
    ROUND(CAST(approx_percentile(0.5, percentile_agg(bs1m.median_temp)) AS numeric), 2) AS median_temp,
    FIRST(bs1m.temperature, bs1m.bucket) FILTER (WHERE bs1m.temperature IS NOT NULL) AS temperature
FROM battery_stack_temperature_1m_aggregate bs1m
GROUP BY
    bs1m.imei,
    bs1m.vehicle_id,
    bs1m.stack_id,
    bs1m.mfr_org_id,
    time_bucket('10m', bs1m.bucket)
WITH NO DATA;

SELECT add_continuous_aggregate_policy('battery_stack_temperature_10m_aggregate',
   start_offset => INTERVAL '1 day',
   end_offset => INTERVAL '1 minute',
   schedule_interval => INTERVAL '10 minute');


CREATE MATERIALIZED VIEW IF NOT EXISTS battery_stack_temperature_1h_aggregate
WITH (timescaledb.continuous) AS
SELECT
    bs10m.imei AS imei,
    bs10m.vehicle_id AS vehicle_id,
    bs10m.stack_id AS stack_id,
    bs10m.mfr_org_id AS mfr_org_id,
    time_bucket('1 hour', bs10m.bucket) AS bucket,
    MAX(bs10m.max_temp) AS max_temp,
    MIN(bs10m.min_temp) AS min_temp,
    AVG(bs10m.avg_temp) AS avg_temp,
    ROUND(CAST(approx_percentile(0.5, percentile_agg(bs10m.median_temp)) AS numeric), 2) AS median_temp,
    FIRST(bs10m.temperature, bs10m.bucket) FILTER (WHERE bs10m.temperature IS NOT NULL) AS temperature
FROM battery_stack_temperature_10m_aggregate bs10m
GROUP BY
    bs10m.imei,
    bs10m.vehicle_id,
    bs10m.stack_id,
    bs10m.mfr_org_id,
    time_bucket('1 hour', bs10m.bucket)
WITH NO DATA;

SELECT add_continuous_aggregate_policy('battery_stack_temperature_1h_aggregate',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 hour');
