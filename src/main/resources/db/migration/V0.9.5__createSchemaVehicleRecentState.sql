-- Create the vehicle_latest_data table
CREATE TABLE vehicle_latest_data (
    id SERIAL PRIMARY KEY,
    vehicle_id INTEGER UNIQUE,
    imei VARCHAR(255) UNIQUE,
    location_updated_at TIMESTAMP,
    latitude FLOAT,
    longitude FLOAT,
    cal_odometer_updated_at TIMESTAMP,
    cal_odometer FLOAT,
    soc_updated_at TIMESTAMP,
    soc FLOAT,
    CONSTRAINT fk_vehicle_id
        FOREIGN KEY(vehicle_id)
        REFERENCES vehicle(id)
);

-- Add unique constraint on imei
ALTER TABLE vehicle_latest_data
    ADD CONSTRAINT unique_imei UNIQUE (imei);

-- Insert data from the vehicle table into the vehicle_latest_data table
INSERT INTO vehicle_latest_data (vehicle_id, imei, location_updated_at, latitude, longitude, cal_odometer_updated_at, cal_odometer)
SELECT id, imei, location_updated_at, latitude, longitude, odometer_last_updated_at, total_distance_traveled
FROM vehicle