
-- set chunk time interval for imu hypertables
SELECT set_chunk_time_interval('vehicle_imu_data_raw', INTERVAL '7 days');

--set retention policy for imu hypertables
SELECT add_retention_policy('vehicle_imu_data_raw', INTERVAL '1 months');
-- enable compression for imu hypertables
ALTER TABLE vehicle_imu_data_raw
SET (
	timescaledb.compress,
	timescaledb.compress_segmentby = 'imei,vehicle_id,mfr_org_id,owner_org_id',
	timescaledb.compress_orderby='timestamp'
);

-- enable compression policies for imu hypertables
SELECT add_compression_policy('vehicle_imu_data_raw', INTERVAL '1 days');
