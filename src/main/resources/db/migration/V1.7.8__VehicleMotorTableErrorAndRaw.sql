-- create new table vehicle_motor_data
CREATE TABLE evdata.vehicle_motor_data_error (
    motor_id BIGINT NOT NULL,
    motor_brake BOOLEAN,
    motor_cruise BOOLEAN,
    motor_dc_current FLOAT4,
    motor_dc_voltage FLOAT4,
    motor_mcs_temperature FLOAT4,
    motor_parking_sign <PERSON><PERSON><PERSON><PERSON><PERSON>,
    motor_ready_sign <PERSON><PERSON><PERSON><PERSON><PERSON>,
    motor_regeneration BOOLEAN,
    motor_reverse BOOLEAN,
    motor_side_stand BOOLEAN,
    motor_speed FLOAT4,
    motor_temperature FLOAT4,
    motor_throttle FLOAT4,
    motor_driving_mode varchar(255) check (motor_driving_mode in ('ECO','POWER','CITY','REVERSE','NULL_DRIVE_SELECTION','MODE_S','MODE_M','MODE_H')),
    motor_fault_feedback VARCHAR(255),
    timestamp timestamp(6) with time zone NOT NULL,
    imei TEXT NOT NULL,
    vehicle_id BIGINT NOT NULL,
    created_on timestamp(6) with time zone,
    mfr_org_id BIGINT,
    owner_org_id BIGINT,
    packet_received_on timestamp(6) with time zone,
    co_relation_id UUID,
    di_ignition BOOLEAN,
    di_motion BOOLEAN
);


DO $$
DECLARE
    start_date TIMESTAMP;
    end_date TIMESTAMP;
    current_start TIMESTAMP;
    current_end TIMESTAMP;
BEGIN
    -- Get date range
    SELECT MIN(timestamp), MAX(timestamp)
    INTO start_date, end_date
    FROM evdata.vehicle_telemetry_data_error;

    -- Initialize batch range
    current_start := start_date;
    current_end := start_date + INTERVAL '3 days';

    -- Loop through date range in 3-day batches
    WHILE current_start <= end_date LOOP
        -- Insert batch
        INSERT INTO evdata.vehicle_motor_data_error (
            motor_id,
            motor_brake,
            motor_cruise,
            motor_dc_current,
            motor_dc_voltage,
            motor_mcs_temperature,
            motor_parking_sign,
            motor_ready_sign,
            motor_regeneration,
            motor_reverse,
            motor_side_stand,
            motor_speed,
            motor_temperature,
            motor_throttle,
            motor_driving_mode,
            motor_fault_feedback,
            timestamp,
            imei,
            vehicle_id,
            created_on,
            mfr_org_id,
            owner_org_id,
            packet_received_on,
            co_relation_id,
            di_ignition,
            di_motion
        )
        SELECT
            p.id AS motor_id,
            vtd.motor_brake,
            vtd.motor_cruise,
            vtd.motor_dc_current,
            vtd.motor_dc_voltage,
            vtd.motor_mcs_temperature,
            vtd.motor_parking_sign,
            vtd.motor_ready_sign,
            vtd.motor_regeneration,
            vtd.motor_reverse,
            vtd.motor_side_stand,
            vtd.motor_speed,
            vtd.motor_temperature,
            vtd.motor_throttle,
            vtd.motor_driving_mode,
            vtd.motor_fault_feedback,
            vtd.timestamp,
            vtd.imei,
            vtd.vehicle_id,
            vtd.created_on,
            vtd.mfr_org_id,
            vtd.owner_org_id,
            vtd.packet_received_on,
            vtd.co_relation_id,
            di_ignition,
            di_motion
        FROM
            evdata.vehicle_telemetry_data_error vtd
            JOIN evdata.vehicle_parts vp ON vtd.vehicle_id = vp.vehicle_id
            JOIN evdata.part p ON vp.part_id = p.id
        WHERE
            p.part_type = 'MOTOR'
            AND vtd.timestamp >= current_start
            AND vtd.timestamp < current_end;

        -- Move to next 3-day batch
        current_start := current_end;
        current_end := current_end + INTERVAL '3 days';

    END LOOP;
END $$;

-- create constraints
ALTER TABLE evdata.vehicle_motor_data_error
ADD CONSTRAINT vehicle_motor_data_error_pk PRIMARY KEY (timestamp, imei, motor_id);

ALTER TABLE evdata.vehicle_motor_data_error
ADD CONSTRAINT vehicle_motor_data_error_motor_fk FOREIGN KEY (motor_id)
REFERENCES evdata.part (id);

ALTER TABLE evdata.vehicle_motor_data_error
ADD CONSTRAINT vehicle_motor_data_error_mfr_org_fk FOREIGN KEY (mfr_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_motor_data_error
ADD CONSTRAINT vehicle_motor_data_error_owner_org_fk FOREIGN KEY (owner_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_motor_data_error
ADD CONSTRAINT vehicle_motor_data_error_vehicle_fk FOREIGN KEY (vehicle_id)
REFERENCES evdata.vehicle (id);


CREATE INDEX vehicle_motor_data_error_imei_idx ON evdata.vehicle_motor_data_error (imei);
CREATE INDEX vehicle_motor_data_error_id_idx ON evdata.vehicle_motor_data_error (motor_id, imei, timestamp);
CREATE INDEX vehicle_motor_data_error_ts_idx ON evdata.vehicle_motor_data_error (timestamp);

ALTER TABLE evdata.vehicle_telemetry_data_error
    DROP COLUMN motor_brake,
    DROP COLUMN motor_cruise,
    DROP COLUMN motor_dc_current,
    DROP COLUMN motor_dc_voltage,
    DROP COLUMN motor_mcs_temperature,
    DROP COLUMN motor_parking_sign,
    DROP COLUMN motor_ready_sign,
    DROP COLUMN motor_regeneration,
    DROP COLUMN motor_reverse,
    DROP COLUMN motor_side_stand,
    DROP COLUMN motor_speed,
    DROP COLUMN motor_temperature,
    DROP COLUMN motor_throttle,
    DROP COLUMN motor_driving_mode,
    DROP COLUMN motor_fault_feedback;

ALTER TABLE evdata.vehicle_telemetry_data_raw
    DROP COLUMN motor_brake,
    DROP COLUMN motor_cruise,
    DROP COLUMN motor_dc_current,
    DROP COLUMN motor_dc_voltage,
    DROP COLUMN motor_mcs_temperature,
    DROP COLUMN motor_parking_sign,
    DROP COLUMN motor_ready_sign,
    DROP COLUMN motor_regeneration,
    DROP COLUMN motor_reverse,
    DROP COLUMN motor_side_stand,
    DROP COLUMN motor_speed,
    DROP COLUMN motor_temperature,
    DROP COLUMN motor_throttle,
    DROP COLUMN motor_driving_mode,
    DROP COLUMN motor_fault_feedback;

