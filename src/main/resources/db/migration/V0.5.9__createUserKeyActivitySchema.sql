CREATE SEQUENCE IF NOT EXISTS user_key_activity_seq START WITH 1 INCREMENT BY 50;

CREATE TABLE IF NOT EXISTS user_key_activity (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    activity_type VARCHAR(255),
    value VARCHAR(255),
    date TIMES<PERSON>MP WITHOUT TIME ZONE);

ALTER TABLE IF EXISTS user_key_activity ADD CONSTRAINT fk_user_key_activity_vehicle_id FOREIGN KEY (user_id) REFERENCES users;


--adding column into user_key_activity
ALTER TABLE user_key_activity
ADD COLUMN org_id BIGINT;

ALTER TABLE IF EXISTS setting ADD CONSTRAINT fk_user_key_activity_org_id FOREIGN KEY (organisation_id) REFERENCES organisations;

CREATE INDEX idx_user_key_activity_org_id_date_desc ON user_key_activity (user_id, activity_type,org_id, date DESC);


