DROP MATERIALIZED VIEW IF EXISTS evdata.vehicle_imu_first_aggregate_1d;
DROP MATERIALIZED VIEW IF EXISTS evdata.vehicle_imu_first_aggregate_1h;
DROP MATERIALIZED VIEW IF EXISTS evdata.vehicle_imu_first_aggregate_10m;
DROP MATERIALIZED VIEW IF EXISTS evdata.vehicle_imu_first_aggregate_1m;


CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_imu_first_aggregate_1m
WITH (timescaledb.continuous) AS
SELECT
    vid.vehicle_id AS vehicle_id,
    vid.imei AS imei,
    vid.mfr_org_id AS mfr_org_id,
    vid.owner_org_id AS owner_org_id,
    time_bucket('1m', vid.timestamp) AS bucket,
    first(vid.ai_lean_angle, vid.timestamp) FILTER (WHERE vid.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vid.accel_x_axis, vid.timestamp) FILTER (WHERE vid.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vid.accel_y_axis, vid.timestamp) FILTER (WHERE vid.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vid.accel_z_axis, vid.timestamp) FILTER (WHERE vid.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vid.grv_x_axis, vid.timestamp) FILTER (WHERE vid.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vid.grv_y_axis, vid.timestamp) FILTER (WHERE vid.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vid.grv_z_axis, vid.timestamp) FILTER (WHERE vid.grv_z_axis IS NOT NULL) AS grv_z_axis,
    first(vid.gyro_x_axis, vid.timestamp) FILTER (WHERE vid.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vid.gyro_y_axis, vid.timestamp) FILTER (WHERE vid.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vid.gyro_z_axis, vid.timestamp) FILTER (WHERE vid.gyro_z_axis IS NOT NULL) AS gyro_z_axis,
    first(vid.di_ignition, vid.timestamp) FILTER (WHERE vid.di_ignition IS NOT NULL) AS di_ignition,
    first(vid.di_motion, vid.timestamp) FILTER (WHERE vid.di_motion IS NOT NULL) AS di_motion
FROM evdata.vehicle_imu_data vid
GROUP BY
    time_bucket('1m', vid.timestamp),
    vid.vehicle_id,
    vid.imei,
    vid.mfr_org_id,
    vid.owner_org_id
WITH NO DATA;

--Refresh the vehicle_imu_first_aggregate_1m every minute
SELECT add_continuous_aggregate_policy('evdata.vehicle_imu_first_aggregate_1m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');


--Continuous Aggregate for vehicle_imu_data across 10 minutes window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_imu_first_aggregate_10m
WITH (timescaledb.continuous) AS
SELECT
    vid.vehicle_id AS vehicle_id,
    vid.imei AS imei,
    vid.mfr_org_id AS mfr_org_id,
    vid.owner_org_id AS owner_org_id,
    time_bucket('10m', vid.timestamp) AS bucket,
    first(vid.ai_lean_angle, vid.timestamp) FILTER (WHERE vid.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vid.accel_x_axis, vid.timestamp) FILTER (WHERE vid.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vid.accel_y_axis, vid.timestamp) FILTER (WHERE vid.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vid.accel_z_axis, vid.timestamp) FILTER (WHERE vid.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vid.grv_x_axis, vid.timestamp) FILTER (WHERE vid.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vid.grv_y_axis, vid.timestamp) FILTER (WHERE vid.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vid.grv_z_axis, vid.timestamp) FILTER (WHERE vid.grv_z_axis IS NOT NULL) AS grv_z_axis,
    first(vid.gyro_x_axis, vid.timestamp) FILTER (WHERE vid.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vid.gyro_y_axis, vid.timestamp) FILTER (WHERE vid.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vid.gyro_z_axis, vid.timestamp) FILTER (WHERE vid.gyro_z_axis IS NOT NULL) AS gyro_z_axis,
    first(vid.di_ignition, vid.timestamp) FILTER (WHERE vid.di_ignition IS NOT NULL) AS di_ignition,
    first(vid.di_motion, vid.timestamp) FILTER (WHERE vid.di_motion IS NOT NULL) AS di_motion
FROM evdata.vehicle_imu_data vid
GROUP BY
    time_bucket('10m', vid.timestamp),
    vid.vehicle_id,
    vid.imei,
    vid.mfr_org_id,
    vid.owner_org_id
WITH NO DATA;

--Refresh the vehicle_imu_first_aggregate_1m every 10 minutes
SELECT add_continuous_aggregate_policy('evdata.vehicle_imu_first_aggregate_10m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '10 minute');


--Continuous Aggregate for vehicle_imu_data across 1 hour window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_imu_first_aggregate_1h
WITH (timescaledb.continuous) AS
SELECT
    vid.vehicle_id AS vehicle_id,
    vid.imei AS imei,
    vid.mfr_org_id AS mfr_org_id,
    vid.owner_org_id AS owner_org_id,
    time_bucket('1h', vid.timestamp) AS bucket,
    first(vid.ai_lean_angle, vid.timestamp) FILTER (WHERE vid.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vid.accel_x_axis, vid.timestamp) FILTER (WHERE vid.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vid.accel_y_axis, vid.timestamp) FILTER (WHERE vid.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vid.accel_z_axis, vid.timestamp) FILTER (WHERE vid.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vid.grv_x_axis, vid.timestamp) FILTER (WHERE vid.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vid.grv_y_axis, vid.timestamp) FILTER (WHERE vid.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vid.grv_z_axis, vid.timestamp) FILTER (WHERE vid.grv_z_axis IS NOT NULL) AS grv_z_axis,
    first(vid.gyro_x_axis, vid.timestamp) FILTER (WHERE vid.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vid.gyro_y_axis, vid.timestamp) FILTER (WHERE vid.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vid.gyro_z_axis, vid.timestamp) FILTER (WHERE vid.gyro_z_axis IS NOT NULL) AS gyro_z_axis,
    first(vid.di_ignition, vid.timestamp) FILTER (WHERE vid.di_ignition IS NOT NULL) AS di_ignition,
    first(vid.di_motion, vid.timestamp) FILTER (WHERE vid.di_motion IS NOT NULL) AS di_motion
FROM evdata.vehicle_imu_data vid
GROUP BY
    time_bucket('1h', vid.timestamp),
    vid.vehicle_id,
    vid.imei,
    vid.mfr_org_id,
    vid.owner_org_id
WITH NO DATA;

--Refresh the vehicle_imu_first_aggregate_1h every hour
SELECT add_continuous_aggregate_policy('evdata.vehicle_imu_first_aggregate_1h',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 hour');

--Continuous Aggregate for vehicle_imu_data across 1 day window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_imu_first_aggregate_1d
WITH (timescaledb.continuous) AS
SELECT
    vid.vehicle_id AS vehicle_id,
    vid.imei AS imei,
    vid.mfr_org_id AS mfr_org_id,
    vid.owner_org_id AS owner_org_id,
    time_bucket('1d', vid.timestamp) AS bucket,
    first(vid.ai_lean_angle, vid.timestamp) FILTER (WHERE vid.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vid.accel_x_axis, vid.timestamp) FILTER (WHERE vid.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vid.accel_y_axis, vid.timestamp) FILTER (WHERE vid.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vid.accel_z_axis, vid.timestamp) FILTER (WHERE vid.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vid.grv_x_axis, vid.timestamp) FILTER (WHERE vid.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vid.grv_y_axis, vid.timestamp) FILTER (WHERE vid.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vid.grv_z_axis, vid.timestamp) FILTER (WHERE vid.grv_z_axis IS NOT NULL) AS grv_z_axis,
    first(vid.gyro_x_axis, vid.timestamp) FILTER (WHERE vid.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vid.gyro_y_axis, vid.timestamp) FILTER (WHERE vid.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vid.gyro_z_axis, vid.timestamp) FILTER (WHERE vid.gyro_z_axis IS NOT NULL) AS gyro_z_axis,
    first(vid.di_ignition, vid.timestamp) FILTER (WHERE vid.di_ignition IS NOT NULL) AS di_ignition,
    first(vid.di_motion, vid.timestamp) FILTER (WHERE vid.di_motion IS NOT NULL) AS di_motion
FROM evdata.vehicle_imu_data vid
GROUP BY
    time_bucket('1d', vid.timestamp),
    vid.vehicle_id,
    vid.imei,
    vid.mfr_org_id,
    vid.owner_org_id
WITH NO DATA;

--Refresh the vehicle_imu_first_aggregate_1d every day
SELECT add_continuous_aggregate_policy('evdata.vehicle_imu_first_aggregate_1d',
  start_offset => INTERVAL '3 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 day');