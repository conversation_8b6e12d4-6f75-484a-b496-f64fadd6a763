-- create new table vehicle_motor_data
CREATE TABLE evdata.vehicle_motor_data (
    motor_id BIGINT NOT NULL,
    motor_brake BOOLEAN,
    motor_cruise BOOLEAN,
    motor_dc_current FLOAT4,
    motor_dc_voltage FLOAT4,
    motor_mcs_temperature FLOAT4,
    motor_parking_sign <PERSON><PERSON><PERSON><PERSON><PERSON>,
    motor_ready_sign <PERSON><PERSON><PERSON><PERSON><PERSON>,
    motor_regeneration BOOLEAN,
    motor_reverse BOOLEAN,
    motor_side_stand BOOLEAN,
    motor_speed FLOAT4,
    motor_temperature FLOAT4,
    motor_throttle FLOAT4,
    motor_driving_mode varchar(255) check (motor_driving_mode in ('ECO','POWER','CITY','REVERSE','NULL_DRIVE_SELECTION','MODE_S','MODE_M','MODE_H')),
    motor_fault_feedback VARCHAR(255),
    timestamp timestamp(6) with time zone NOT NULL,
    imei TEXT NOT NULL,
    vehicle_id BIGINT NOT NULL,
    created_on timestamp(6) with time zone,
    mfr_org_id BIGINT,
    owner_org_id BIGINT,
    packet_received_on timestamp(6) with time zone,
    co_relation_id UUID,
    di_motion BOOLEAN,
    di_ignition BOOLEAN
);


