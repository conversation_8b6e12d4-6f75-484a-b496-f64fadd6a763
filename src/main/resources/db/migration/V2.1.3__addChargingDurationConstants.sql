-- Add constraints
ALTER TABLE event_duration_constants
    DROP CONSTRAINT IF EXISTS trip_constant_check,
    ADD CONSTRAINT event_constant_check check( event_constant in ('STOP_DURATION','MIN_TRIP_DURATION','CHARGE_START_DURATION','CHARGE_STOP_DURATION'));

 --insert default values for charging constants
INSERT INTO event_duration_constants (id,event_constant, event_type, org_id, duration, unit)
VALUES
    (nextval('trip_duration_constants_seq'),'CHARGE_START_DURATION', 'CHARGING', 2, 60, 'seconds'),
    (nextval('trip_duration_constants_seq'),'CHARGE_STOP_DURATION', 'CHARGING', 2, 600, 'seconds');