ALTER TABLE part
DROP CONSTRAINT IF EXISTS part_part_type_check;

UPDATE part
SET part_type = 'REAR_TYRE'
WHERE part_type = 'WHEEL';

ALTER TABLE part
ADD CONSTRAINT part_part_type_check CHECK((part_type in ('BATTERY','CHARGER','MOTOR','COLOR','HORN','MIRROR','SHOCK_FRONT','SHOCK_REAR','STAND','SAREE_GUARD','GPS','ACCELEROMETER','MCS','IO','DRIVE_MODE','TCU','CHASSIS','BMS','HS_DATA_LOGGER','VEHICLE','FRONT_TYRE','REAR_TYRE','MCU','TCU_FIRMWARE','MCU_FIRMWARE','BMS_SOFTWARE','DC_DC_CONVERTER')));

ALTER TABLE part_model
DROP CONSTRAINT IF EXISTS part_model_part_type_check;

UPDATE part_model
SET part_type = 'REAR_TYRE'
WHERE part_type = 'WHEEL';

ALTER TABLE part_model
ADD CONSTRAINT part_model_part_type_check CHECK((part_type in ('BATTERY','CHARGER','MOTOR','COLOR','HORN','MIRROR','SHOCK_FRONT','SHOCK_REAR','STAND','SAREE_GUARD','GPS','ACCELEROMETER','MCS','IO','DRIVE_MODE','TCU','CHASSIS','BMS','HS_DATA_LOGGER','VEHICLE','FRONT_TYRE','REAR_TYRE','MCU','TCU_FIRMWARE','MCU_FIRMWARE','BMS_SOFTWARE','DC_DC_CONVERTER')));

ALTER TABLE wheel_model RENAME TO rear_tyre_model;