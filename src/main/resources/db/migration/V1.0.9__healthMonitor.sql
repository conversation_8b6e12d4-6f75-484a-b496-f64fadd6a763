CREATE TABLE motor_health_monitor (
    part_id BIGINT PRIMARY KEY,
    part_type VARCHAR(255),
    alarm_count INTEGER,
    alert_count INTEGER,
    updated_on TIMESTAMP(6) WITH TIME ZONE,
    status VARCHAR(255),
    CONSTRAINT monitor_health_part_type_check CHECK (part_type IN ('MOTOR', 'MCU', 'MCS')),
    CONSTRAINT monitor_health_status_check CHECK (status IN ('ACTIVE', 'INACTIVE', 'DELETED'))
);

CREATE INDEX idx_motor_health_monitor_updated_on ON motor_health_monitor(updated_on);

CREATE INDEX idx_motor_health_monitor_part_type ON motor_health_monitor(part_type);


CREATE TABLE battery_health_monitor (
    part_id BIGINT PRIMARY KEY,
    part_type VARCHAR(255),
    alarm_count INTEGER,
    alert_count INTEGER,
    updated_on TIMESTAMP(6) WITH TIME ZONE,
    status VARCHAR(255),
    CONSTRAINT monitor_health_part_type_check CHECK (part_type IN ('BATTERY','BMS')),
    CONS<PERSON>AINT monitor_health_status_check CHECK (status IN ('ACTIVE','INACTIVE','DELETED'))
);

CREATE INDEX idx_battery_health_monitor_updated_on ON battery_health_monitor(updated_on);

CREATE INDEX idx_battery_health_monitor_part_type ON battery_health_monitor(part_type);


CREATE SEQUENCE IF NOT EXISTS part_model_alert_limits_seq start with 1 increment by 50;

CREATE TABLE part_model_alert_limits(
	id INTEGER PRIMARY KEY DEFAULT nextval('part_model_alert_limits_seq'),
	alert_type VARCHAR(255),
	part_model_id BIGINT NOT NULL,
	min_count INTEGER,
	max_count INTEGER,
	created_on TIMESTAMP(6) WITH TIME ZONE,
	remark_value INTEGER,
    CONSTRAINT fk_part_model_id FOREIGN KEY (part_model_id) REFERENCES part_model (id),
	CONSTRAINT uq_alert_type_part_model_id_remark_value UNIQUE (alert_type, part_model_id, remark_value)
);






