
--alter constraint check on aggregate_name table
ALTER TABLE aggregate_name DROP CONSTRAINT IF EXISTS type_check;
ALTER TABLE aggregate_name ADD CONSTRAINT type_check CHECK ((type in ('TELEMETRY', 'BATTERY', 'LOCATION', 'BATTERY_STACK', 'BATTERY_CELL', 'BATTERY_ALARM', 'MOTOR')));

INSERT INTO aggregate_name (name,type)
VALUES
('vehicle_status_motor_10sec_aggregate','MOTOR'),
('vehicle_status_motor_30sec_aggregate','MOTOR'),
('vehicle_status_motor_1min_aggregate','MOTOR');

insert into data_frequency_plan_details_aggregate_names (data_frequency_plan_details_id,aggregate_name_id)
select dfpd.id,an.id
from data_frequency_plan_details dfpd, aggregate_name an
where dfpd.feature_name in('VEHICLE_STATUS') and computation_frequency=10 and an.name in ('vehicle_status_motor_10sec_aggregate')
UNION ALL
select dfpd.id,an.id
from data_frequency_plan_details dfpd, aggregate_name an
where dfpd.feature_name in('VEHICLE_STATUS') and computation_frequency=30 and an.name in ('vehicle_status_motor_30sec_aggregate')
UNION ALL
select dfpd.id,an.id
from data_frequency_plan_details dfpd, aggregate_name an
where dfpd.feature_name in('VEHICLE_STATUS') and computation_frequency=60 and an.name in ('vehicle_status_motor_1min_aggregate');