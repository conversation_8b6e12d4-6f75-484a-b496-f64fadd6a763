-- create sequence for the vehicle_test_duration table

CREATE SEQUENCE IF NOT EXISTS vehicle_test_duration_seq start with 1 increment by 1;

-- create table vehicle_test_duration

CREATE TABLE IF NOT EXISTS vehicle_test_duration (
    id BIGINT PRIMARY KEY DEFAULT nextval('vehicle_test_duration_seq'),
    test_type_name VARCHAR(255) check (test_type_name in ('CONNECTIVITY','DYNO','TEST_RIDE')),
    data_frequency_plan_id BIGINT,
    duration INTEGER,
    unit VARCHAR(255),
    FOREIGN KEY (data_frequency_plan_id) REFERENCES data_frequency_plan(id)
);

--inserting the data to the table vehicle_test_duration according to the data frequency plan

INSERT INTO vehicle_test_duration (test_type_name,data_frequency_plan_id,duration ,unit )
VALUES ('CONNECTIVITY',(select id from data_frequency_plan where name ='High Frequency') ,60 ,'seconds'),
('CONNECTIVITY',(select id from data_frequency_plan where name ='Medium Frequency') , 180,'seconds'),
('CONNECTIVITY',(select id from data_frequency_plan where name ='Low Frequency') ,300 ,'seconds')

