--drop the unused views
DROP MATERIALIZED VIEW IF EXISTS  view_vehicle_telemetry_aggregate_data;
DROP MATERIALIZED VIEW IF EXISTS  view_battery_sensor_aggregate_data;

--create view_vehicle_telemetry_aggregate_data view
CREATE MATERIALIZED VIEW IF NOT EXISTS view_vehicle_motor_aggregate_data
WITH (timescaledb.continuous) AS
select
vtd.imei as imei,
vtd.mfr_org_id as mfr_org_id,
vtd.owner_org_id as owner_org_id,
time_bucket('2m',vtd.timestamp) as bucket,
round(avg(vtd.motor_dc_voltage)::numeric,2) as avg_motor_dc_voltage,
round(avg(vtd.motor_dc_current)::numeric,2) as avg_motor_dc_current,
round(avg(vtd.motor_temperature)::numeric,2) as avg_motor_temperature,
round(avg(vtd.motor_mcs_temperature)::numeric,2) as avg_motor_mcs_temperature,
round(max(vtd.motor_dc_voltage)::numeric, 2) AS max_motor_dc_voltage,
round(max(vtd.motor_dc_current)::numeric, 2) AS max_motor_dc_current,
round(max(vtd.motor_temperature)::numeric, 2) AS max_motor_temperature,
round(max(vtd.motor_mcs_temperature)::numeric, 2) AS max_motor_mcs_temperature,
round(min(vtd.motor_dc_voltage)::numeric, 2) AS min_motor_dc_voltage,
round(min(vtd.motor_dc_current)::numeric, 2) AS min_motor_dc_current,
round(min(vtd.motor_temperature)::numeric, 2) AS min_motor_temperature,
round(min(vtd.motor_mcs_temperature)::numeric, 2) AS min_motor_mcs_temperature
from vehicle_telemetry_data vtd
where vtd.timestamp >'2024-01-01'
group by bucket,
vtd.imei,
vtd.mfr_org_id,
vtd.owner_org_id,
time_bucket('2m',timestamp) WITH NO DATA;

--adding aggregate policy for view_vehicle_telemetry_aggregate_data
SELECT add_continuous_aggregate_policy('view_vehicle_telemetry_aggregate_data',
 start_offset => INTERVAL '1 day',
 end_offset => INTERVAL '2 minutes',
 schedule_interval => INTERVAL '1 minute');

--creating view_battery_sensor_aggregate_data view
 CREATE MATERIALIZED VIEW IF NOT EXISTS view_battery_sensor_aggregate_data
 WITH (timescaledb.continuous) AS
 select
 bs.imei as imei,
 bs.mfr_org_id as mfr_org_id,
 bs.owner_org as owner_org,
 bs.stack_id as stack_id,
 time_bucket('2m',bs.timestamp) as bucket,
 round(avg(bs.temperature)::numeric,2) as avg_temperature,
 round(max(bs.temperature)::numeric,2) as max_temperature,
 round(min(bs.temperature)::numeric,2) as min_temperature
 from battery_stack bs
 where bs.timestamp > '2024-01-01'
 group by bucket,
 bs.imei,
 bs.mfr_org_id,
 bs.owner_org,
 bs.stack_id,
 time_bucket('2m',timestamp) WITH NO DATA;

 --adding aggregate policy for view_battery_sensor_aggregate_data
 SELECT add_continuous_aggregate_policy('view_battery_sensor_aggregate_data',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '2 minutes',
  schedule_interval => INTERVAL '1 minute');