--
--CREATE OR REPLACE VIEW _timescaledb_internal._direct_view_669 AS
--SELECT vtd.imei,
--time_bucket('1 day'::interval, vtd."timestamp") AS bucket,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_x_axis::double precision)) AS grv_x_still,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_y_axis::double precision)) AS grv_y_still,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_z_axis::double precision)) AS grv_z_still
--FROM evdata.vehicle_telemetry_data vtd
--JOIN evdata.vehicle_imu_data vid
--ON vtd.co_relation_id = vid.co_relation_id
--WHERE vtd.di_ignition = false AND vtd.di_motion = false AND vtd."timestamp" > '2024-12-02 00:00:00+00'::timestamp with time zone
--GROUP BY vtd.imei, (time_bucket('1 day'::interval, vtd."timestamp"));
--
--CREATE OR REPLACE VIEW _timescaledb_internal._direct_view_670 AS
--SELECT vtd.imei,
--time_bucket('1 day'::interval, vtd."timestamp") AS bucket,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_x_axis::double precision)) AS grv_x_running,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_y_axis::double precision)) AS grv_y_running,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_z_axis::double precision)) AS grv_z_running
--FROM evdata.vehicle_telemetry_data vtd
--JOIN evdata.vehicle_imu_data vid
--ON vtd.co_relation_id = vid.co_relation_id
--WHERE vtd.di_ignition = true AND vtd.di_motion = true AND vtd."timestamp" > '2024-12-02 00:00:00+00'::timestamp with time zone
--GROUP BY vtd.imei, (time_bucket('1 day'::interval, vtd."timestamp"));
--
--
--CREATE OR REPLACE VIEW _timescaledb_internal._partial_view_669 AS
--SELECT vtd.imei,
--time_bucket('1 day'::interval, vtd."timestamp") AS bucket,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_x_axis::double precision)) AS grv_x_still,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_y_axis::double precision)) AS grv_y_still,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_z_axis::double precision)) AS grv_z_still
--FROM evdata.vehicle_telemetry_data vtd
--JOIN evdata.vehicle_imu_data vid
--ON vtd.co_relation_id = vid.co_relation_id
--WHERE vtd.di_ignition = false AND vtd.di_motion = false AND vtd."timestamp" > '2024-12-02 00:00:00+00'::timestamp with time zone
--GROUP BY vtd.imei, (time_bucket('1 day'::interval, vtd."timestamp"));
--
--CREATE OR REPLACE VIEW _timescaledb_internal._partial_view_670 AS
--SELECT vtd.imei,
--time_bucket('1 day'::interval, vtd."timestamp") AS bucket,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_x_axis::double precision)) AS grv_x_running,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_y_axis::double precision)) AS grv_y_running,
--percentile_cont(0.5::double precision) WITHIN GROUP (ORDER BY (vid.grv_z_axis::double precision)) AS grv_z_running
--FROM evdata.vehicle_telemetry_data vtd
--JOIN evdata.vehicle_imu_data vid
--ON vtd.co_relation_id = vid.co_relation_id
--WHERE vtd.di_ignition = true AND vtd.di_motion = true AND vtd."timestamp" > '2024-12-02 00:00:00+00'::timestamp with time zone
--GROUP BY vtd.imei, (time_bucket('1 day'::interval, vtd."timestamp"));


DROP MATERIALIZED VIEW IF EXISTS evdata.median_grv_still_1day_aggregate;
DROP MATERIALIZED VIEW IF EXISTS evdata.median_grv_running_1day_aggregate;
