--data_frequency_plan table creation
CREATE SEQUENCE IF NOT EXISTS data_frequency_plan_seq start with 1 increment by 50;
CREATE TABLE IF NOT EXISTS data_frequency_plan
(
	id bigint PRIMARY KEY DEFAULT nextval('data_frequency_plan_seq'),
	name VARCHAR(255) NOT NULL UNIQUE,
	value int,
	unit VARCHAR(255)
);

--combo_plan table creation
CREATE SEQUENCE IF NOT EXISTS combo_plan_seq start with 1 increment by 50;
CREATE TABLE IF NOT EXISTS combo_plan
(
	id bigint PRIMARY KEY DEFAULT nextval('combo_plan_seq'),
	name VARCHAR(255) NOT NULL UNIQUE,
	status VARCHAR(255),
	data_frequency_plan_id bigint,
    FOREIGN KEY (data_frequency_plan_id) REFERENCES data_frequency_plan(id),
	CONSTRAINT vehicle_operation_status_check CHECK (status::text = ANY (ARRAY['ACTIVE'::character varying::text, 'EXPIRED'::character varying::text]))
);

--organisation_subscription table
CREATE SEQUENCE IF NOT EXISTS organisation_subscription_seq start with 1 increment by 50;
CREATE TABLE IF NOT EXISTS organisation_subscription (
    id bigint PRIMARY KEY DEFAULT nextval('organisation_subscription_seq'),
    combo_plan_id bigint NOT NULL,
	org_id bigint NOT NULL,
    start_date timestamp(6) with time zone,
    end_date timestamp(6) with time zone,
    FOREIGN KEY (combo_plan_id) REFERENCES combo_plan(id),
	FOREIGN KEY (org_id) REFERENCES organisations(id)
);

--vehicle_model_subscription table
CREATE SEQUENCE IF NOT EXISTS vehicle_model_subscription_seq start with 1 increment by 50;
CREATE TABLE IF NOT EXISTS vehicle_model_subscription (
    id bigint PRIMARY KEY DEFAULT nextval('vehicle_model_subscription_seq'),
    combo_plan_id bigint NOT NULL,
	vehicle_model_id bigint NOT NULL,
    start_date timestamp(6) with time zone,
    end_date timestamp(6) with time zone,
    FOREIGN KEY (combo_plan_id) REFERENCES combo_plan(id),
	FOREIGN KEY (vehicle_model_id) REFERENCES vehicle_model(id)
);

--vehicle_data_frequency_plan
CREATE SEQUENCE IF NOT EXISTS active_vehicle_subscription_plan_seq start with 1 increment by 50;
CREATE TABLE IF NOT EXISTS active_vehicle_subscription_plan (
    id bigint PRIMARY KEY DEFAULT nextval('active_vehicle_subscription_plan_seq'),
    vehicle_id bigint NOT NULL,
	combo_plan_id bigint,
	data_frequency_plan_id bigint,
    FOREIGN KEY (vehicle_id) REFERENCES vehicle(id),
	FOREIGN KEY (data_frequency_plan_id) REFERENCES data_frequency_plan(id)
);

--add column to vehicle_status
ALTER TABLE vehicle_status
ADD COLUMN IF NOT EXISTS updated_on timestamp(6) with time zone;

