ALTER TABLE part
DROP CONSTRAINT IF EXISTS part_part_type_check;

ALTER TABLE part
ADD CONSTRAINT part_part_type_check CHECK((part_type in ('BATTERY','CHARGER','MOTOR','COLOR','HORN','MIRRO<PERSON>','SHOCK_FRONT','SHOCK_REAR','STAND','SAREE_GUARD','GPS','ACCELEROMETER','MCS','IO','DRIVE_MODE','TCU','CHASSIS','BMS','HS_DATA_LOGGER','VEHICLE','FRONT_TYRE','REAR_TYRE','MCU','TCU_FIRMWARE','MCU_FIRMWARE','BMS_SOFTWARE','DC_DC_CONVERTER','GSM')));

ALTER TABLE part_model
DROP CONSTRAINT IF EXISTS part_model_part_type_check;

ALTER TABLE part_model
ADD CONSTRAINT part_model_part_type_check CHECK((part_type in ('BATTERY','CHARGE<PERSON>','MOT<PERSON>','COLOR','HORN','MIRROR','SHOCK_FRONT','SHOCK_REAR','STAND','SAREE_GUARD','GPS','ACCELEROMETER','MCS','IO','DRIVE_MODE','TCU','CHASSIS','BMS','HS_DATA_LOGGER','VEHICLE','FRONT_TYRE','REAR_TYRE','MCU','TCU_FIRMWARE','MCU_FIRMWARE','BMS_SOFTWARE','DC_DC_CONVERTER','GSM')));

CREATE TABLE if NOT EXISTS gsm_model(
    id BIGINT,
    PRIMARY KEY(id)
);

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'fk_gsm_part_model_id'
    ) THEN
        ALTER TABLE gsm_model
        ADD CONSTRAINT fk_gsm_part_model_id
        FOREIGN KEY (id)
        REFERENCES part_model(id);
    END IF;
END $$;