--deleting the older data from subscription related tables
delete from combo_plan;
delete from active_vehicle_subscription_plan;
delete from data_frequency_plan;

--creating table aggregate_name and inserting the data
create sequence if not exists aggregate_name_seq start with 1 increment by 1;
create table if not exists aggregate_name
(
	id bigint PRIMARY KEY DEFAULT nextval('aggregate_name_seq'),
	name character varying(255),
	type character varying(255),
	CONSTRAINT type_check CHECK (type::text = ANY (ARRAY['TELEMETRY'::character varying::text, 'BATTERY'::character varying::text,
																   'LOCATION'::character varying::text, 'BATTERY_STACK'::character varying::text,
																   'BATTERY_CELL'::character varying::text,
																   'BATTERY_ALARM'::character varying::text]))
);
INSERT INTO aggregate_name (name,type)
VALUES
('vehicle_status_telemetry_10sec_aggregate','TELEMETRY'),
('vehicle_status_telemetry_30sec_aggregate','TELEMETRY'),
('vehicle_status_telemetry_1min_aggregate','TELEMETRY'),
('vehicle_status_battery_10sec_aggregate','BATTERY'),
('vehicle_status_battery_30sec_aggregate','BATTERY'),
('vehicle_status_battery_1min_aggregate','BATTERY');

--inserting new data into data_frequency_plan
insert into data_frequency_plan (name,value,unit)
values
('High Frequency','1','seconds'),
('Medium Frequency','5','seconds'),
('Low Frequency','10','seconds');

--inserting data into active_vehicle_subscription_plan
insert into active_vehicle_subscription_plan (vehicle_id,data_frequency_plan_id)
select v.id,dfp.id from vehicle v,data_frequency_plan dfp
where dfp.name='High Frequency';

--frequency_plan_details table creation
create sequence if not exists data_frequency_plan_details_seq start with 1 increment by 1;
CREATE TABLE IF NOT EXISTS data_frequency_plan_details
(
	id bigint PRIMARY KEY DEFAULT nextval('data_frequency_plan_details_seq'),
	feature_name character varying (255),
    data_frequency_plan_id bigint,
	feature_status character varying(255) NOT NULL,
	computation_frequency smallint,
	unit character varying (255),
	CONSTRAINT fk_data_frequency_plan_id FOREIGN KEY (data_frequency_plan_id)
	REFERENCES data_frequency_plan (id),
	CONSTRAINT uk_data_frequency_plan_feature_name UNIQUE (data_frequency_plan_id, feature_name),
    CONSTRAINT feature_status_check CHECK (feature_status::text = ANY (ARRAY['ACTIVE'::character varying::text, 'EXPIRED'::character varying::text]))
);

--create association table data_frequency_plan_details_aggregate_names
CREATE TABLE if not exists data_frequency_plan_details_aggregate_names (
    data_frequency_plan_details_id BIGINT NOT NULL,
    aggregate_name_id BIGINT NOT NULL,
    FOREIGN KEY (data_frequency_plan_details_id) REFERENCES data_frequency_plan_details(id) ,
	FOREIGN KEY (aggregate_name_id) REFERENCES aggregate_name(id) ,
	PRIMARY KEY (data_frequency_plan_details_id, aggregate_name_id)
);

--data insertion into data_frequency_plan_details table
INSERT INTO data_frequency_plan_details (feature_name, data_frequency_plan_id, feature_status, computation_frequency, unit)
SELECT 'vehicleStatus', id, 'ACTIVE', 10, 'seconds'
FROM data_frequency_plan
WHERE name = 'High Frequency'
UNION ALL
SELECT 'vehicleStatus', id, 'ACTIVE', 30, 'seconds'
FROM data_frequency_plan
WHERE name = 'Medium Frequency'
UNION ALL
SELECT 'vehicleStatus', id, 'ACTIVE', 60, 'seconds'
FROM data_frequency_plan
WHERE name = 'Low Frequency';

--data insertion association table into data_frequency_plan_details_aggregate_names
insert into data_frequency_plan_details_aggregate_names (data_frequency_plan_details_id,aggregate_name_id)
select dfpd.id,an.id
from data_frequency_plan_details dfpd, aggregate_name an
where dfpd.feature_name='vehicleStatus' and computation_frequency=10 and an.name in ('vehicle_status_telemetry_10sec_aggregate',
																					'vehicle_status_battery_10sec_aggregate')
UNION ALL
select dfpd.id,an.id
from data_frequency_plan_details dfpd, aggregate_name an
where dfpd.feature_name='vehicleStatus' and computation_frequency=30 and an.name in ('vehicle_status_telemetry_30sec_aggregate',
																					'vehicle_status_battery_30sec_aggregate')
UNION ALL
select dfpd.id,an.id
from data_frequency_plan_details dfpd, aggregate_name an
where dfpd.feature_name='vehicleStatus' and computation_frequency=60 and an.name in ('vehicle_status_telemetry_1min_aggregate',
																					'vehicle_status_battery_1min_aggregate');

--insertion of data into active_vehicle_subscription_plan
insert into active_vehicle_subscription_plan (vehicle_id,data_frequency_plan_id)
select v.id,dfp.id
from vehicle v, data_frequency_plan dfp
where dfp.name='High Frequency';

--creating the table cron frequency
create sequence if not exists cron_frequency_seq start with 1 increment by 1;
create table if not exists cron_frequency
(
	id bigint PRIMARY KEY DEFAULT nextval('cron_frequency_seq'),
	cron_time INTEGER not null,
	unit varchar not null,
	CONSTRAINT uk_cron_time_unit UNIQUE (cron_time, unit)
);
insert into cron_frequency (cron_time,unit)
values
(0,'seconds'),
(10,'seconds'),
(20,'seconds'),
(30,'seconds'),
(40,'seconds'),
(50,'seconds');

-- creation of mapping table data_frequency_plan_details_cron_frequencies
create table if not exists cron_frequency_data_frequency_plan_details
(
	cron_frequency_id bigint not null,
	data_frequency_plan_details_id bigint not null,
	FOREIGN KEY (data_frequency_plan_details_id) REFERENCES data_frequency_plan_details(id) ,
	FOREIGN KEY (cron_frequency_id) REFERENCES cron_frequency(id) ,
	PRIMARY KEY (cron_frequency_id, data_frequency_plan_details_id)
);

-- data insertion into data_frequency_plan_details_cron_frequencies
INSERT INTO cron_frequency_data_frequency_plan_details (cron_frequency_id, data_frequency_plan_details_id)
SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'vehicleStatus'
  AND dfpd.computation_frequency = 10
  AND cf.cron_time IN (0, 10, 20, 30, 40, 50)
  AND cf.unit = 'seconds'

UNION ALL

SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'vehicleStatus'
  AND dfpd.computation_frequency = 30
  AND cf.cron_time IN (0, 30)
  AND cf.unit = 'seconds'

UNION ALL

SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'vehicleStatus'
  AND dfpd.computation_frequency = 60
  AND cf.cron_time IN (0)
  AND cf.unit = 'seconds';