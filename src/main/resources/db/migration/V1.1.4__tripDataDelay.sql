alter table trip
add column IF NOT EXISTS update_source varchar,
add CONSTRAINT update_source_check CHECK (update_source::text = ANY (ARRAY['TRIP_CRON'::character varying::text, 'TRIP_DATA_DELAY_CRON'::character varying::text]));

alter table trip
drop constraint IF EXISTS trip_summary_population_status_check;
alter table trip
add CONSTRAINT trip_summary_population_status_check CHECK (summary_population_status::text = ANY (ARRAY['IN_PROGRESS'::character varying::text, 'COMPLETED'::character varying::text,
																									   'FAILED'::character varying::text, 'ARCHIVED'::character varying::text]));

alter table data_frequency_plan_details
drop constraint IF EXISTS feature_name_check;
alter table data_frequency_plan_details
add CONSTRAINT feature_name_check CHECK (feature_name::text = ANY (ARRAY['VEHICLE_STATUS'::character varying::text, 'VEHICLE_STATUS_UPDATION'::character varying::text,'TRIP_UPDATION'::character varying::text]));

alter table user_vehicle_trip
drop constraint IF EXISTS status_check;
alter table user_vehicle_trip
add CONSTRAINT statuc_check CHECK (status::text = ANY (ARRAY['IN_PROGRESS'::character varying::text, 'COMPLETED'::character varying::text,
																									   'FAILED'::character varying::text, 'ARCHIVED'::character varying::text]));


--insertion of data into cron_frequency
insert into cron_frequency (cron_time,unit)
values (19,'HOURS');

--insertion of data into data_frequency_plan_details
INSERT INTO data_frequency_plan_details (feature_name, data_frequency_plan_id, status, computation_frequency, unit)
SELECT 'TRIP_UPDATION', id, 'ACTIVE', 10, 'SECONDS'
FROM data_frequency_plan
WHERE name = 'High Frequency'
UNION ALL
SELECT 'TRIP_UPDATION', id, 'ACTIVE', 30, 'SECONDS'
FROM data_frequency_plan
WHERE name = 'Medium Frequency'
UNION ALL
SELECT 'TRIP_UPDATION', id, 'ACTIVE', 60, 'SECONDS'
FROM data_frequency_plan
WHERE name = 'Low Frequency';


-- data insertion into data_frequency_plan_details_cron_frequencies
INSERT INTO cron_frequency_data_frequency_plan_details (cron_frequency_id, data_frequency_plan_details_id)
SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'TRIP_UPDATION'
  AND dfpd.computation_frequency = 10
  AND cf.cron_time IN (19)
  AND cf.unit = 'HOURS'

UNION ALL

SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'TRIP_UPDATION'
  AND dfpd.computation_frequency = 30
  AND cf.cron_time IN (19)
  AND cf.unit = 'HOURS'

UNION ALL

SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'TRIP_UPDATION'
  AND dfpd.computation_frequency = 60
  AND cf.cron_time IN (19)
  AND cf.unit = 'HOURS';

