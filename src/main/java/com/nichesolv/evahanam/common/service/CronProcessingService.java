package com.nichesolv.evahanam.common.service;

import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import com.nichesolv.evahanam.trip.service.TripSummaryService;
import com.nichesolv.evahanam.vehicle.service.VehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;

@Slf4j
@Service
public class CronProcessingService {

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private TripSummaryService tripSummaryService;

    /**
     * Asynchronously processes vehicle status updates
     * @param imei Vehicle IMEI
     * @param currentTime Current timestamp
     * @param dataFrequencyPlanDetails Data frequency plan details
     */
    @Async("cronEventListenerExecutor")
    public void processVehicleStatusAsync(String imei, Instant currentTime, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        log.debug("THREAD_DEBUG: Processing vehicle status for IMEI {} on thread: {}", imei, Thread.currentThread().getName());
        try {
            vehicleService.saveVehicleState(imei, currentTime, dataFrequencyPlanDetails);
            log.debug("CRON_SUCCESS: Saved vehicle state for IMEI {}", imei);
        } catch (Exception ex) {
            log.error("CRON_ERROR: Failed to save vehicle state for IMEI {}: {}", imei, ex.getMessage(), ex);
        }
    }

    /**
     * Asynchronously processes vehicle status updates for data delay scenarios
     * @param imei Vehicle IMEI
     * @param currentTime Current timestamp
     * @param dataFrequencyPlanDetails Data frequency plan details
     */
    @Async("cronEventListenerExecutor")
    public void processVehicleStatusUpdationAsync(String imei, Instant currentTime, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        log.debug("THREAD_DEBUG: Processing vehicle status update for IMEI {} on thread: {}", imei, Thread.currentThread().getName());
        try {
            vehicleService.updateVehicleStateForDataDelay(imei, currentTime, dataFrequencyPlanDetails);
            log.debug("CRON_SUCCESS: Updated vehicle state for data delay for IMEI {}", imei);
        } catch (Exception ex) {
            log.error("CRON_ERROR: Failed to update vehicle state for data delay for IMEI {}: {}", imei, ex.getMessage(), ex);
        }
    }

    /**
     * Asynchronously processes trip updates
     * @param imei Vehicle IMEI
     * @param currentTime Current timestamp
     * @param dataFrequencyPlanDetails Data frequency plan details
     */
    @Async("cronEventListenerExecutor")
    public void processTripUpdationAsync(String imei, Instant currentTime, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        log.debug("THREAD_DEBUG: Processing trip update for IMEI {} on thread: {}", imei, Thread.currentThread().getName());
        try {
            tripSummaryService.saveDataDelayedAutomaticTrips(imei, currentTime, dataFrequencyPlanDetails);
            log.debug("CRON_SUCCESS: Saved data delayed automatic trips for IMEI {}", imei);
        } catch (Exception ex) {
            log.error("CRON_ERROR: Failed to save data delayed automatic trips for IMEI {}: {}", imei, ex.getMessage(), ex);
        }
    }
}
