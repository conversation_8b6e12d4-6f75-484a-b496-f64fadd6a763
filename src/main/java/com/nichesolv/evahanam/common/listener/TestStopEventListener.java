package com.nichesolv.evahanam.common.listener;

import com.nichesolv.evahanam.common.events.CronEvent;
import com.nichesolv.evahanam.vehicle.dto.CronDataSource;
import com.nichesolv.evahanam.vehicleTests.dto.TestStartSource;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.events.TestStartEvent;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import com.nichesolv.evahanam.vehicleTests.v2.service.VehicleTestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.List;

@Component
@Slf4j
public class TestStopEventListener implements ApplicationListener<CronEvent> {

    @Autowired
    ApplicationEventPublisher publisher;

    @Autowired
    VehicleTestRepository vehicleTestRepository;

    @Override
    @Async("cronEventListenerExecutor")
    public void onApplicationEvent(CronEvent event) {
        try {
            CronDataSource data = event.getSource();

             /* In case of the connectivity test if there is any test which is still in the running state ,
         past its completion time then those tests are made to complete. As this cron runs every second
         checking if any tests are present which is in RUNNING status from (now - 2 minutes) so that the
         tests which are started now should not be included .
        */
            Instant bufferDuration = data.getCurrentTime().minus(Duration.ofMinutes(2));
            List<VehicleTest> runningTest = vehicleTestRepository.findByStatusAndTestTypPastEndTime(TestStatus.RUNNING.name(), TestTypeName.CONNECTIVITY.name(), bufferDuration);
            for (VehicleTest test : runningTest) {
                try {
                    log.info("Publishing TestStartEvent for test: {}", test.getId());
                    publisher.publishEvent(new TestStartEvent(new TestStartSource(test.getId(), test.getEndTime())));
                } catch (Exception exception) {
                    log.error("Failed to publish TestStartEvent for test id {} ", test.getId(), exception);
                }
            }
        } catch (Exception exception) {
            log.error("Error processing CronEvent in TestStopEventListener", exception);
        }
    }
}
