package com.nichesolv.evahanam.common.listener;

import com.nichesolv.evahanam.common.events.CronEvent;
import com.nichesolv.evahanam.common.events.VehicleCronEvent;
import com.nichesolv.evahanam.common.jpa.ActiveVehicleSubscriptionPlan;
import com.nichesolv.evahanam.common.jpa.CronFrequency;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import com.nichesolv.evahanam.common.repository.ActiveVehicleSubscriptionPlanRepository;
import com.nichesolv.evahanam.common.repository.CronFrequencyRepository;
import com.nichesolv.evahanam.vehicle.dto.CronDataSource;
import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Stream;

@Component
@Slf4j
public class CronEventListener implements ApplicationListener<CronEvent> {

    @Autowired
    CronFrequencyRepository cronFrequencyRepository;

    @Autowired
    ActiveVehicleSubscriptionPlanRepository activeVehicleSubscriptionPlanRepository;

    @Autowired
    ApplicationEventPublisher eventPublisher;

    @Override
    @Async("cronEventListenerExecutor")
    @Transactional
    public void onApplicationEvent(CronEvent event) {
        CronDataSource data = event.getSource();
        LocalDateTime currentLocalDataTime = data.getCurrentLocalTime();
        log.debug("currentLocalDateTime {} {}", currentLocalDataTime, data.getCurrentTime());
        List<Integer> cronTimes = new ArrayList<>();
        cronTimes.add(currentLocalDataTime.getSecond());
        List<ChronoUnit> chronoUnits = new ArrayList<>();
        chronoUnits.add(ChronoUnit.SECONDS);
        if (currentLocalDataTime.getMinute() == 0 && currentLocalDataTime.getSecond() == 0) {
            cronTimes.add(currentLocalDataTime.getMinute());
            chronoUnits.add(ChronoUnit.MINUTES);
        }
        //in prod and preprod -> checking if the time is 12:35 AM
        // in dev -> checking if the time is 8:35 PM
        if (currentLocalDataTime.getHour() == 19 && currentLocalDataTime.getMinute() == 5 && currentLocalDataTime.getSecond() == 0) {
            cronTimes.add(currentLocalDataTime.getHour());
            chronoUnits.add(ChronoUnit.HOURS);
        }
        List<CronFrequency> cronFrequencies = cronFrequencyRepository.findByCronTimeInAndUnitIn(cronTimes, chronoUnits);

        List<DataFrequencyPlanDetails> dataFrequencyPlanDetails = cronFrequencies.stream().flatMap(cronFrequency -> cronFrequency.getDataFrequencyPlanDetails().stream()).toList();
        dataFrequencyPlanDetails.forEach(e -> {

            try (Stream<String> vehicleImeiStream = getVehiclesByDataFrequencyPlan(e.getDataFrequencyPlan()).stream().map(Vehicle::getImei)) {
                vehicleImeiStream.forEach(imei -> {

                    VehicleCronEvent vehicleCronEvent = new VehicleCronEvent(imei, data.getCurrentTime(), e);
                    eventPublisher.publishEvent(vehicleCronEvent);
                });
            } catch (Exception ex) {
                log.error("CRON_ERROR: Failed to process data frequency plan {}: {}", e.getDataFrequencyPlan().getId(), ex.getMessage(), ex);
            }
        });
    }
    /**
     * This method finds the ACTIVE vehicles based on the parameter
     *
     * @param dataFrequencyPlan plan
     * @return list of ACTIVE vehicles which comes under that dataFrequencyPlan
     */
    public List<Vehicle> getVehiclesByDataFrequencyPlan(DataFrequencyPlan dataFrequencyPlan) {
        List<Vehicle> vehicles = new ArrayList<>();
        Predicate<ActiveVehicleSubscriptionPlan> isVehicleActiveCheck = activeVehicleSubscriptionPlan -> activeVehicleSubscriptionPlan.getVehicle().getOperationStatus().equals(OperationStatus.ACTIVE);
        try (Stream<ActiveVehicleSubscriptionPlan> stream = activeVehicleSubscriptionPlanRepository.findAllByDataFrequencyPlan(dataFrequencyPlan)) {
            vehicles = stream .filter(isVehicleActiveCheck)
                    .map(ActiveVehicleSubscriptionPlan::getVehicle)
                    .toList();
            return vehicles;

        }
    }
}

