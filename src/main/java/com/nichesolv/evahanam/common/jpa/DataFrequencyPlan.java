package com.nichesolv.evahanam.common.jpa;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.temporal.ChronoUnit;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "data_frequency_plan", uniqueConstraints = {
        @UniqueConstraint(columnNames = "name")
})
@Getter
/**
 * This entity class is to define data_frequency_plans (10sec/30sec/1min) for the vehicles
 */
@Setter
public class DataFrequencyPlan {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;

    @NotNull
    @Column(unique = true)
    String name;

    Integer value;

    @Enumerated(EnumType.STRING)
    ChronoUnit unit;

    public DataFrequencyPlan(String name, Integer value, ChronoUnit unit) {
        this.name = name;
        this.value = value;
        this.unit = unit;
    }
}
