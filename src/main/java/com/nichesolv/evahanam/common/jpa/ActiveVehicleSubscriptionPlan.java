package com.nichesolv.evahanam.common.jpa;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
/**
 * This entity class contains the mapping between vehicle and data_frequency_plan, which tells the data_frequency_plan for the vehicle
 */
public class ActiveVehicleSubscriptionPlan {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;

    @OneToOne
    @NotNull
    Vehicle vehicle;

    @ManyToOne
    ComboPlan comboPlan;

    @ManyToOne
    @JoinColumn(name = "data_frequency_plan_id")
    DataFrequencyPlan dataFrequencyPlan;

    @ManyToOne
    @JoinColumn(name = "data_parsing_plan_id")
    DataParsingPlan dataParsingPlan;

    public ActiveVehicleSubscriptionPlan(Vehicle vehicle, DataFrequencyPlan dataFrequencyPlan) {
        this.vehicle = vehicle;
        this.dataFrequencyPlan = dataFrequencyPlan;
    }
}
