package com.nichesolv.evahanam;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.reactive.function.client.WebClient;


@SpringBootApplication
@EntityScan(basePackages = {"com.nichesolv.*"})
@EnableJpaRepositories(basePackages = {"com.nichesolv.*"})
@ComponentScan(basePackages = {"com.nichesolv.*"})
@EnableScheduling
@EnableCaching
public class DataLayerApplication extends SpringBootServletInitializer {
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        System.setProperty("jdk.httpclient.allowRestrictedHeaders", "Host");
        return application.sources(DataLayerApplication.class);
    }

    public static void main(String[] args) {

        System.setProperty("jdk.httpclient.allowRestrictedHeaders", "Host");
        SpringApplication.run(DataLayerApplication.class, args);
    }

    @Bean
    public WebClient.Builder getWebClient() {
        return WebClient.builder();
    }
}
