package com.nichesolv.evahanam.vehicle.service;

import com.nichesolv.evahanam.telemetryData.dto.PartHealthMonitorDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class PartHealthMonitorListener {

    @Autowired
    private PartHealthMonitorServiceFactory partHealthMonitorServiceFactory;

    @Value("${rabbitmq.queue.part-health-monitor-queue}")
    String partHealthMonitorQueue;

    @RabbitListener(queues = "${rabbitmq.queue.part-health-monitor-queue}")
    public void handleMessage(PartHealthMonitorDto partHealthMonitorDto) {
        try {
            log.debug("Received message from queue :{}", partHealthMonitorDto);
            PartHealthMonitorService partHealthMonitorService = partHealthMonitorServiceFactory.getService(partHealthMonitorDto.getPartType());
            partHealthMonitorService.updatePartHealthCounter(partHealthMonitorDto);
        } catch (Exception e) {
            log.error("error processing from part health monitor queue : {} ", partHealthMonitorDto, e);
        }
    }
}
