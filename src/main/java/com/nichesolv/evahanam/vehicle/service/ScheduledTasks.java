package com.nichesolv.evahanam.vehicle.service;

import com.nichesolv.evahanam.cache.service.VehicleCacheRetrievalService;
import com.nichesolv.evahanam.common.dto.PushNotificationRequestDto;
import com.nichesolv.evahanam.common.dto.TimestampProjection;
import com.nichesolv.evahanam.common.events.CronEvent;
import com.nichesolv.evahanam.common.service.FCMService;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.evApp.jpa.UserVehicleConnection;
import com.nichesolv.evahanam.evApp.repository.UserVehicleConnectionRepository;
import com.nichesolv.evahanam.telemetryData.dto.*;
import com.nichesolv.evahanam.telemetryData.enums.ExportReportRequestStatus;
import com.nichesolv.evahanam.telemetryData.jpa.ExportRequest;
import com.nichesolv.evahanam.telemetryData.jpa.bmsdata.enums.AlertType;
import com.nichesolv.evahanam.telemetryData.repository.ExportRequestStatusRepository;
import com.nichesolv.evahanam.telemetryData.repository.MotorDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.TelemetryBatteryRepository;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.bmsrepo.BatteryAlarmRepository;
import com.nichesolv.evahanam.telemetryData.service.telemetryData.ExportService;
import com.nichesolv.evahanam.vehicle.dto.CronDataSource;
import com.nichesolv.evahanam.vehicle.dto.ImeiPartTypeCountProjection;
import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.exception.VehicleNotFoundException;
import com.nichesolv.evahanam.vehicle.jpa.Metric;
import com.nichesolv.evahanam.vehicle.jpa.RangeAlert;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleLatestData;
import com.nichesolv.evahanam.vehicle.repository.PartRepository;
import com.nichesolv.evahanam.vehicle.repository.RangeAlertRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleLatestDataRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.usermgmt.user.model.user.device.DeviceToken;
import com.nichesolv.usermgmt.user.repository.user.device.DeviceTokenRepository;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.FileNotFoundException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.RejectedExecutionException;


@Slf4j
@Component
public class ScheduledTasks {

    @Autowired
    IVehicleService vehicleService;
    @Autowired
    VehicleDataRepository vehicleDataRepository;

    @Autowired
    MotorDataRepository motorDataRepository;

    @Autowired
    TelemetryBatteryRepository telemetryBatteryRepository;

    @Autowired
    FCMService fcmService;

    @Autowired
    DeviceTokenRepository deviceTokenRepository;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    UserVehicleConnectionRepository userVehicleConnectionRepository;
    @Autowired
    ExportRequestStatusRepository exportRequestStatusRepository;
    @Autowired
    RangeAlertRepository rangeAlertRepository;

    @Autowired
    ExportService exportService;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    VehicleLatestDataRepository vehicleLatestDataRepository;

    @Autowired
    ApplicationEventPublisher publisher;


    @Scheduled(cron = "0/1 * * * * ?", zone = "UTC")
    public void vehicleStatus1SecCron() {
        Instant instantNow = Instant.now().truncatedTo(ChronoUnit.SECONDS);
        try {
            log.info("[CRON TICK] Scheduled tick at {}", instantNow);
            LocalDateTime currentLocalTime = LocalDateTime.now();
            //publishing the message to ApplicationEventListener
            CronEvent cronEvent = new CronEvent(new CronDataSource(currentLocalTime, instantNow));
            publisher.publishEvent(cronEvent);
        } catch (Exception ex) {
            log.error("Unexpected error during vehicleStatus1SecCron execution", ex);
        }
    }

    @Autowired
    VehicleCacheRetrievalService vehicleCacheRetrievalService;


    @Autowired
    BatteryAlarmRepository batteryAlarmRepository;

    @Autowired
    PartRepository partRepository;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Value("${rabbitmq.queue.part-health-monitor-queue}")
    String partHealthMonitorQueue;


    @Scheduled(cron = "0/10 * * * * ?", zone = "UTC")
    public void interval10secEvents() {
        try {
            updateVehicleLatestData();
        } catch (Exception e) {
            log.error("Error updating the updateOdometerAndLatitudeAndLongitude");
        }
    }

    @Transactional
    public void updateVehicleLatestData() {
        TimestampProjection maxTimestampProjection = vehicleDataRepository.findFirstByTelemetryIdxTimestampLessThanOrderByTelemetryIdxTimestampDesc(Instant.now());

        //updating the current vehicle_state, lat-long, soc for all the vehicles
        List<String> vehicleImeis = vehicleRepository.getVehicleImeiWithOperationStatus(OperationStatus.ACTIVE.name());
        log.debug("number of active vehicles {}", vehicleImeis.size());
        vehicleImeis.forEach(imei -> {
            Optional<VehicleLatestData> vehicleLatestDataOptional = vehicleLatestDataRepository.findByImei(imei);
            if (vehicleLatestDataOptional.isEmpty()) {
                log.info("vehicle latest data is empty for IMEI : {}", imei);
            } else {
                VehicleLatestData vehicleLatestData = vehicleLatestDataOptional.get();
                vehicleLatestData = vehicleService.saveVehicleLatitudeAndLongitude(imei, vehicleLatestData);
                vehicleLatestData = vehicleService.getRecentVehicleBatteryData(imei, vehicleLatestData);
                vehicleLatestDataRepository.save(vehicleLatestData);
            }
        });
    }

    @Scheduled(fixedRate = 120000)
    @Transactional
    public void sendTemperatureAlertPushNotificationMessagesThroughFCM() throws FileNotFoundException {
        Instant currentTime = Instant.now();
        List<VehicleAlertsResponseDto> vehicleAlertsResponses = telemetryBatteryRepository.getVehiclesWithAlerts(currentTime.minusSeconds(120), currentTime, Pageable.unpaged());

        for (VehicleAlertsResponseDto alert : vehicleAlertsResponses) {
            Vehicle vehicle = vehicleRepository.findByImei(alert.getImei()).orElseThrow(() -> new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));
            List<UserVehicleConnection> userVehicleConnections = userVehicleConnectionRepository.findUserByVehicleAndEndOn(vehicle, null);
            for (UserVehicleConnection e : userVehicleConnections) {
                fcmService.sendPushNotificationRequest(getPushNotificationRequest(alert, e.getUser(), e.getVehicle()));
            }
        }
    }

    private PushNotificationRequestDto getPushNotificationRequest(VehicleAlertsResponseDto vehicleAlertsResponseDto, CustomUser user, Vehicle vehicle) {
        Optional<DeviceToken> deviceToken = deviceTokenRepository.findTokenByUser(user);
        PushNotificationRequestDto pushNotificationRequestDto = new PushNotificationRequestDto();
        if (deviceToken.isPresent() && !vehicleAlertsResponseDto.getStatus().equals(AlertType.NONE)) {
            if (vehicleAlertsResponseDto.getStatus().equals(AlertType.HIGH)) {
                Float baseTemp = Float.parseFloat(vehicleCacheRetrievalService.getAttribute("tempMaxAlertLow", PartType.BATTERY.name(), vehicle).getValue());
                pushNotificationRequestDto.setTitle(evMessageBundle.getMessage("CRITICAL_BATTERY_ALERT_MESSAGE"));
                pushNotificationRequestDto.setBody(evMessageBundle.getMessage("CRITICAL_BATTERY_ALERT_MESSAGE_BODY", baseTemp));
            } else if (vehicleAlertsResponseDto.getStatus().equals(AlertType.MEDIUM)) {
                pushNotificationRequestDto.setTitle(evMessageBundle.getMessage("BATTERY_OVERHEATING_WARNING_MESSAGE"));
                pushNotificationRequestDto.setBody(evMessageBundle.getMessage("BATTERY_OVERHEATING_WARNING_MESSAGE_BODY"));
            } else if (vehicleAlertsResponseDto.getStatus().equals(AlertType.LOW)) {
                pushNotificationRequestDto.setTitle(evMessageBundle.getMessage("BATTERY_WARNING_MESSAGE"));
                pushNotificationRequestDto.setBody(evMessageBundle.getMessage("BATTERY_WARNING_MESSAGE_BODY"));
            }
            pushNotificationRequestDto.setToken(deviceToken.get().getToken());
            pushNotificationRequestDto.setChannelId("max_importance_channel_id");
            pushNotificationRequestDto.setIcon("ic_launcher");
        }
        return pushNotificationRequestDto;
    }

    @Scheduled(cron = "0 0 10 * * ?", zone = "UTC")
    public void updateCsvRequestStatus() {
        List<ExportRequest> exportRequests = exportRequestStatusRepository.findByStatusAndModifiedOnBefore(ExportReportRequestStatus.READY, Instant.now().minus(34, ChronoUnit.HOURS));
        exportRequests.addAll(exportRequestStatusRepository.findByStatusAndModifiedOnBefore(ExportReportRequestStatus.PROCESSING, Instant.now().minus(1, ChronoUnit.HOURS)));
        for (ExportRequest exportRequest1 : exportRequests) {
            if (exportRequest1.getStatus().equals(ExportReportRequestStatus.READY)) {
                log.debug("Updating EXPIRED export requests");
                exportService.updateFailedOrExpiredStatus(new ExportResponseDto(exportRequest1.getId(), null), ExportReportRequestStatus.EXPIRED);
            } else {
                log.debug("Updating FAILED export requests");
                exportService.updateFailedOrExpiredStatus(new ExportResponseDto(exportRequest1.getId(), null), ExportReportRequestStatus.FAILED);
            }
        }
    }

    @Transactional
    @Scheduled(cron = "0 */1 * * * *", zone = "UTC")
    public void checkThresholds() {
        Instant now = Instant.now();
        Instant fiveMinutesPrior = now.minus(Duration.ofMinutes(5));
        log.debug("current time {}", now);
        log.debug("current time minus five minute {}", fiveMinutesPrior);
        List<String> vehicleImeiList = vehicleRepository.getVehicleImei();
        log.debug("vehicle size {}", vehicleImeiList.size());
        for (String imei : vehicleImeiList) {
            log.debug("imei before {}", imei);
            VehicleBatteryDataProjection vehicleBatteryData = telemetryBatteryRepository.findBatteryAggregateFields(imei, fiveMinutesPrior, now);
            VehicleBatteryThresholdsProjection vehicleBatteryThresholds = telemetryBatteryRepository.getThreshold(imei);
            if (vehicleBatteryData != null && vehicleBatteryThresholds != null) {
                log.debug("checking for battery alerts for imei {}", vehicleBatteryData.getImei());
                checkVehicleBatteryThresholds(imei, vehicleBatteryData, vehicleBatteryThresholds);
            }
            MotorDataProjection motorData = motorDataRepository.findMotorAggregateFields(imei, fiveMinutesPrior, now);
            MotorThresholdsProjection motorThresholds = telemetryBatteryRepository.getMotorThreshold(imei);
            if (motorData != null && motorThresholds != null) {
                log.debug("checking for the motor alerts for imei {}", motorData.getImei());
                checkMotorThresholds(imei, motorData, motorThresholds);
            }
        }
    }

    public void checkVehicleBatteryThresholds(String imei, VehicleBatteryDataProjection vehicleBatteryData, VehicleBatteryThresholdsProjection vehicleBatteryThresholds) {
        long vehicleId = Long.parseLong(vehicleBatteryData.getVehicleId());
        Instant timeBucket = vehicleBatteryData.getTimeBucketMin();
        log.debug(" battery thresholds check ");
        checkThresholdAndAddAlertAbove(imei, vehicleId, timeBucket, vehicleBatteryData.getMaxBatteryVolt(), vehicleBatteryThresholds.getBatteryVoltAboveNominalInfo(), vehicleBatteryThresholds.getBatteryVoltAboveNominalWarning(), vehicleBatteryThresholds.getBatteryVoltAboveNominalCritical(), Metric.BATTERY_VOLT, PartType.BATTERY);
        checkThresholdAndAddAlertAbove(imei, vehicleId, timeBucket, vehicleBatteryData.getMaxCurrent(), vehicleBatteryThresholds.getCurrentAboveNominalInfo(), vehicleBatteryThresholds.getCurrentAboveNominalWarning(), vehicleBatteryThresholds.getCurrentAboveNominalCritical(), Metric.CURRENT, PartType.BATTERY);
        checkThresholdAndAddAlertAbove(imei, vehicleId, timeBucket, vehicleBatteryData.getMaxTemperatureMax(), vehicleBatteryThresholds.getTemperatureAboveNominalInfo(), vehicleBatteryThresholds.getTemperatureAboveNominalWarning(), vehicleBatteryThresholds.getTemperatureAboveNominalCritical(), Metric.TEMPERATURE_MAX, PartType.BATTERY);
        checkThresholdAndAddAlertBelow(imei, vehicleId, timeBucket, vehicleBatteryData.getMinBatteryVolt(), vehicleBatteryThresholds.getBatteryVoltBelowNominalInfo(), vehicleBatteryThresholds.getBatteryVoltBelowNominalWarning(), vehicleBatteryThresholds.getBatteryVoltBelowNominalCritical(), Metric.BATTERY_VOLT, PartType.BATTERY);
        checkThresholdAndAddAlertBelow(imei, vehicleId, timeBucket, vehicleBatteryData.getMinCurrent(), vehicleBatteryThresholds.getCurrentBelowNominalInfo(), vehicleBatteryThresholds.getCurrentBelowNominalWarning(), vehicleBatteryThresholds.getCurrentBelowNominalCritical(), Metric.CURRENT, PartType.BATTERY);
        checkThresholdAndAddAlertBelow(imei, vehicleId, timeBucket, vehicleBatteryData.getMinTemperatureMin(), vehicleBatteryThresholds.getTemperatureBelowNominalInfo(), vehicleBatteryThresholds.getTemperatureBelowNominalWarning(), vehicleBatteryThresholds.getTemperatureBelowNominalCritical(), Metric.TEMPERATURE_MIN, PartType.BATTERY);

    }

    public void checkMotorThresholds(String imei, MotorDataProjection motorData, MotorThresholdsProjection motorThresholds) {
        long vehicleId = Long.parseLong(motorData.getVehicleId());
        Instant timeBucket = motorData.getTimeBucketMin();
        log.debug(" motor thresholds check ");
        checkThresholdAndAddAlertBelow(imei, vehicleId, timeBucket, motorData.getMinMotorDcVoltage(), motorThresholds.getMotorDcVoltageBelowNominalInfo(), motorThresholds.getMotorDcVoltageBelowNominalWarning(), motorThresholds.getMotorDcVoltageBelowNominalCritical(), Metric.MOTOR_DC_VOLTAGE, PartType.MOTOR);
        checkThresholdAndAddAlertAbove(imei, vehicleId, timeBucket, motorData.getMaxMotorDcVoltage(), motorThresholds.getMotorDcVoltageAboveNominalInfo(), motorThresholds.getMotorDcVoltageAboveNominalWarning(), motorThresholds.getMotorDcVoltageAboveNominalCritical(), Metric.MOTOR_DC_VOLTAGE, PartType.MOTOR);
        checkThresholdAndAddAlertAbove(imei, vehicleId, timeBucket, motorData.getMaxMotorDcCurrent(), motorThresholds.getMotorDcCurrentAboveNominalInfo(), motorThresholds.getMotorDcCurrentAboveNominalWarning(), motorThresholds.getMotorDcCurrentAboveNominalCritical(), Metric.MOTOR_DC_CURRENT, PartType.MOTOR);
        checkThresholdAndAddAlertAbove(imei, vehicleId, timeBucket, motorData.getMaxMotorMcsTemperature(), motorThresholds.getMcsTemperatureAboveNominalInfo(), motorThresholds.getMcsTemperatureAboveNominalWarning(), motorThresholds.getMcsTemperatureAboveNominalCritical(), Metric.MOTOR_MCS_TEMPERATURE, PartType.MCU);
        checkThresholdAndAddAlertBelow(imei, vehicleId, timeBucket, motorData.getMinMotorMcsTemperature(), motorThresholds.getMcsTemperatureBelowNominalInfo(), motorThresholds.getMcsTemperatureBelowNominalWarning(), motorThresholds.getMcsTemperatureBelowNominalCritical(), Metric.MOTOR_MCS_TEMPERATURE, PartType.MCU);
    }

    public void checkThresholdAndAddAlertAbove(String imei, long vehicleId, Instant timeBucket, Float value, Float infoThreshold, Float warningThreshold, Float criticalThreshold, Metric metric, PartType partType) {
        if (value != null) {
            log.debug("value of the field is not null {}", value);

            if (infoThreshold != null && warningThreshold != null && value >= infoThreshold && value < warningThreshold) {
                log.debug("info {}", value);
                addAlert(imei, vehicleId, timeBucket, value, infoThreshold, metric, "above_nominal_info", partType);
            } else if (warningThreshold != null && criticalThreshold != null && value >= warningThreshold && value < criticalThreshold) {
                log.debug("warning {}", value);
                addAlert(imei, vehicleId, timeBucket, value, warningThreshold, metric, "above_nominal_warning", partType);
            } else if (criticalThreshold != null && value >= criticalThreshold) {
                log.debug("critical {}", value);
                addAlert(imei, vehicleId, timeBucket, value, criticalThreshold, metric, "above_nominal_critical", partType);
            }
        }

    }

    public void checkThresholdAndAddAlertBelow(String imei, long vehicleId, Instant timeBucket, Float value, Float infoThreshold, Float warningThreshold, Float criticalThreshold, Metric metric, PartType partType) {
        if (value != null) {
            if (infoThreshold != null && warningThreshold != null && value <= infoThreshold && value > warningThreshold) {
                log.debug("info {}, infoThreshold {} ", value, infoThreshold);
                addAlert(imei, vehicleId, timeBucket, value, infoThreshold, metric, "below_nominal_info", partType);
            } else if (warningThreshold != null && criticalThreshold != null && value <= warningThreshold && value > criticalThreshold) {
                log.debug("warning {} , warningThreshold {}", value, warningThreshold);
                addAlert(imei, vehicleId, timeBucket, value, warningThreshold, metric, "below_nominal_warning", partType);
            } else if (criticalThreshold != null && value <= criticalThreshold) {
                log.debug("critical {} , criticalThreshold {} ", value, criticalThreshold);
                addAlert(imei, vehicleId, timeBucket, value, criticalThreshold, metric, "below_nominal_critical", partType);
            }
        }
    }

    @Transactional
    public void addAlert(String imei, long vehicleId, Instant timeBucket, Float value, Float thresholdData, Metric metric, String category, PartType partType) {
        Optional<RangeAlert> existingAlert = rangeAlertRepository.findByImeiAndTimeBucketMinuteAndMetricAndPartTypeAndCategory(imei, timeBucket, metric, partType, category);

        if (existingAlert.isPresent()) {
            log.info("Alert already exists for imei: {}, time_bucket_minute: {}, metric: {}, part_type: {}, category: {}. Skipping save.", imei, timeBucket, metric, partType, category);
        } else {
            RangeAlert rangeAlert = new RangeAlert();
            rangeAlert.setImei(imei);
            rangeAlert.setPartType(partType);
            rangeAlert.setCategory(category);
            rangeAlert.setVehicleId(vehicleId);
            rangeAlert.setThresholdValue(thresholdData);
            rangeAlert.setCurrentValue(value);
            rangeAlert.setMetric(metric);
            rangeAlert.setTimeBucketMinute(timeBucket);
            log.info("Creating new alert for imei: {}, time_bucket_minute: {}, metric: {}, part_type: {}, category: {}", imei, timeBucket, metric, partType, category);
            rangeAlertRepository.save(rangeAlert);// Save the new alert
            processPartAlertCount(rangeAlert, category);
        }
    }

    private void processPartAlertCount(RangeAlert rangeAlert, String category) {
        if (category.contains("critical")) {
            PartType partType = rangeAlert.getPartType();
            if (partType == PartType.BATTERY || partType == PartType.MOTOR || partType == PartType.MCU) {
                log.debug("partType : {}", partType.name());
                Long partId = partRepository.getPartIdByImeiAndPartType(rangeAlert.getImei(), partType.name());
                PartHealthMonitorDto partHealthMonitorDto = new PartHealthMonitorDto(partId, partType, "alert", 1);
                rabbitTemplate.convertAndSend(partHealthMonitorQueue, partHealthMonitorDto);
            }
        }
    }


    @Transactional
    @Scheduled(cron = "0 */1 * * * *", zone = "UTC")
    public void batteryAlarmCron() {
        Instant now = Instant.now().truncatedTo(ChronoUnit.SECONDS);
        Instant oneMinutePrior = now.minus(Duration.ofMinutes(1));
        log.debug("start time :{}  end time :{}  alarm cron ", oneMinutePrior, now);
        List<ImeiPartTypeCountProjection> imeiPartTypeCountProjectionList = batteryAlarmRepository.getImeiPartTypeCountInLastMinute(oneMinutePrior, now, PartType.BMS.name(), PartType.MCU.name());
        if (imeiPartTypeCountProjectionList != null && !imeiPartTypeCountProjectionList.isEmpty()) {
            for (ImeiPartTypeCountProjection imeiPartTypeCountProjection : imeiPartTypeCountProjectionList) {
                String imei = imeiPartTypeCountProjection.getImei();
                String partType = imeiPartTypeCountProjection.getPartType();
                int count = imeiPartTypeCountProjection.getCount();
                log.debug("imei : {} , PartType : {} , Count : {} ", imei, partType, count);
                PartType resultPartType = PartType.MCU;
                Long partId;
                if (partType.equals(PartType.BMS.name())) {
                    partId = partRepository.getPartIdByImeiAndPartType(imei, PartType.BATTERY.name());
                    resultPartType = PartType.BATTERY;
                } else {
                    partId = partRepository.getPartIdByImeiAndPartType(imei, partType);
                }
                PartHealthMonitorDto partHealthMonitorDto = new PartHealthMonitorDto(partId, resultPartType, "alarm", count);
                rabbitTemplate.convertAndSend(partHealthMonitorQueue, partHealthMonitorDto);
                log.debug("Published {} message to queue: {}", resultPartType, partHealthMonitorDto);
            }
        }
    }
}
