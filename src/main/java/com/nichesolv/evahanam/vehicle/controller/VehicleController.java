package com.nichesolv.evahanam.vehicle.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.evahanam.util.UserOrganisationUtils;
import com.nichesolv.evahanam.vehicle.dto.*;
import com.nichesolv.evahanam.vehicle.enums.TimeFilter;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.enums.VehicleIdentifierTypes;
import com.nichesolv.evahanam.vehicle.exception.VehicleException;
import com.nichesolv.evahanam.vehicle.exception.VehicleNotFoundException;
import com.nichesolv.evahanam.vehicle.jpa.Metric;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import com.nichesolv.evahanam.vehicle.service.VehicleHealthService;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.usermgmt.user.exception.UserNotFoundException;
import com.nichesolv.usermgmt.user.model.user.BaseUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@SecurityRequirement(name = "Bearer Authentication")
public class VehicleController {


    IVehicleService evDeviceService;

    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;


    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    UserOrganisationUtils userOrganisationUtils;

    @Autowired
    VehicleHealthService vehicleHealthService;

    public VehicleController(IVehicleService evDeviceService) {
        this.evDeviceService = evDeviceService;
    }

    /**
     * List the vehicle by its various identifiers <li>imei</li><li>chassis no</li><li>vehicle id</li><li>registration no</li>
     * @param pageable
     * @param request
     * @return
     */
    @GetMapping("/vehicles/ids")
    @ReadOnly
    public List<VehicleIdsProjection> getVehicleListByIdentifier(
            @RequestParam(value = "vIdType",required = false) VehicleIdentifierTypes idType,
            @RequestParam(value = "pattern",required = false) String pattern,
            @PageableDefault(size = 10, page = 0, direction = Sort.Direction.ASC) Pageable pageable,
            HttpServletRequest request) {

        CustomOrganisation customOrganisation = httpRequestOriginUtil.getUserOrganisation(request);
        return evDeviceService.listVehiclesByIdentifier(customOrganisation, pageable,idType,pattern)
                ;
    }

    /***
     * Get Vehicle by imei
     * @param imei
     * @param id
     * @param identifier
     * @param idValue
     * @param request
     * @return
     * @throws VehicleException
     */
    @GetMapping("/vehicles")
    @ReadOnly
    public VehicleDto getVehicle(
            @RequestParam(value = "imei", required = false) String imei,
            @RequestParam(value = "id", required = false) Long id,
            @RequestAttribute (value = "identifier") String identifier,
            @RequestParam(value = "vIdVal",required = false) String idValue,
            HttpServletRequest request)
            throws VehicleException {
        if(identifier == null)
            throw new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_ID_VALIDATION"));
        return evDeviceService.find(Optional.ofNullable(identifier)).get();
    }

    /***
     * Create Vehicles
     * @param newVehicleDto
     * @param user
     * @return
     */
    @PostMapping({"v1/create-vehicle","/erp/vehicle"})
    public Long createVehicle(@Validated  @RequestBody ErpVehicleDto newVehicleDto , @AuthenticationPrincipal BaseUser user) {
        return evDeviceService.createVehicle(newVehicleDto , user);
    }

    /***
     * Paginated list of vehilce imei nos
     * @param pageable
     * @param request
     * @return
     */
    @GetMapping("/vehicles/by-imei")
    @ReadOnly
    public List<String> getVehicleList(
            @PageableDefault(size = 10, page = 0, direction = Sort.Direction.ASC) Pageable pageable,
            HttpServletRequest request) {
        CustomOrganisation customOrganisation = httpRequestOriginUtil.getUserOrganisation(request);
        return evDeviceService.listVehicles(customOrganisation, pageable);
    }

    /***
     * List all vehicle imei nos
     * @param request
     * @return
     */
    @GetMapping("/vehicles/by-imei/all")
    @ReadOnly
    public List<String> getVehicleListWithOutPagination(HttpServletRequest request) {
        CustomOrganisation customOrganisation = httpRequestOriginUtil.getUserOrganisation(request);
        return evDeviceService.listVehiclesWithOutPagination(customOrganisation);
    }

    /***
     * Update Vehicle's operational status
     * @param operationStatusDto
     */
    @PutMapping({"v1/update-vehicle-status","/erp/vehicle-status"})
    public void updateVehicleStatus(
            @RequestBody OperationStatusDto operationStatusDto) {
        evDeviceService.updateVehicleStatus(operationStatusDto);
    }

    /***
     * Add existing unassigned parts to a vehicle
     * @param addPartRequestDto
     * @param user
     */
    @PutMapping({"v1/add-part-to-vehicle","/erp/vehicle-parts"})
    public void addPartsToVehicle(@RequestBody AddPartRequestDto addPartRequestDto, @AuthenticationPrincipal BaseUser user) {

        evDeviceService.addPartToVehicle(addPartRequestDto , user);
    }

    @PutMapping({"v1/update-registration-details","/erp/vehicle-registration"})
    public void updateVehicleRegistrationDetails(
            @RequestBody VehicleRegistrationDto vehicleRegistrationDto) {
        evDeviceService.updateVehicleRegistrationDetails(vehicleRegistrationDto);
    }

    /***
     * Update dealership details
     * @param imei
     * @param dealershipName
     * @param identifier
     * @param idValue
     */
    @PutMapping({"v1/add-dealership-to-vehicle","/erp/vehicle-dealership"})
    public void updateDealershipToVehicle(
            @RequestParam(value = "imei",required = false) String imei,
            @RequestParam(value = "dealershipName") String dealershipName,
            @RequestAttribute (value = "identifier") String identifier,
            @RequestParam(value = "vIdVal",required = false) String idValue
    ) {
        evDeviceService.updateVehicleDealership(identifier, dealershipName);
    }

    @PostMapping({"v1/sell-vehicle","/erp/vehicle-ownership"})
    public void updateVehicleSale(
            @RequestBody SellVehicleRequestBody sellVehicleRequestBody, HttpServletRequest request
    ) throws UserNotFoundException {
        Optional.ofNullable(sellVehicleRequestBody.getImeis())
                .filter(imeis -> !imeis.isEmpty())
                .orElseThrow(() -> new VehicleException(evMessageBundle.getMessage("IMEI_LIST_VALIDATION")));
        evDeviceService.sellVehicle(sellVehicleRequestBody, request);
    }


    /***
     * Return odometer reading
     * @param imei
     * @param identifier
     * @param idValue
     * @return
     */
    @Deprecated
//    @GetMapping("/vehicles/odometer")
//    @ReadOnly
    public Float getVehicleOdometerReading(@RequestParam(value = "imei",required = false) String imei,
                                    @RequestAttribute (value = "identifier") String identifier,
                                    @RequestParam(value = "vIdVal",required = false) String idValue) {
        return evDeviceService.getLatestOdometerReadingThroughMotorSpeed(identifier);
    }

    @GetMapping("/vehicles/by-part")
    @ReadOnly
    public String getVehicleByPart(@RequestParam String serialNumber) {
        return evDeviceService.getVehicleBySerialNumber(serialNumber);
    }

    @GetMapping("/vehicles/drive-mode/range")
    @ReadOnly
    public List<DriveModeAndRangeDto> getVehicleDriveModesAndRanges(@RequestParam(value = "imei",required = false) String imei,
                                                                    @RequestAttribute (value = "identifier") String identifier,
                                                                    @RequestParam(value = "vIdVal",required = false) String idValue) {
        return evDeviceService.getDriveModesAndRangesByImei(identifier);
    }

    /***
     * Return a Parts attribute
     * @param imei
     * @param partType
     * @param attributeName
     * @param identifier
     * @param idValue
     * @return
     */
    @GetMapping("/vehicles/parts/attribute")
    @ReadOnly
    public VehiclePartAttributeDetailsDto getVehiclePartAttributes(@RequestParam(value = "imei",required = false) String imei, @RequestParam PartType partType, @RequestParam(required = false) String attributeName,
                                                                   @RequestAttribute (value = "identifier") String identifier,
                                                                   @RequestParam(value = "vIdVal",required = false) String idValue) {
        return evDeviceService.getPartAttributesByImei(identifier, partType, attributeName);
    }

    /***
     * Distance travelled by vehicle in given time range
     * @param imei
     * @param from
     * @param to
     * @param timeFilter
     * @param identifier
     * @param idValue
     * @return
     */
    @GetMapping("/vehicles/distance")
    @ReadOnly
    public VehicleDistanceDto getVehicleDistance(@RequestParam(required = false) String imei , @RequestParam Long from, @RequestParam Long to,
                                                 @RequestParam TimeFilter timeFilter,
                                                 @RequestAttribute (value = "identifier") String identifier,
                                                 @RequestParam(value = "vIdVal",required = false) String idValue) {
        return evDeviceService.getVehicleDistanceBetween(identifier, from, to, timeFilter);
    }

    /***
     * Get vehicle status's of all possible states
     * @param pageable
     * @param status
     * @param fleetId
     * @param orgId
     * @param request
     * @return
     */
    @GetMapping("/vehicles/status")
    @ReadOnly
    public VehicleStatusListDto getVehicleStatuses(@PageableDefault(size = 10, page = 0, sort = "imei") Pageable pageable, @RequestParam Optional<String> status, @RequestParam Optional<Long> fleetId, @RequestParam Optional<Long> orgId,
                                           HttpServletRequest request) {
        Long organisationId = orgId.orElseGet(() -> httpRequestOriginUtil.getUserOrganisation(request).getId());
        return evDeviceService.getVehicleByStatus(fleetId, status, organisationId, pageable);
    }

    @GetMapping("/vehicles/status/all")
    @ReadOnly
    public VehicleStatusListAllDto getVehiclesByVehicleStateWithOutPagination(@RequestParam Optional<VehicleState> status, @RequestParam Optional<Long> orgId, @RequestParam Optional<Long> fleetId, HttpServletRequest request) {
        Long organisationId = orgId.orElseGet(() -> httpRequestOriginUtil.getUserOrganisation(request).getId());
        return evDeviceService.getVehiclesByVehicleStateWithOutPagination(fleetId, status, organisationId);
    }

    @GetMapping("/vehicles/status/count")
    @ReadOnly
    public VehicleStatusCountDto getCount(@RequestParam Optional<Long> fleetId ,@RequestParam Optional<Long> orgId,HttpServletRequest request){
        Long organisationId = orgId.orElseGet(() -> httpRequestOriginUtil.getUserOrganisation(request).getId());

        return evDeviceService.getVehicleCount(fleetId, organisationId);
    }

    @GetMapping("/vehicles/status/current")
    @ReadOnly

    public String getVehicleCurrentStatus(@RequestParam(required = false) String imei,@RequestParam Optional<Long> orgId,HttpServletRequest request,
                            @RequestAttribute (value = "identifier",required = false) String identifier,
                            @RequestParam(value = "vIdVal",required = false) String idValue) {
        Long organisationId = orgId.orElseGet(() -> httpRequestOriginUtil.getUserOrganisation(request).getId());
        return evDeviceService.getVehicleState(identifier,organisationId);
    }

    @GetMapping("/vehicles/details")
    @ReadOnly
    public VehicleFieldDto getVehicleDetail(@PageableDefault(size = 10, page = 0, sort = "mfrDate") Pageable pageable, @ModelAttribute SearchFilterDto searchFilter, @RequestParam Optional<Long> orgId, HttpServletRequest request) {
        Long organisationId = orgId.orElseGet(() -> httpRequestOriginUtil.getUserOrganisation(request).getId());
        return evDeviceService.getVehicleByFilter(searchFilter, organisationId, pageable);
    }

    @GetMapping("/vehicles/graph")
    @ReadOnly
    public GraphDataResponse getVehicleGraphData(@RequestParam(required = false) String imei , @RequestParam Metric metric, @RequestParam Long startTime , @RequestParam Long endTime , @RequestParam String interval, HttpServletRequest httpServletRequest,
                                                 @RequestAttribute("identifier") String identifier,
                                                 @RequestParam(value = "vIdVal",required = false) String identifierValue) {
        return evDeviceService.getVehicleGraphData(identifier, metric, startTime, endTime, interval);
    }


    @GetMapping("/vehicles/health")
    @ReadOnly
    public VehicleHealthResultDto getVehicleHealth(@RequestParam Optional<String> imei,
                                                   @RequestAttribute("identifier") String identifier,
                                                   @RequestParam(value = "vIdVal",required = false) String identifierValue) {
//        Long organisationId = httpRequestOriginUtil.getUserOrganisation(request).getId();
//        log.info("inside the controller  {}", organisationId);
        return vehicleHealthService.getHealth(identifier);
    }

    @GetMapping("/vehicles/running-statistics")
    @ReadOnly
    public VehicleRunningStats getVehicleRunningStats(@RequestParam Optional<Long> orgId, @RequestParam Long startTime, @RequestParam Long endTime, @PageableDefault(size = 10, page = 0) Pageable pageable , HttpServletRequest request) {
        CustomOrganisation organisation;
        if (orgId.isPresent()) {
            organisation = userOrganisationUtils.getOrganisationById(orgId.get());
        } else {
            organisation = httpRequestOriginUtil.getUserOrganisation(request);
        }
        return evDeviceService.getVehicleRunningStats(organisation, startTime, endTime,pageable);
    }


    @GetMapping("/vehicles/{vehicleId}/parts")
    @ReadOnly
    public List<PartReplacementDto> getParts(@PathVariable Long vehicleId) {
        return evDeviceService.getPartsAssociated(vehicleId);
    }

    @Operation(summary = "Get vehicle overview analytics", description = "Returns top-level vehicle statistics like top speed, trip count ,etc")
    @GetMapping("/vehicles/overview")
    @ReadOnly
    public AnalyticsDto getVehicleAnalytics(@RequestAttribute("identifier") String identifier,
                                            @RequestParam(value = "vIdVal", required = false) String vehicleId) {
        return evDeviceService.getVehicleAnalytics(identifier);
    }
}
