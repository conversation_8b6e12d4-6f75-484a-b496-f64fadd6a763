package com.nichesolv.evahanam.vehicle.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.evahanam.vehicle.dto.*;
import com.nichesolv.evahanam.vehicle.dto.metadata.FleetMetaDataApiDto;
import com.nichesolv.evahanam.vehicle.enums.FleetStatus;
import com.nichesolv.evahanam.vehicle.service.FleetService;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.usermgmt.user.exception.role.RoleNotFoundException;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;

@RestController
@RequestMapping("/fleets")
@SecurityRequirement(name = "Bearer Authentication")
public class FleetController {

    private static final Logger log = LoggerFactory.getLogger(FleetController.class);
    @Autowired
    FleetService fleetService;

    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;

    @PostMapping
    public FleetResponseDto saveFleet(@RequestBody @Validated FleetRequestDto fleetRequestDto) {
        return fleetService.saveFleet(fleetRequestDto);
    }

    @GetMapping("/{id}")
    @ReadOnly
    public List<FleetResponseDto> getFleetsById(@PathVariable Long id) {
        return fleetService.getFleetById(id);
    }

    @GetMapping
    @ReadOnly
    public List<FleetResponseDto> getFleetsByOrgId(@RequestParam(name = "status",defaultValue = "ACTIVE",required = false) FleetStatus status, HttpServletRequest request) {
        CustomOrganisation customOrganisation = httpRequestOriginUtil.getUserOrganisation(request);
        return fleetService.getFleetByOrgIdAndStatus(customOrganisation,status);
    }

    @GetMapping("/org/{fleetId}")
    @ReadOnly
    public List<FleetResponseDto> getOrgFleetsById( @PathVariable Long fleetId,HttpServletRequest request) {
        CustomOrganisation customOrganisation = httpRequestOriginUtil.getUserOrganisation(request);
        return fleetService.getFleetByOrgAndFleetId(fleetId,customOrganisation);
    }

    @PutMapping
    public FleetResponseDto updateFleet(@RequestBody FleetUpdateDto fleetUpdateDto) {
        return fleetService.updateFleet(fleetUpdateDto);
    }

    @GetMapping("/vehicle-models")
    @ReadOnly
    @PreAuthorize("hasAuthority('SUPER_ADMIN') || hasAuthority('Admin') || hasAuthority('Fleet Manager') || hasAuthority('Executive')")
    public List<FleetVehicleModelResponseDto> getFleetVehicleModels(Long fleetId) throws RoleNotFoundException {
           return fleetService.getFleetVehicleModels(fleetId);
    }

    @PostMapping("/users")
    public FleetUserDto addUserToFleets(@RequestBody FleetUserDto fleetUserDto, HttpServletRequest request) {
        CustomOrganisation customOrganisation = httpRequestOriginUtil.getUserOrganisation(request);
        return fleetService.addUserToFleets(fleetUserDto.getUserId(), fleetUserDto.getFleetIds(), customOrganisation);
    }

    @PostMapping("/meta-data")
    public FleetMetaDataApiDto getMetaData(@RequestBody com.nichesolv.evahanam.vehicle.dto.FleetMetaDataApiDto fleetMetaDataApiDto,
                                           HttpServletRequest request) {
        httpRequestOriginUtil.getUserOrganisation(request);
        return fleetService.getFleetMetaData(fleetMetaDataApiDto.getFleetId(), fleetMetaDataApiDto.getFrom(), fleetMetaDataApiDto.getTo(), fleetMetaDataApiDto.getImei());
    }

    @PostMapping("/vehicles")
    public FleetVehicleDetailsApiResponseDto getFleetVehiclesDetails(@RequestBody FleetVehicleDetailsApiDto fleetVehicleDetailsApiDto,HttpServletRequest request) {
        httpRequestOriginUtil.getUserOrganisation(request);
        Pageable pageable = PageRequest.of(fleetVehicleDetailsApiDto.getPageNo(), fleetVehicleDetailsApiDto.getPageSize(),Sort.by(Sort.Direction.fromString(fleetVehicleDetailsApiDto.getSortDirection()), fleetVehicleDetailsApiDto.getSortBy()));
        return fleetService.getFleetVehiclesDetails(fleetVehicleDetailsApiDto.getFleetId(), fleetVehicleDetailsApiDto.getFrom(), fleetVehicleDetailsApiDto.getTo(), fleetVehicleDetailsApiDto.getImei(),pageable);
    }


}
