package com.nichesolv.evahanam.vehicle.filter;

import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.evahanam.vehicle.dto.VehicleIdInfo;
import com.nichesolv.evahanam.util.VehicleIdentifierUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Optional;

public class VehicleOwnershipValidationFilter extends OncePerRequestFilter {


    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;

    @Autowired
    VehicleIdentifierUtil vehicleIdentifierUtil;

    public VehicleOwnershipValidationFilter(HttpRequestOriginUtil httpRequestOriginUtil, VehicleIdentifierUtil vehicleIdentifierUtil) {
        this.httpRequestOriginUtil = httpRequestOriginUtil;
        this.vehicleIdentifierUtil = vehicleIdentifierUtil;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        try {
            if ("GET".equalsIgnoreCase(request.getMethod())) {

                VehicleIdInfo vehicleIdInfo = vehicleIdentifierUtil.getVehicleIdentifier(request);
                Optional.ofNullable(vehicleIdInfo).map(VehicleIdInfo::getIdentifier).ifPresent(e-> {
                    httpRequestOriginUtil.checkVehicleBelongToOrganisation(e, null, request);
                });
            }
            filterChain.doFilter(request, response);
        } catch (AccessDeniedException e) {
            String msg = e.getMessage();
            int statusValue = HttpStatus.BAD_REQUEST.value();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write("{\"message\":\"" + msg + "\",\"status\":" + statusValue + "}");
            response.setStatus(statusValue);
        }

    }
}
