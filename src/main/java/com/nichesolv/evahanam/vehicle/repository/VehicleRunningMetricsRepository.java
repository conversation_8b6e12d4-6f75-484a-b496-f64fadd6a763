package com.nichesolv.evahanam.vehicle.repository;

import com.nichesolv.evahanam.vehicle.dto.AverageMedianDataFromVehicleMetricsProjection;
import com.nichesolv.evahanam.vehicle.dto.AverageMedianProjection;
import com.nichesolv.evahanam.vehicle.dto.MaxAndMinTimeStampProjection;
import com.nichesolv.evahanam.vehicle.dto.VehicleTotalDistanceProjection;
import com.nichesolv.evahanam.vehicle.dto.metadata.FleetVehicleDetailsProjections;
import com.nichesolv.evahanam.vehicle.dto.metadata.VehicleMinMaxData;
import com.nichesolv.evahanam.vehicle.jpa.VehicleRunningMetrics;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface VehicleRunningMetricsRepository extends JpaRepository<VehicleRunningMetrics, Long> {

    @Query(value = "select COALESCE(sum(distance_travelled),0.0) from vehicle_running_metrics where imei= ?3 and " +
            "timestamp between ?1 and ?2", nativeQuery = true)
    Float getTotalDistanceTravelledBetweenTimeAndImei(Instant from, Instant to, String imei);

    @Query(value = "select count(distinct(imei)) from vehicle_running_metrics where imei in ?1 and " +
            "timestamp between ?2 and ?3 ",nativeQuery = true)
    Long getCountOfDistinctImeiTimeBetween(List<String> imei, Instant from, Instant to);

    @Query(value = "select sum(distance_travelled) from vehicle_running_metrics where imei in ?3 and " +
            "timestamp between ?1 and ?2",nativeQuery = true)
    Long getTotalDistanceTravelledBetweenTimeAndImeiIn(Instant from, Instant to, List<String> imei);

    @Query(value = "select vdd.imei as imei,sum(vdd.distance_travelled) as distance from vehicle_running_metrics vdd where vdd.imei in ?3 and " +
            "vdd.timestamp between ?1 and ?2 group by vdd.imei ",nativeQuery = true)
    List<VehicleMinMaxData> getTopRunningVehicleByAscOrDescBetweenTimeAndVehicleIn(Instant from, Instant to, List<String> imei, Pageable pageable);


    @Query(value = "select imei as imei,sum(distance_travelled) as distanceTravelled from vehicle_running_metrics where timestamp between ?1 and ?2 " +
            "and imei in ?3 group by imei ", nativeQuery = true)
    List<VehicleTotalDistanceProjection> getTotalDistanceTravelledBetweenTimestampAndVehicleIn(Instant from, Instant to, List<String> imei);

    /***
     * Query to return distance travelled by a list of vehicles ,also returning the Vehicle Identifiers
     * @param from
     * @param to
     * @param imei
     * @param pageable
     * @return List VehicleMinMaxData
     */
    @Query(value = "select vdd.imei as imei,sum(vdd.distance_travelled) as distance " +
            ",vrd.registration_number as vehRegNo" +
            ",v.id as vehId" +
            ",v.chassis_number as vehChassisNo " +
            ",v.imei as vehImei " +
            "from vehicle v " +
            "join vehicle_running_metrics vdd on vdd.imei=v.imei " +
            "left join vehicle_registration_details vrd on vrd.vehicle_id=v.id " +
            "where vdd.imei in ?3 " +
            "and vdd.timestamp between ?1 and ?2 " +
            "group by vdd.imei,vehRegNo,vehId,vehChassisNo,vehImei",nativeQuery = true)
    List<VehicleMinMaxData> getTopRunningVehiclesByAscOrDescBetweenTimeAndVehicleIn(Instant from, Instant to, List<String> imei, Pageable pageable);

    VehicleRunningMetrics findFirstByTelemetryIdxImeiOrderByTelemetryIdxTimestampDesc(String imei);

    @Query(value = "select max(timestamp) as maxTimestamp,min(timestamp) as minTimestamp from vehicle_running_metrics where imei in ?1 " +
            "and timestamp between ?2 and ?3 ",nativeQuery = true)
    MaxAndMinTimeStampProjection findMaxTimeStampAndMinTimeImeiInAndTimestampBetween(List<String> imei, Instant startTime, Instant endTime);

    Long countByTelemetryIdxImeiInAndTelemetryIdxTimestampBetween(List<String> imei,Instant startTime,Instant endTime);

    @Query(value = "select " +
            "round(coalesce(cast(AVG(distance) as decimal),0),2) AS averageDistance, " +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY distance)as decimal),0),2) AS medianDistance, " +
            "round(coalesce(cast(AVG(range) as decimal),0),2) AS averageRange, " +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY range)as decimal),0),2) AS medianRange, " +
            "round(coalesce(cast(AVG(usage_time) as decimal),0),2) AS averageUsageTime, " +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY usage_time)as decimal),0),2) AS medianUsageTime, " +
            "round(coalesce(cast(AVG(charging_time) as decimal),0),2) AS averageChargingTime, " +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY charging_time)as decimal),0),2) AS averageChargingTime " +
            "from view_vehicle_daily_usage vvdu " +
            "where vvdu.imei in ?1 " +
            "and date between ?2 and ?3",nativeQuery = true)
    AverageMedianDataFromVehicleMetricsProjection getAvgAndMedDataAndImeiInAndTimestampBetween(List<String> imei, Instant startTime, Instant endTime);

    @Query(value = "with distance_average_data as( " +
            "select imei,sum(distance) as distance " +
            "from view_vehicle_daily_usage " +
            "where imei in ?1 " +
            "and date between ?2 and ?3 " +
            "group by imei " +
            "order by distance desc limit 5) " +
            "SELECT " +
            "round(coalesce(cast(AVG(distance) as decimal),0),2) AS average, " +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY distance)as decimal),0),2) AS median " +
            "FROM distance_average_data;",nativeQuery = true)
    AverageMedianProjection getTop5VehicleAvgAndMedDistanceData(List<String> imei,Instant startTime, Instant endTime);

    @Query(value = "with distance_average_data as( " +
            "select imei,sum(distance) as distance " +
            "from view_vehicle_daily_usage " +
            "where imei in ?1 " +
            "and date between ?2 and ?3 " +
            "group by imei " +
            "order by distance asc limit 5) " +
            "SELECT " +
            "round(coalesce(cast(AVG(distance) as decimal),0),2) AS average, " +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY distance)as decimal),0),2) AS median " +
            "FROM distance_average_data;",nativeQuery = true)
    AverageMedianProjection getBottom5VehicleAvgAndMedDistanceData(List<String> imei, Instant startTime, Instant endTime);

    @Query(value = "with usage_time_average_data as( " +
            "select imei,sum(usage_time) as usage_time " +
            "from view_vehicle_daily_usage " +
            "where imei in ?1 " +
            "and date between ?2 and ?3 " +
            "group by imei " +
            "order by usage_time desc limit 5) " +
            "SELECT " +
            "round(coalesce(cast(AVG(usage_time) as decimal),0),2) AS average, " +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY usage_time)as decimal),0),2) AS median " +
            "FROM usage_time_average_data;",nativeQuery = true)
    AverageMedianProjection getTop5VehicleAvgAndMedUsageTimeData(List<String> imei,Instant startTime, Instant endTime);

    @Query(value = "with usage_time_average_data as( " +
            "select imei,sum(usage_time) as usage_time " +
            "from view_vehicle_daily_usage " +
            "where imei in ?1 " +
            "and date between ?2 and ?3 " +
            "group by imei " +
            "order by usage_time asc limit 5) " +
            "SELECT " +
            "round(coalesce(cast(AVG(usage_time) as decimal),0),2) AS average, " +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY usage_time)as decimal),0),2) AS median " +
            "FROM usage_time_average_data;",nativeQuery = true)
    AverageMedianProjection getBottom5VehicleAvgAndMedUsageTimeData(List<String> imei,Instant startTime, Instant endTime);

    @Query(value = "with range_average_data as( " +
            "select imei,sum(range) as range " +
            "from view_vehicle_daily_usage " +
            "where imei in ?1 " +
            "and date between ?2 and ?3 " +
            "group by imei " +
            "order by range desc limit 5) " +
            "SELECT " +
            "round(coalesce(cast(AVG(range) as decimal),0),2) AS average, " +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY range)as decimal),0),2) AS median " +
            "FROM range_average_data ",nativeQuery = true)
    AverageMedianProjection getTop5VehicleAvgAndMedRangeData(List<String> imei,Instant startTime, Instant endTime);

    @Query(value = "with range_average_data as( " +
            "select imei,sum(range) as range " +
            "from view_vehicle_daily_usage " +
            "where imei in ?1 " +
            "and date between ?2 and ?3 " +
            "group by imei " +
            "order by range asc limit 5) " +
            "SELECT " +
            "round(coalesce(cast(AVG(range) as decimal),0),2) AS average, " +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY range)as decimal),0),2) AS median " +
            "FROM range_average_data ",nativeQuery = true)
    AverageMedianProjection getBottom5VehicleAvgAndMedRangeData(List<String> imei,Instant startTime, Instant endTime);

    @Query(value = "with charging_time_average_data as( " +
            "select imei,sum(charging_time) as charging_time " +
            "from view_vehicle_daily_usage " +
            "where imei in ?1 " +
            "and date between ?2 and ?3 " +
            "group by imei " +
            "order by charging_time desc limit 5) " +
            "SELECT " +
            "round(coalesce(cast(AVG(charging_time) as decimal),0),2) AS average," +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY charging_time)as decimal),0),2) AS median " +
            "FROM charging_time_average_data ", nativeQuery = true)
    AverageMedianProjection getTop5VehicleAvgAndMedChargingData(List<String> imei, Instant startTime, Instant endTime);

    @Query(value = "with charging_time_average_data as( " +
            "select imei,sum(charging_time) as charging_time " +
            "from view_vehicle_daily_usage " +
            "where imei in ?1 " +
            "and date between ?2 and ?3 " +
            "group by imei " +
            "order by charging_time asc limit 5) " +
            "SELECT " +
            "round(coalesce(cast(AVG(charging_time) as decimal),0),2) AS average," +
            "round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY charging_time)as decimal),0),2) AS median " +
            "FROM charging_time_average_data ", nativeQuery = true)
    AverageMedianProjection getBottom5VehicleAvgAndMedChargingData(List<String> imei, Instant startTime, Instant endTime);


    @Query(value = "select " +
            "imei as imei, " +
            "round(coalesce(cast((sum(distance)/sum(range)) as decimal),0),2) as discharge, " +
            "sum(usage_time) as usageTime, " +
            "sum(distance) as distance, " +
            "sum(charging_time) as chargingTime " +
            "from view_vehicle_daily_usage " +
            "where date between ?2 and ?3 " +
            "and imei in ?1 " +
            "group by imei ",nativeQuery = true)
    Page<FleetVehicleDetailsProjections> getFleetVehiclesDetails(List<String> imei, Instant startTime, Instant endTime, Pageable pageable);

    Optional<VehicleRunningMetrics> findFirstByTelemetryIdxTimestampAndTelemetryIdxImei(Instant key, String imei);

    List<VehicleRunningMetrics> findByTelemetryIdxTimestampInAndTelemetryIdxImeiOrderByTelemetryIdxTimestampAsc(List<Instant> instants , String imei);

    @Query(value = "SELECT MAX(max_speed) FROM vehicle_running_metrics WHERE imei = ?1 ", nativeQuery = true)
    Double findMaxSpeedByImei(String imei);
}
