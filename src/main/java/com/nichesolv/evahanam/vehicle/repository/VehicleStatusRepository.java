package com.nichesolv.evahanam.vehicle.repository;

import com.nichesolv.evahanam.vehicle.enums.UpdateSource;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatusIdx;
import com.nichesolv.evahanam.trip.dto.tripsummary.VehicleStatusTimeWindowProjection;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static org.hibernate.jpa.HibernateHints.HINT_FETCH_SIZE;

@Repository
public interface VehicleStatusRepository extends JpaRepository<VehicleStatus, VehicleStatusIdx> {

    Optional<VehicleStatus> findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestamp(String imei,Instant timestamp);

    List<VehicleStatus> findByVehicleStatusIdxImei(String imei);

    List<VehicleStatus> findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetween(String imei, Instant startTime,Instant endTime);

    @Query(value = "WITH RankedStatus AS ( " +
            "    SELECT *,LAG(timestamp) OVER (ORDER BY timestamp) AS prev_timestamp " +
            "    FROM vehicle_status " +
            "    WHERE imei = ?1 " +
            "    AND update_source = ?2 " +
            "    AND updated_on between ?3 and ?4 ), " +
            "GroupedStatus AS ( " +
            "    SELECT *,SUM(CASE WHEN EXTRACT(EPOCH FROM (timestamp - prev_timestamp)) <= ?5 THEN 0 ELSE 1 END) OVER (ORDER BY timestamp) AS group_id " +
            "    FROM RankedStatus " +
            ") " +
            "SELECT MIN(timestamp) AS minTimestamp,MAX(timestamp) AS maxTimestamp " +
            "FROM GroupedStatus " +
            "GROUP BY group_id " +
            "ORDER BY minTimestamp ",nativeQuery = true)
    List<VehicleStatusTimeWindowProjection> getDataDelayTimeWindowsByImeiAndUpdateSourceAndUpdatedOnBetween(String imei,String updateSource,Instant startTime,Instant endTime,Integer groupingInterval);

    boolean existsByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenAndUpdateSource(String imei, Instant startTime, Instant endTime, UpdateSource updateSource);


    @QueryHints(value = {
            @QueryHint(name = HINT_FETCH_SIZE, value = "600"),
    })
    Stream<VehicleStatus> findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(String imei, Instant startTime, Instant endTime);

    List<VehicleStatus> findByVehicleStatusIdxImeiAndUpdateSource(String imei,UpdateSource updateSource);


    List<VehicleStatus> findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampIn(String imei,List<Instant> timestamp);

    @Query(value = "SELECT vs.vehicle_state, COUNT(vs.vehicle_state) " +
            "FROM vehicle_status vs " +
            "WHERE vs.imei = :imei " +
            "AND vs.timestamp BETWEEN :startTime AND :endTime " +
            "GROUP BY vs.vehicle_state",
            nativeQuery = true)
    List<Object[]> countVehicleStatesByImeiAndTimestampBetweenNative(String imei, Instant startTime, Instant endTime);

    Optional<VehicleStatus> findFirstByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampGreaterThanOrderByVehicleStatusIdxTimestampDesc(String imei,Instant timestamp);

    @Query(value = "SELECT COUNT(DISTINCT vs.vehicle_id) FROM vehicle_status vs WHERE vs.vehicle_state = ?1 AND vs.timestamp BETWEEN ?2 AND ?3 and vehicle_id in ?4 ", nativeQuery = true)
    long countVehicleStateVehiclesBetween(String vehicleState, Instant startTime, Instant endTime, List<Long> vehicleId);

}
