package com.nichesolv.evahanam.vehicle.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
public class VehicleLocationDto extends VehicleIdentifiers {
    String imei;
    Float latitude;
    Float longitude;

    public VehicleLocationDto(String vehChassisNo, String vehRegNo, String vehId, String imei, Float latitude, Float longitude) {
        super(vehChassisNo, vehRegNo, vehId, imei);
        this.imei = imei;
        this.latitude = latitude;
        this.longitude = longitude;
    }
}
