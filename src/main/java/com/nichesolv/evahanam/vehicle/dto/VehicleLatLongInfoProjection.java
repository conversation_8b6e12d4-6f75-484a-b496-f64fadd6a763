package com.nichesolv.evahanam.vehicle.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class VehicleLatLongInfoProjection extends VehicleIdentifiers implements VehicleLocationProjection {
    String imei;
    Float latitude;
    Float longitude;

    public VehicleLatLongInfoProjection(String vehChassisNo, String vehRegNo, String vehId, String vehImei, Float latitude, Float longitude) {
        super(vehChassisNo, vehRegNo, vehId, vehImei);
        this.imei = vehImei;
        this.latitude = latitude;
        this.longitude = longitude;
    }
}
