package com.nichesolv.evahanam.vehicle.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleDistanceDataDto  extends VehicleIdentifiers{
    String imei;
    Float distance;


    public VehicleDistanceDataDto(String vehChassisNo, String vehRegNo, String vehId, String imei, Float distance) {
        super(vehChassisNo,vehRegNo,vehId,imei);
        this.imei = imei;
        this.distance = distance;
    }
}
