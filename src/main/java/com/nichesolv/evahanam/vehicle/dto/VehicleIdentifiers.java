package com.nichesolv.evahanam.vehicle.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.Objects;

/**
 * Base class which has all the possible identifiers of a vehicle, to be returned in all API responses
 */
@NoArgsConstructor
@Getter
@Setter
@AllArgsConstructor
public class VehicleIdentifiers implements VehicleIdsProjection {
    String vehChassisNo;

    String vehRegNo;

    String vehId;

    String vehImei;

    public void setVehicleImei(String imei){
        this.vehImei = Objects.requireNonNull(imei);
    }
    public VehicleIdentifiers(String vehImei){
        this.vehImei = vehImei;
    }

}
