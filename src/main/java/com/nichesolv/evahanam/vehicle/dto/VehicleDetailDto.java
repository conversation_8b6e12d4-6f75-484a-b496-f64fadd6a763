package com.nichesolv.evahanam.vehicle.dto;


import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.time.LocalDate;

import static java.util.Objects.requireNonNull;


@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleDetailDto extends VehicleIdentifiers{
   String imei;
   String modelName;
   LocalDate mfrDate;
   Float netWeight;
   String color;
   String chassisNumber;
   OperationStatus operationStatus;
   Long vehicleModelId;

   public VehicleDetailDto(String vehChassisNo, String vehRegNo, String vehId, String vehImei) {
          super(vehChassisNo, vehRegNo, vehId, vehImei);
         }
}
