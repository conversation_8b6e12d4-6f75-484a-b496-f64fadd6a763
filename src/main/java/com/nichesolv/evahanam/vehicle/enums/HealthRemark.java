package com.nichesolv.evahanam.vehicle.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public enum HealthRemark {
    EXCELLENT(0, "Excellent" , "Low"),
    GOOD(1, "Good" , "Normal"),
    AVERAGE(2, "Average" , "High"),
    POOR(3, "Poor" , null);

    private final int value;
    @Getter
    private final String remark;
    @Getter
    private final String level;

    HealthRemark(int value, String remark , String level ) {
        this.remark = remark;
        this.value = value;
        this.level = level;
    }

    public static List<String> getRemarks() {
        List<String> remarks = new ArrayList<>();
        for (HealthRemark healthRemark : HealthRemark.values()) {
            remarks.add(healthRemark.getRemark());
        }
        return remarks;
    }

    public static List<String> getLevels() {
        List<String> levels = new ArrayList<>();
        for (HealthRemark healthRemark : HealthRemark.values()) {
            if (!healthRemark.level.equals(HealthRemark.POOR.level))
                levels.add(healthRemark.getLevel());
        }
        return levels;
    }

}
