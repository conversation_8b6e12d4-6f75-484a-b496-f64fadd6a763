package com.nichesolv.evahanam.vehicle.jpa;

import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicleModel.jpa.ColorModel;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import com.nichesolv.usermgmt.user.model.user.User;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@ToString
@Table(indexes = {
        @Index(name = "vehicle_model_idx", columnList = "vehicle_model_id"),
        @Index(name = "vehicle_manufacturer_idx", columnList = "mfr_org_id")
})
public class Vehicle {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  Long id;

  @NonNull
  @Column(unique = true, nullable = false)
  String imei;

  @NonNull
  @ManyToOne
  @JoinColumn(foreignKey = @ForeignKey(name = "fk_vehicle_model_id"))
  VehicleModel vehicleModel;

  @ManyToOne
  @JoinColumn(foreignKey = @ForeignKey(name = "fk_color_model_id"))
  ColorModel colorModel;

  @CreationTimestamp
  Instant createdOn;
  @UpdateTimestamp
  Instant modifiedOn;



  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "vehicle_parts",
      joinColumns = {@JoinColumn(name = "vehicle_id", nullable = false,foreignKey = @ForeignKey(name = "fk_vehicle_id"))}
      , inverseJoinColumns = {@JoinColumn(name = "part_id", nullable = false, foreignKey = @ForeignKey(name = "fk_part_id"))})
  Set<Part> vehicleParts = new HashSet<>();

  LocalDate mfrDate;

  @ManyToOne(targetEntity = CustomOrganisation.class)
  @JoinColumn(name = "owner_org_id",foreignKey = @ForeignKey(name = "fk_owner_id"))
  Organisation owner;

  @ManyToOne(targetEntity = CustomOrganisation.class)
  @JoinColumn(foreignKey = @ForeignKey(name = "fk_dealership_id"))
  Organisation dealership;

  @ManyToOne(targetEntity = CustomOrganisation.class)
  @JoinColumn(name = "mfr_org_id",foreignKey = @ForeignKey(name = "fk_owner_id"))
  Organisation manufacturer;

  @Enumerated(EnumType.STRING)
  OperationStatus operationStatus;
  Float totalDistanceTraveled;
  Instant odometerLastUpdatedAt;

  @NonNull
  @Column(unique = true, nullable = false)
  String chassisNumber;
  String encryptionKey;
  String deviceAdvertisingName;

  @ManyToOne(targetEntity = CustomUser.class)
  User user;

  Float latitude;
  Float longitude;
  Instant locationUpdatedAt;

  @Enumerated(EnumType.STRING)
  VehicleState vehicleState;

  public LocalDate getMfrDate() {
    return mfrDate;
  }

  public Long getId() {
    return id;
  }

  public String getImei() {
    return imei;
  }

  public VehicleModel getVehicleModel() {
    return vehicleModel;
  }

  public Instant getCreatedOn() {
    return createdOn;
  }

  public Set<Part> getVehicleParts() {
    return vehicleParts;
  }



  public Vehicle(@NonNull String imeiNumber, VehicleModel vehicleModel, Set<Part> vehicleParts,
      LocalDate manufacturedDate) {
    this.imei = imeiNumber;
    this.vehicleModel = vehicleModel;
    this.createdOn = Instant.now();
    this.modifiedOn = createdOn;
    this.vehicleParts = vehicleParts;
    this.mfrDate = manufacturedDate;
  }
}
