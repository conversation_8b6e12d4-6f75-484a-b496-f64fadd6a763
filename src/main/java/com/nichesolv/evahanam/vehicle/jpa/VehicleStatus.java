package com.nichesolv.evahanam.vehicle.jpa;

import com.nichesolv.evahanam.vehicle.enums.UpdateSource;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
@Table(indexes = {
        @Index(name = "vehicle_status_imei_idx", columnList = "imei"),
        @Index(name = "vehicle_status_timestamp_idx", columnList = "timestamp"),
        @Index(name = "vehicle_status_veh_id_idx", columnList = "vehicle_id")
})
public class VehicleStatus {

    @EmbeddedId
    VehicleStatusIdx vehicleStatusIdx;

    @ManyToOne
    @JoinColumn(name = "vehicle_id", foreignKey = @ForeignKey(name = "fk_vehicle_id"))
    Vehicle vehicle;

    @Enumerated(EnumType.STRING)
    VehicleState vehicleState;

    Float latitude;

    Float longitude;

    @UpdateTimestamp
    Instant updatedOn;

    @Enumerated(EnumType.STRING)
    UpdateSource updateSource;


    public VehicleStatus(VehicleStatusIdx vehicleStatusIdx, VehicleState vehicleState, Float latitude, Float longitude,UpdateSource updateSource) {
        this.vehicleStatusIdx = vehicleStatusIdx;
        this.vehicleState = vehicleState;
        this.latitude = latitude;
        this.longitude = longitude;
        this.updateSource=updateSource;
    }



    public VehicleStatus(VehicleStatusIdx vehicleStatusIdx, VehicleState vehicleState, Float latitude, Float longitude,Instant updatedOn,  UpdateSource updateSource) {
        this.vehicleStatusIdx = vehicleStatusIdx;
        this.vehicleState = vehicleState;
        this.latitude = latitude;
        this.updatedOn = updatedOn;
        this.longitude = longitude;
        this.updateSource = updateSource;
    }

    public VehicleStatus(VehicleStatusIdx vehicleStatusIdx,Vehicle vehicle,VehicleState vehicleState, Float latitude, Float longitude,  UpdateSource updateSource) {
        this.vehicleStatusIdx = vehicleStatusIdx;
        this.vehicleState = vehicleState;
        this.latitude = latitude;
        this.updatedOn = Instant.now();
        this.longitude = longitude;
        this.updateSource = updateSource;
        this.vehicle = vehicle;
    }

    public VehicleStatus(VehicleStatusIdx vehicleStatusIdx, VehicleState vehicleState, Float latitude, Float longitude,Instant updatedOn) {
        this.vehicleStatusIdx = vehicleStatusIdx;
        this.vehicleState = vehicleState;
        this.latitude = latitude;
        this.updatedOn  = updatedOn;
        this.longitude = longitude;
    }

}
