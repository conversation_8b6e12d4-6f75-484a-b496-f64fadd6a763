package com.nichesolv.evahanam.vehicle.jpa;

import com.nichesolv.evahanam.telemetryData.jpa.TelemetryIdx;
import com.nichesolv.evahanam.vehicleModel.jpa.DriveMode;
import jakarta.persistence.*;
import lombok.*;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@ToString
@Table(name = "vehicle_running_metrics", indexes = {
        @Index(name = "vehicle_running_metrics_imei_idx", columnList = "imei"),
        @Index(name = "vehicle_running_metrics_ts_idx", columnList = "timestamp"),
        @Index(name = "vehicle_running_metrics_veh_id_idx", columnList = "vehicle_id")
})
public class VehicleRunningMetrics {
    @EmbeddedId
    TelemetryIdx telemetryIdx;

    @ManyToOne
    @JoinColumn(name = "vehicle_id", foreignKey = @ForeignKey(name = "fk_vehicle_id"))
    Vehicle vehicle;

    Float distanceTravelled;

    @Column(name = "cal_soc")
    Double calculatedSoc;

    Double discharge;

    Float soc;

    Float avgSpeed;

    Float maxSpeed;

    @ManyToOne
    DriveMode driveMode;

    Double calDischarge;

    public VehicleRunningMetrics(TelemetryIdx telemetryIdx, Float distanceTravelled, Double calculatedSoc, Double discharge, Float soc, Float avgSpeed, Float maxSpeed, DriveMode driveMode, Double calDischarge) {
        this.telemetryIdx = telemetryIdx;
        this.distanceTravelled = distanceTravelled;
        this.calculatedSoc = calculatedSoc;
        this.discharge = discharge;
        this.soc = soc;
        this.avgSpeed = avgSpeed;
        this.maxSpeed = maxSpeed;
        this.driveMode = driveMode;
        this.calDischarge = calDischarge;
    }
    @Override
    public String toString() {
        return "VehicleRunningMetrics{" +
                " telemetryIdxTimestamp=" + telemetryIdx.getTimestamp() +
                ", telemetryIdxIMEI=" + telemetryIdx.getImei() +
                ", distanceTravelled=" + distanceTravelled +
                ", calculatedSoc=" + calculatedSoc +
                ", discharge=" + discharge +
                ", soc=" + soc +
                ", avgSpeed=" + avgSpeed +
                ", maxSpeed=" + maxSpeed +
                ", driveMode=" + driveMode +
                ", calDischarge=" + calDischarge +
                '}';
    }
}
