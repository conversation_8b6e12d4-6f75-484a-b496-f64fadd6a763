package com.nichesolv.evahanam.vehicleTests.listener;

import com.nichesolv.evahanam.trip.dto.v2.V2TestRideInput;
import com.nichesolv.evahanam.trip.dto.TestRideV2;
import com.nichesolv.evahanam.vehicle.events.FetchTripDetailsEvent;
import com.nichesolv.evahanam.trip.service.TripSummaryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class TripDetailsListener {

    @Autowired
    TripSummaryService analyticsService;

    /**
     * This method listens for FetchTripDetailsEvent and processes the trip details asynchronously.
     * It fetches trip summary data from ev-servables service for the trip and saves the generated trip statistics.
     *
     * @param event the FetchTripDetailsEvent containing the input data for fetching trip details
     */
    @EventListener
    public void handleFetchTripDetails(FetchTripDetailsEvent event) {
        V2TestRideInput input = event.getInput();
        try {
            log.info("Fetching analytics for trip: {}", input.getTripId());
            TestRideV2 response = analyticsService.getV2AnalyticsData(input);
            log.debug(" TestRideV2 : {}", response);
            analyticsService.saveGeneratedTripStats(response);
        } catch (Exception e) {
            log.error("Failed to fetch analytics for trip {}: {}", input.getTripId(), e.getMessage());
        }
    }
}
