package com.nichesolv.evahanam.vehicleTests.jpa;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleTests.enums.TestResult;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.enums.TestVariableResult;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(indexes = {
        @Index(name = "connectivity_test_summary_vehicle_id_idx", columnList = "vehicle_test_id"),
        @Index(name = "connectivity_test_summary_imei_idx", columnList = "imei"),
        @Index(name = "connectivity_test_summary_iter_idx", columnList = "iteration"),
        @Index(name = "connectivity_test_summary_veh_id_idx", columnList = "vehicle_id")
})
public class ConnectivityTestSummary {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;

    @OneToOne
    @JoinColumn(foreignKey = @ForeignKey(name = "fk_vehicle_test_id"))
    VehicleTest vehicleTest;

    @Enumerated(EnumType.STRING)
    TestTypeName testType;

    @ManyToOne
    @JoinColumn(name = "vehicle_id", foreignKey = @ForeignKey(name = "fk_vehicle_id"))
    Vehicle vehicle;

    String imei;

    @CreationTimestamp
    LocalDateTime createdOn;

    @UpdateTimestamp
    LocalDateTime modifiedOn;

    Integer iteration;

    Double locAvailabilityPerc;
    Double battAvailabilityPerc;
    Double tcuAvailabilityPerc;
    Double motorAvailabilityPerc;
    Double ioAvailabilityPerc;
    Double accelAvailabilityPerc;

    @Enumerated(EnumType.STRING)
    TestVariableResult locTestResult;

    @Enumerated(EnumType.STRING)
    TestVariableResult battTestResult;

    @Enumerated(EnumType.STRING)
    TestVariableResult tcuTestResult;

    @Enumerated(EnumType.STRING)
    TestVariableResult motorTestResult;

    @Enumerated(EnumType.STRING)
    TestVariableResult ioTestResult;

    @Enumerated(EnumType.STRING)
    TestVariableResult accelTestResult;

    public ConnectivityTestSummary(VehicleTest vehicleTest, TestTypeName testType, String imei, TestResult finalResult, Integer iteration, Double locAvailabilityPerc, Double battAvailabilityPerc, Double tcuAvailabilityPerc, Double motorAvailabilityPerc, Double ioAvailabilityPerc, Double accelAvailabilityPerc, TestVariableResult locTestResult, TestVariableResult battTestResult, TestVariableResult tcuTestResult, TestVariableResult motorTestResult, TestVariableResult ioTestResult, TestVariableResult accelTestResult) {
        this.vehicleTest = vehicleTest;
        this.testType = testType;
        this.imei = imei;
        this.iteration = iteration;
        this.locAvailabilityPerc = locAvailabilityPerc;
        this.battAvailabilityPerc = battAvailabilityPerc;
        this.tcuAvailabilityPerc = tcuAvailabilityPerc;
        this.motorAvailabilityPerc = motorAvailabilityPerc;
        this.ioAvailabilityPerc = ioAvailabilityPerc;
        this.accelAvailabilityPerc = accelAvailabilityPerc;
        this.locTestResult = locTestResult;
        this.battTestResult = battTestResult;
        this.tcuTestResult = tcuTestResult;
        this.motorTestResult = motorTestResult;
        this.ioTestResult = ioTestResult;
        this.accelTestResult = accelTestResult;
    }
}
