package com.nichesolv.evahanam.vehicleTests.jpa;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(indexes = {
        @Index(name = "test_details_type_idx", columnList = "testType"),
        @Index(name = "test_details_vid_idx", columnList = "imei"),
        @Index(name = "test_details_test_idx", columnList = "vehicle_test_id"),
        @Index(name = "test_details_query_idx", columnList = "testType,status,imei,name"),
        @Index(name = "test_details_veh_id_idx", columnList = "vehicle_id")
})
@Data
public class VehicleTestDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;

    @Enumerated(EnumType.STRING)
    TestTypeName testType;

    @Enumerated(EnumType.STRING)
    TestStatus status;


    @Enumerated(EnumType.STRING)
    DriveMode driveMode;

    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(name = "fk_vehicle_test_id"))
    VehicleTest vehicleTest;

    String imei;

    @CreationTimestamp
    Instant createdOn;

    @UpdateTimestamp
    Instant modifiedOn;

    String name;

    Double value;

    @ManyToOne
    @JoinColumn(name = "vehicle_id", foreignKey = @ForeignKey(name = "fk_vehicle_id"))
    Vehicle vehicle;
}
