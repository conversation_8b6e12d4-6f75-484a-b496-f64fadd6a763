package com.nichesolv.evahanam.vehicleTests.jpa;

import com.nichesolv.evahanam.vehicleTests.enums.TestResult;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.enums.TestVariableResult;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(indexes = {
        @Index(name = "dyno_test_summary_vehicle_id_idx",columnList = "vehicle_test_id"),
        @Index(name = "dyno_test_summary_imei_idx",columnList = "imei"),
        @Index(name = "dyno_test_summary_iter_idx",columnList = "iteration"),
})
public class DynoTestSummary {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;

    @OneToOne
    @JoinColumn(foreignKey = @ForeignKey(name = "fk_vehicle_test_id"))
    VehicleTest vehicleTest;

    @Enumerated(EnumType.STRING)
    TestTypeName testType;

    String imei;

    @CreationTimestamp
    LocalDateTime createdOn;

    @UpdateTimestamp
    LocalDateTime modifiedOn;

    @Enumerated(EnumType.STRING)
    TestResult finalResult;

    Integer iteration;
    Double ecoSpeedMin;
    Double ecoSpeedMax;
    Double ecoSpeedAvg;
    Double ecoSpeedMedian;

    @Enumerated(EnumType.STRING)
    TestVariableResult ecoModeSpeedTestResult;

    Double citySpeedMin;
    Double citySpeedMax;
    Double citySpeedAvg;
    Double citySpeedMedian;

    @Enumerated(EnumType.STRING)
    TestVariableResult cityModeSpeedTestResult;

    Double powerSpeedMin;
    Double powerSpeedMax;
    Double powerSpeedAvg;
    Double powerSpeedMedian;

    @Enumerated(EnumType.STRING)
    TestVariableResult powerModeSpeedTestResult;

    Double reverseSpeedMin;
    Double reverseSpeedMax;
    Double reverseSpeedAvg;
    Double reverseSpeedMedian;

    @Enumerated(EnumType.STRING)
    TestVariableResult reverseModeSpeedTestResult;

}
