package com.nichesolv.evahanam.vehicleTests.jpa;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleModel.jpa.DriveMode;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.enums.TestVariableResult;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(indexes = {
        @Index(name = "dyno_test_summary_drive_mode_details_vehicle_id_idx",columnList = "vehicle_test_id"),
        @Index(name = "dyno_test_summary_drive_mode_details_imei_idx",columnList = "imei"),
        @Index(name = "dyno_test_summary_drive_mode_details_iter_idx",columnList = "iteration"),
        @Index(name = "dyno_test_summary_drive_mode_details_veh_id_idx", columnList = "vehicle_id"),
})
public class DynoTestSummaryDriveModeDetails {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;

    @ManyToOne
    VehicleTest vehicleTest;

    @ManyToOne
    @JoinColumn(name = "vehicle_id", foreignKey = @ForeignKey(name = "fk_vehicle_id"))
    Vehicle vehicle;

    @Enumerated(EnumType.STRING)
    TestTypeName testType;

    String imei;

    @CreationTimestamp
    LocalDateTime createdOn;

    @UpdateTimestamp
    LocalDateTime modifiedOn;

    Integer iteration;
    Double speedMin;
    Double speedMax;
    Double speedAvg;
    Double speedMedian;

    @Enumerated(EnumType.STRING)
    TestVariableResult speedTestResult;

    @ManyToOne
    DriveMode driveMode;
}