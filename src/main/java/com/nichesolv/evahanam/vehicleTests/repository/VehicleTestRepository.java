package com.nichesolv.evahanam.vehicleTests.repository;

import com.nichesolv.evahanam.vehicle.dto.metadata.VehicleTestMetaData;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleTests.jpa.TestType;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface VehicleTestRepository extends JpaRepository<VehicleTest, Long> {

    Boolean existsByVehicleAndTestTypeAndStatus(Vehicle vehicle, TestType testType, TestStatus status);

    Optional<VehicleTest> findFirstByVehicleAndTestTypeAndStatusOrderByEndTimeDesc(Vehicle vehicle, TestType testType, TestStatus status);


    @Query(value = " select vt.* from vehicle_test vt , trip t where vt.id = t.test_id and " +
            " vt.test_type = ?2 and vt.status = ?3 and t.summary_population_status = ?4 " +
            " and vt.vehicle_id = ?1 and vt.end_time is not null order by vt.end_time desc , vt.id desc limit 1 " , nativeQuery = true)
    Optional<VehicleTest> findLatestVehicleByTestTypeAndStatusOrderByEndTimeDesc(Long id  , String testType , String status , String tripSummaryStatus);

    List<VehicleTest> findByVehicle(Vehicle vehicle);

    @Query(value = "select coalesce(max(vt.iteration),0) from VehicleTest vt where vt.vehicle=?1 and vt.testType=?2"
            , nativeQuery = false)
    int getMaxIterations(Vehicle vehicle, TestType testType);

    List<VehicleTest> findByTestTypeOrderByEndTimeDesc(TestType testType, Pageable pageable);

    List<VehicleTest> findByVehicleAndTestTypeAndStatusOrderByEndTimeDesc(Vehicle vehicle, TestType testType,TestStatus status, Pageable pageable);


    Long countByVehicleAndTestTypeAndStatusOrderByEndTimeDesc(Vehicle vehicle,TestType testType,TestStatus status);



    List<VehicleTest> findByStatusAndStartTimeIsLessThan(TestStatus testStatus, Instant instant);

    List<VehicleTest> findByStatusAndTestTypeAndStartTimeIsLessThan(TestStatus testStatus,TestType testType,Instant instant);

    List<VehicleTest> findByStatusAndTestTypeAndCreatedOnIsLessThan(TestStatus testStatus,TestType testType,Instant instant);

    @Query(value = "select distinct(battery_manufacturer) from vehicle_test_ride_form", nativeQuery = true)
    List<String> getBatteryManufacturer();

    @Query(value = "select count(vt.*) as testCount, vt.test_type as testType " +
            "from vehicle_test vt " +
            "inner join vehicle v on vt.vehicle_id=v.id " +
            "where (v.dealership_id= ?1 or v.mfr_org_id= ?1) and vt.status=?2 " +
            "group by vt.test_type", nativeQuery = true)
    List<VehicleTestMetaData> getvehicleTestMetaData(Long id,String status);

    @Query(value = "select count(vt.*) as testCount, vt.test_type as testType " +
            "from vehicle_test vt " +
            "inner join vehicle v on vt.vehicle_id=v.id " +
            "where (v.dealership_id= ?1 or v.mfr_org_id= ?1) and vt.status=?2 " +
            "and vt.start_time between ?3 and ?4 "+
            "group by vt.test_type", nativeQuery = true)
    List<VehicleTestMetaData> getVehicleTestMetaDataBetweenTimestamp(Long id,String status,Instant start, Instant end);

    @Query(value = "select count(vt.*) as testCount " +
            "from vehicle_test vt " +
            "inner join vehicle v on vt.vehicle_id=v.id " +
            "inner join trip t on t.test_id=vt.id " +
            "where (v.dealership_id= ?1 or v.mfr_org_id= ?1) and vt.status=?2 " +
            "and vt.test_type=?4 " +
            "and t.summary_population_status=?3",nativeQuery = true)
    Long getTotalVehicleTestCount(Long id,String testStatus,String summaryPopulationStatus,String testType);

    @Query(value = "select count(vt.*) as testCount " +
            "from vehicle_test vt " +
            "inner join vehicle v on vt.vehicle_id=v.id " +
            "inner join trip t on t.test_id=vt.id " +
            "where (v.dealership_id= ?1 or v.mfr_org_id= ?1) and vt.status=?2 " +
            "and vt.test_type=?4 " +
            "and vt.start_time between ?5 and ?6 "+
            "and t.summary_population_status=?3",nativeQuery = true)
    Long getTotalVehicleTestCountBetweenTimestamp(Long id,String testStatus,String summaryPopulationStatus,String testType,Instant start,Instant end);

    List<VehicleTest> findByVehicleAndTestTypeAndStatus(Vehicle vehicle,TestType testType,TestStatus testStatus);

    @Query(value = "select * from vehicle_test where status = ?1 and test_type = ?2 and end_time < ?3 ", nativeQuery = true)
    List<VehicleTest> findByStatusAndTestTypPastEndTime(String testStatus, String testType, Instant now);

    List<VehicleTest> findByCreatedOnBeforeAndStatus(Instant timestamp, TestStatus testStatus);
}

