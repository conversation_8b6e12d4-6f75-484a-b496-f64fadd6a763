package com.nichesolv.evahanam.vehicleTests.service;

import com.nichesolv.evahanam.common.jpa.Comment;
import com.nichesolv.evahanam.common.repository.CommentRepository;
import com.nichesolv.evahanam.common.util.DateValidationUtil;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.exceptions.DateValidationException;
import com.nichesolv.evahanam.telemetryData.repository.LocationDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.MotorDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.TelemetryBatteryRepository;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.vehicle.exception.VehicleException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleTests.dto.*;
import com.nichesolv.evahanam.vehicleTests.enums.RideType;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.enums.TimePosition;
import com.nichesolv.evahanam.vehicleTests.exception.VehicleTestException;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTestRideForm;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTestRideFormVehicleParameters;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTestRideFormVehicleParametersIdx;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleRideFormRepository;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRideFormVehicleParametersRepository;
import com.nichesolv.evahanam.trip.dto.TripDto;
import com.nichesolv.evahanam.trip.dto.mapper.TripDtoMapper;
import com.nichesolv.evahanam.trip.service.TripSummaryService;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.nds.repository.CustomUserRepository;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class VehicleRideFormService implements IVehicleRideFormService {
    @Autowired
    private TelemetryBatteryRepository telemetryBatteryRepository;
    @Autowired
    private LocationDataRepository locationDataRepository;
    @Autowired
    private VehicleDataRepository vehicleDataRepository;
    @Autowired
    private MotorDataRepository motorDataRepository;
    @Autowired
    private VehicleTestRepository vehicleTestRepository;

    @Autowired
    VehicleRideFormRepository vehicleRideFormRepository;
    @Autowired
    CommentRepository commentRepository;
    @Autowired
    VehicleRepository vehicleRepository;
    @Autowired
    IVehicleEolService vehicleEolService;
    @Autowired
    CustomUserRepository userRepository;
    @Autowired
    TripSummaryService tripSummaryService;
    @Autowired
    CustomOrganisationRepository organisationRepository;

    @Autowired
    DateValidationUtil dateValidationUtil;
    final String startTime="startTime";
    final String stopTime="stopTime";

    Double minTelemetryDataPercentage = 20.0;
    Double minBatteryDataPercentage = 20.0;
    Double minLocationDataPercentage = 20.0;

    Double minMotorDataPercentage=20.0;
    Short minTelemetryDataCount = 20;
    Short minBatteryDataCount = 20;
    Short minLocationDataCount = 20;

    short minMotorDataCount=20;

    @Autowired
    VehicleTestRideFormVehicleParametersRepository vehicleTestRideFormVehicleParametersRepository;

    @Autowired
    TripDtoMapper tripDtoMapper;

    @Autowired
    EvMessageBundle evMessageBundle;


    @Transactional
    public TestRideFeedbackFormResponseDto saveRideForm(TestRideFeedbackFormDto request, CustomUser user,Optional<Boolean> userConfirm) {
        TestRideFeedbackFormResponseDto result = null;
        try {
            log.info("inside the saveRideForm");
            log.info("system time now : "+Instant.now());
            log.info("startTime,endTime,imei coming from request :"+request.getStartTime()+" "+request.getStopTime()+" "+request.getImei());
            Instant gmtStartTime=Instant.ofEpochMilli(request.getStartTime().get());
            Instant gmtStopTime=Instant.ofEpochMilli(request.getStopTime().get());
            log.info("gmt start and endTime after converting to instance "+gmtStartTime +" "+ gmtStopTime);
            Long utcEpochMilliSecondsStart=gmtStartTime.atOffset(ZoneOffset.UTC).toEpochSecond();
            Long utcEpochMilliSecondsEnd=gmtStopTime.atOffset(ZoneOffset.UTC).toEpochSecond();
            log.info("utcEpochMilliSecondsStart and utcEpochMilliSecondsEnd "+utcEpochMilliSecondsStart+" "+utcEpochMilliSecondsEnd);
            Instant utcStartTime=Instant.ofEpochSecond(utcEpochMilliSecondsStart);
            Instant utcEndTime=Instant.ofEpochSecond(utcEpochMilliSecondsEnd);
            log.info("utcStartTime and utcEndTime "+utcStartTime+" "+utcEndTime);
//        request.setStartTime(Optional.of(utcEpochMilliSecondsStart));
//        request.setStopTime(Optional.of(utcEpochMilliSecondsEnd));


            result = new TestRideFeedbackFormResponseDto();
            if (request.getType() != TestTypeName.TEST_RIDE) {
                throw new VehicleTestException(evMessageBundle.getMessage("INCORRECT_TEST_TYPE"));
            }
            VehicleTestRideForm form = rideFormDtoToEntity(request);
            if(userConfirm.isEmpty() || userConfirm.get().equals(false)){
                //check for sufficient telemetry and motor data in particular time interval
                DataPercentageResultDto dataPercentage = getDataPercentageBetween(request.getStartTime().get(), request.getStopTime().get(), request.getImei());
                log.info("DataPercentageResultDto {}",dataPercentage);
                if (!dataPercentage.getResult()) {
                    result.setMotorData(dataPercentage.getMotorDataPercentage());
                    result.setTcuData(dataPercentage.getLocationDataPercentage());
                    result.setBmsData(dataPercentage.getBatteryDataPercentage());
                    result.setTestId(null);
                    return result;
                }
            }
            StartTestResponse test = vehicleEolService.startTest(request,user);
            vehicleEolService.stopTest_Formatted(new StopTestRequest(test.getImei(), test.getTestId(), Optional.of(request.getStopTime().get()), null));

            log.info("Saved Vehicle Test table");
            VehicleTest vehicleTest = vehicleTestRepository.findById(test.getTestId()).get();

            //saving the comment
            if (Optional.ofNullable(request.getComment()).isPresent() && request.getComment().length() > 0) {
                Comment comment = new Comment(request.getComment());
                comment.setVehicleTest(vehicleTest);
                commentRepository.save(comment);
                log.info("Saved comment");
                form.setComment(comment);
            }

            form.setVehicleTest(vehicleTest);
            form.setUser(user);

            VehicleTestRideForm savedForm= vehicleRideFormRepository.save(form);
            log.info("Saved ride form with comment");

            saveTestRideFormVehicleParameters(request,savedForm);
            log.info("test ride form vehicle parameters saved");

            TripDto tripDto = getTripDto(form);
            tripSummaryService.saveTrip(tripDto);
            log.info("Requested ride summary generation");
            result.setTestId(test.getTestId());
        } catch (VehicleTestException |VehicleException e) {
            log.error("VehicleTestException or VehicleException occurred: ", e);
            throw new VehicleTestException(e.getMessage());
        }
        return result;
    }

    private DataPercentageResultDto getDataPercentageBetween(Long startTime, Long stopTime, String imei) {
        log.debug("inside getDataPercentageBetween function");
        DecimalFormat decimalFormatter =new DecimalFormat("#.##");
        DataPercentageResultDto dataPercentages = new DataPercentageResultDto(0.0,0.0,0.0,0.0,false);
        Instant from = Instant.ofEpochMilli(startTime);
        Instant to = Instant.ofEpochMilli(stopTime);
        Long timeDiff = Duration.between(from, to).toSeconds();
        Long telemetryDataCount = vehicleDataRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(imei, from, to);
        Long locationDataCount = locationDataRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(imei, from, to);
        Long batteryDataCount = telemetryBatteryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(imei, from, to);
        Long motorDataCount= motorDataRepository.countByMotorIdxImeiAndMotorIdxTimestampBetweenAndMotorSpeedIsNotNull(imei,from,to);
        try {
            dataPercentages.setTelemetryDataPercentage(Double.valueOf(decimalFormatter.format(telemetryDataCount > 0 ? (double) telemetryDataCount / timeDiff * 100 : 0.0)));
            dataPercentages.setLocationDataPercentage(Double.valueOf(decimalFormatter.format(locationDataCount > 0 ? (double) locationDataCount / timeDiff * 100 : 0.0)));
            dataPercentages.setBatteryDataPercentage(Double.valueOf(decimalFormatter.format(batteryDataCount > 0 ? (double) batteryDataCount / timeDiff * 100 : 0.0)));
            dataPercentages.setMotorDataPercentage(Double.valueOf(decimalFormatter.format(motorDataCount > 0 ? (double) motorDataCount / timeDiff * 100 : 0.0)));
        }catch (Exception e){
            log.error("error message {}",e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        if (telemetryDataCount >= minTelemetryDataCount && locationDataCount >= minLocationDataCount
                && batteryDataCount >= minBatteryDataCount && motorDataCount >= minMotorDataCount
                && dataPercentages.getTelemetryDataPercentage() >= minTelemetryDataPercentage &&
                dataPercentages.getLocationDataPercentage() >= minLocationDataPercentage &&
                dataPercentages.getBatteryDataPercentage() >= minBatteryDataPercentage &&
                dataPercentages.getMotorDataPercentage() >= minMotorDataPercentage) {
            dataPercentages.setResult(true);
        }
        log.info("time diff {}",timeDiff);
        log.info("telemetryDataCount {}",telemetryDataCount);
        log.info("locationDataCount {}",locationDataCount);
        log.info("batteryDataCount {}",batteryDataCount);
        log.info("motorDataCount {}",motorDataCount);
        return dataPercentages;
    }

    private TripDto getTripDto(VehicleTestRideForm form) {

        TripDto tripDto = new TripDto();
        tripDto.setVehicleId(form.getVehicle().getId());
        tripDto.setImei(form.getImei());
        tripDto.setEndTime(form.getEndTime());
        tripDto.setUserId(form.getUser().getId());
        tripDto.setStartTime(form.getStartTime());
        tripDto.setTestId(form.getVehicleTest().getId());
        return tripDto;
    }

    private VehicleTestRideForm rideFormDtoToEntity(TestRideFeedbackFormDto request) {
        VehicleTestRideForm form = new VehicleTestRideForm();
        form.setImei(request.getImei());

        if (request.getStartTime().isPresent())
            dateValidationUtil.validateTime(request.getStartTime().get(),startTime);
        if(request.getStopTime().isPresent())
            dateValidationUtil.validateTime(request.getStopTime().get(),stopTime);
        if(request.getStartTime().get()>request.getStopTime().get())
            throw new DateValidationException("startTime cannot be greater than stopTime");

        form.setStartTime(Instant.ofEpochMilli(request.getStartTime().get()));
        form.setEndTime(Instant.ofEpochMilli(request.getStopTime().get()));

        form.setPillionRiderName(request.getPillionRiderName());
        form.setPillionRiderWeight(request.getPillionRiderWeight());
        form.setRiderName(request.getRiderName());
        form.setRiderWeight(request.getRiderWeight());
        form.setTotalWeight(request.getTotalWeight());

        form.setRideEndPlaceName(request.getRideEndPlaceName());
        form.setRideStartPlaceName(request.getRideStartPlaceName());

        form.setRideEndVoltage(request.getRideEndVoltage());
        form.setRideStartVoltage(request.getRideStartVoltage());

        form.setRoadCondition(Arrays.asList(request.getRoadCondition()));
        form.setFlyoverCount(request.getFlyoverCount());

        form.setDistance(request.getDistance());
        form.setTraffic(Arrays.asList(request.getTraffic()));
        form.setClimateCondition(Arrays.asList(request.getClimateCondition()));
        Vehicle vehicle = vehicleRepository.findByImei(request.getImei()).orElseThrow(() -> new VehicleException("Vehicle not found"));
        form.setVehicle(vehicle);
        form.setType(RideType.TEST_RIDE);
        form.setBatteryManufacturer(request.getBatteryManufacturer());
        form.setBatteryModel(request.getBatteryModel());
        form.setBatteryCapacity(request.getBatteryCapacity());
        form.setGoal(request.getGoal());
        form.setIssue(Arrays.asList(request.getIssues()));
        form.setObservationParameter(Arrays.asList(request.getObservationParameters()));
        return form;
    }

    public List<TestRideFeedbackFormDto> fetchRideForms(Long id, String identifier, Pageable pageable) {

        String imei = vehicleRepository.getVehicleIdentifiers(identifier).get().getVehImei();
        if (Optional.ofNullable(id).isPresent()) {
            Optional<VehicleTestRideForm> rideForm = vehicleRideFormRepository.findByVehicleTestId(id);
            return rideFormEntityToDto(rideForm.stream());
        } else if (Optional.ofNullable(imei).isPresent()) {
            List<VehicleTestRideForm> rideForms = vehicleRideFormRepository.findByImei(imei, pageable);
            return rideFormEntityToDto(rideForms.stream());
        } else
            throw new VehicleTestException(evMessageBundle.getMessage("PROVIDE_PARAMETERS"));
    }

    public List<TestRideFeedbackFormDto> rideFormEntityToDto(Stream<VehicleTestRideForm> stream) {
        return stream.map(e -> new TestRideFeedbackFormDto(e.getImei(), Optional.of(e.getStartTime().toEpochMilli()),
                Optional.of(e.getEndTime().toEpochMilli()),
                TestTypeName.TEST_RIDE,
                e.getRiderName(),
                Optional.ofNullable(e.getRiderWeight()).orElse(0f),
                e.getPillionRiderName(),
                Optional.ofNullable(e.getPillionRiderWeight()).orElse(0f),
                Optional.ofNullable(e.getTotalWeight()).orElse(0f),
                e.getRideStartPlaceName(),
                e.getRideEndPlaceName(),
                e.getRoadCondition().toArray(new String[0]),
                e.getTraffic().toArray(new String[0]),
                e.getClimateCondition().toArray(new String[0]),
                e.getFlyoverCount(),
                e.getDistance(),
                e.getBatteryManufacturer(),
                e.getRideStartVoltage(),
                e.getRideEndVoltage(),
                Optional.ofNullable(e.getComment()).isPresent() ?
                        commentRepository.findById(e.getComment().getId()).get().getComment() : null,
                e.getVehicleTest().getId(),
                e.getGoal(),
                e.getObservationParameter().toArray(new String[0]),
                e.getIssue().toArray(new String[0]),
                tripDtoMapper.getTestRideFormVehicleParameters(e,TimePosition.BEFORE),
                tripDtoMapper.getTestRideFormVehicleParameters(e,TimePosition.AFTER),
                e.getBatteryModel(),
                e.getBatteryCapacity()
        )).collect(Collectors.toList());
    }

    @Override
    public List<String> getBatteryManufacturerList(String name) {
        return vehicleTestRepository.getBatteryManufacturer();
    }

    @Override
    @Transactional
    public void saveTestRideFormVehicleParameters(TestRideFeedbackFormDto request,VehicleTestRideForm form) {
        List<VehicleTestRideFormVehicleParameters> vehicleParameters=new ArrayList<>();
        request.getVehicleDetailsBeforeRide().keySet().forEach(e->{
            VehicleTestRideFormVehicleParametersIdx idx=new VehicleTestRideFormVehicleParametersIdx(form,e, TimePosition.BEFORE);
            VehicleTestRideFormVehicleParameters parameters=new VehicleTestRideFormVehicleParameters();
            parameters.setVehicleTestRideFormIdx(idx);
            parameters.setTransitionTime(Instant.ofEpochMilli(request.getStartTime().get()));
            parameters.setFieldValue(request.getVehicleDetailsBeforeRide().get(e).toString());
            vehicleParameters.add(parameters);
        });
        request.getVehicleDetailsAfterRide().keySet().forEach(e->{
            VehicleTestRideFormVehicleParametersIdx idx=new VehicleTestRideFormVehicleParametersIdx(form,e, TimePosition.AFTER);
            VehicleTestRideFormVehicleParameters parameters=new VehicleTestRideFormVehicleParameters();
            parameters.setVehicleTestRideFormIdx(idx);
            parameters.setTransitionTime(Instant.ofEpochMilli(request.getStopTime().get()));
            parameters.setFieldValue(request.getVehicleDetailsAfterRide().get(e).toString());
            vehicleParameters.add(parameters);
        });
        vehicleTestRideFormVehicleParametersRepository.saveAll(vehicleParameters);
    }
}
