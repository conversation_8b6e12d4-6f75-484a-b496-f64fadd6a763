package com.nichesolv.evahanam.vehicleTests.service;

import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.trip.dto.TripDto;
import com.nichesolv.evahanam.trip.dto.v2.V2TestRideInput;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.repository.TripRepository;
import com.nichesolv.evahanam.vehicle.events.FetchTripDetailsEvent;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.exception.TripSummaryException;
import com.nichesolv.evahanam.vehicleTests.exception.VehicleTestException;
import com.nichesolv.evahanam.vehicleTests.exception.VehicleTestRunningException;
import com.nichesolv.evahanam.vehicleTests.jpa.TestType;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicleTests.repository.TestTypeRepository;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import com.nichesolv.nds.model.user.CustomUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
public class TestRideServiceImpl implements TestRideService {
    @Autowired
    VehicleTestRepository vehicleTestRepository;

    @Autowired
    private TripRepository tripRepository;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    TestTypeRepository testTypeRepository;

    @Autowired
    ApplicationEventPublisher eventPublisher;


    @Override
    @Transactional
    public Long saveAutomaticTripToTestRide(Long tripId , CustomUser user) {
        Trip trip = tripRepository.findById(tripId)
                .orElseThrow(() -> new TripSummaryException(evMessageBundle.getMessage("TRIP_NOT_FOUND", tripId)));
        log.debug(" trip id {} ", trip.getId());
        try {
            Optional<TestType> testType = Optional.ofNullable(testTypeRepository.findByTestTypeName(TestTypeName.TEST_RIDE).orElseThrow(() -> new VehicleTestException(evMessageBundle.getMessage("INCORRECT_TEST_TYPE"))));
            List<VehicleTest> runningTests = vehicleTestRepository.findByVehicleAndTestTypeAndStatus(trip.getVehicle(), testType.get(), TestStatus.RUNNING);
            if (vehicleTestRepository.existsByVehicleAndTestTypeAndStatus(trip.getVehicle(), testType.get(), TestStatus.RUNNING)) {
                throw new VehicleTestRunningException(evMessageBundle.getMessage("TEST_RUNNING", trip.getVehicle().getImei()), runningTests.get(0).getId(), runningTests.get(0).getStatus(), runningTests.get(0).getStartTime());
            }
            Integer iteration = vehicleTestRepository.getMaxIterations(trip.getVehicle(), testTypeRepository.findByTestTypeName(testType.get().getTestTypeName()).get());

            CustomUser tripUser = trip.getUser();
            Long tripUserId = tripUser != null ? tripUser.getId() : null;

            VehicleTest vehicleTest = new VehicleTest(trip.getVehicle(), testType.get()
                    , trip.getStartTime()
                    ,  user.getId(), iteration + 1);
            vehicleTest.setCreatedOn(Instant.now());
            vehicleTest.setMfrOrg(trip.getVehicle().getManufacturer());
            vehicleTest.setOwnerOrg(trip.getVehicle().getOwner());
            vehicleTest.setEndTime(trip.getEndTime());
            VehicleTest savedVehicleTest = vehicleTestRepository.save(vehicleTest);

            TripDto tripDto = new TripDto(
                    trip.getImei(),
                    trip.getStartTime(),
                    trip.getEndTime(),
                    tripUserId,
                    trip.getVehicle().getId(),
                    savedVehicleTest.getId()
            );

            trip.setVehicleTest(savedVehicleTest);
            trip.setTripType(TripType.TEST_RIDE);
            trip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.IN_PROGRESS);
            trip.setUpdatedOn(Instant.now());
            V2TestRideInput inputModel = new V2TestRideInput(tripDto.getImei(), trip.getId(), tripDto.getStartTime().toEpochMilli(), tripDto.getEndTime().toEpochMilli(), UUID.randomUUID());
            eventPublisher.publishEvent(new FetchTripDetailsEvent(this, inputModel));

            if (!trip.getVehicleTest().getStatus().equals(TestStatus.COMPLETED)) {
                if (trip.getVehicleTest().getStatus() == TestStatus.RUNNING) {
                    // Abort vehicle test explicitly if it's still running
                    trip.getVehicleTest().setStatus(TestStatus.ABORTED);
                    vehicleTestRepository.save(trip.getVehicleTest());
                }
                // retaining the COMPLETED AUTOMATIC TRIP
                trip.setTripType(TripType.AUTOMATIC);
                trip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.COMPLETED);
                tripRepository.save(trip);
            }

        } catch (Exception e) {
            log.info("An Exception occurred {}", e.getMessage());
        }
        return Optional.ofNullable(trip.getVehicleTest())
                .map(VehicleTest::getId)
                .orElseThrow(() -> new VehicleTestException(evMessageBundle.getMessage("TEST_NOT_FOUND")));
    }
}
