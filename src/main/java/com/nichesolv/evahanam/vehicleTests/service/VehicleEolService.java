package com.nichesolv.evahanam.vehicleTests.service;

import com.nichesolv.evahanam.cache.service.VehicleCacheRetrievalService;
import com.nichesolv.evahanam.common.jpa.Comment;
import com.nichesolv.evahanam.common.repository.CommentRepository;
import com.nichesolv.evahanam.common.service.DateUtils;
import com.nichesolv.evahanam.common.util.DateValidationUtil;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.telemetryData.dto.MeasuredStats;
import com.nichesolv.evahanam.telemetryData.dto.MotorStatusDriveModeMinMaxTimeDto;
import com.nichesolv.evahanam.telemetryData.dto.SpeedStats;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleMotorData;
import com.nichesolv.evahanam.telemetryData.repository.*;
import com.nichesolv.evahanam.vehicle.dto.VehicleIdInfo;
import com.nichesolv.evahanam.vehicle.dto.VehicleIdsProjection;
import com.nichesolv.evahanam.vehicle.enums.VehicleIdentifierTypes;
import com.nichesolv.evahanam.vehicle.exception.VehicleException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import com.nichesolv.evahanam.vehicleModel.dto.PartModelAttributeDto;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.jpa.DriveModeSpeed;
import com.nichesolv.evahanam.vehicleModel.repository.DriveModeRepository;
import com.nichesolv.evahanam.vehicleModel.repository.VehicleModelRepository;
import com.nichesolv.evahanam.vehicleTests.dto.*;
import com.nichesolv.evahanam.vehicleTests.enums.TestResult;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.enums.TestVariableResult;
import com.nichesolv.evahanam.vehicleTests.exception.VehicleTestException;
import com.nichesolv.evahanam.vehicleTests.exception.VehicleTestRunningException;
import com.nichesolv.evahanam.vehicleTests.jpa.*;
import com.nichesolv.evahanam.vehicleTests.repository.*;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class VehicleEolService implements IVehicleEolService {
    private final LocationDataRepository locationDataRepository;
    private final DynoTestSummaryRepository dynoTestSummaryRepository;
    private final ConnectivityTestSummaryRepository connectivityTestSummaryRepository;
    private final VehicleModelRepository vehicleModelRepository;
    private static final Float[] VOLTAGE_THRESHOLD = {64f, 84f};
    private static final Float CURRENT_THRESHOLD = 100f;

    private long vehicleTelemetryDataCount = 0;
    private long vehicleLocationDataCount = 0;

    private long vehicleBatteryDataCount = 0;

//    private final ConnectivityTestRepository connectivityTestRepository;

    VehicleTestRepository vehicleTestRepository;

    IVehicleService vehicleRepository;

    VehicleTestDetailsRepository vehicleTestDetailsRepository;

    @Autowired
    TelemetryBatteryRepository telemetryBatteryRepository;

    @Autowired
    VehicleDataRepository vehicleDataRepository;

    @Autowired
    ImuDataRepository vehicleImuRepository;

    @Autowired
    EvMessageBundle evMessageBundle;


    @Autowired
    TestTypeRepository testTypeRepository;

    @Autowired
    TestFactorRepository testFactorRepository;

    @Autowired
    DateValidationUtil dateValidationUtil;

    final String startTime = "startTime";

    @Autowired
    DriveModeRepository driveModeRepository;

    @Autowired
    VehicleCacheRetrievalService vehicleCacheRetrievalService;


    public VehicleEolService(VehicleTestDetailsRepository vehicleTestDetailsRepository
            , VehicleTestRepository vehicleTestRepository, IVehicleService vehicleRepository,

                             VehicleModelRepository vehicleModelRepository,
                             ConnectivityTestSummaryRepository connectivityTestSummaryRepository,
                             DynoTestSummaryRepository dynoTestSummaryRepository,
                             LocationDataRepository locationDataRepository) {
        this.vehicleTestRepository = vehicleTestRepository;
        this.vehicleRepository = vehicleRepository;
        this.vehicleTestDetailsRepository = vehicleTestDetailsRepository;

        this.vehicleModelRepository = vehicleModelRepository;
        this.connectivityTestSummaryRepository = connectivityTestSummaryRepository;
        this.dynoTestSummaryRepository = dynoTestSummaryRepository;
        this.locationDataRepository = locationDataRepository;
    }

    @Override
    @Transactional
    public StartTestResponse startTest(StartTestRequest request, CustomUser user) {

        Instant currentTime = Instant.now();
        if (request.getType().equals(TestTypeName.TEST_RIDE) && request.getStartTime().isPresent()) {
            currentTime = Instant.ofEpochMilli(request.getStartTime().get());
        }
        log.info("currentTime or startTime : " + currentTime);
        log.info("currentTime or startTime in epoch milli seconds : " + currentTime.toEpochMilli());
        Vehicle vehicle = vehicleRepository.getVehicleByAnyId(request.getImei());
        Optional<TestType> testType = Optional.ofNullable(testTypeRepository.findByTestTypeName(request.getType()).orElseThrow(() -> new VehicleTestException(evMessageBundle.getMessage("INCORRECT_TEST_TYPE"))));
        List<VehicleTest> runningTests = vehicleTestRepository.findByVehicleAndTestTypeAndStatus(vehicle, testType.get(), TestStatus.RUNNING);
        if (vehicleTestRepository.existsByVehicleAndTestTypeAndStatus(vehicle, testType.get(), TestStatus.RUNNING)) {
            throw new VehicleTestRunningException(evMessageBundle.getMessage("TEST_RUNNING", vehicle.getImei()), runningTests.get(0).getId(), runningTests.get(0).getStatus(), runningTests.get(0).getStartTime());
        }
        Integer iteration = vehicleTestRepository.getMaxIterations(vehicle, testTypeRepository.findByTestTypeName(testType.get().getTestTypeName()).get());
        log.info(iteration.toString());
        VehicleTest vehicleTest = new VehicleTest(vehicle, testType.get()
                , currentTime
                , user.getId(), iteration + 1);
        vehicleTest.setMfrOrg(vehicle.getManufacturer());
        vehicleTest.setOwnerOrg(vehicle.getOwner());
        vehicleTestRepository.save(vehicleTest);

        return new StartTestResponse(request.getImei()
                , Optional.of(currentTime.toEpochMilli())
                , Optional.empty()
                , vehicleTest.getId()
                , vehicleTest.getTestType().getTestTypeName(), null);

    }


    @Override
    @Transactional
    public TestResultsResponse stopTest_Formatted(StopTestRequest request) throws VehicleTestException, VehicleException {
        if (!vehicleRepository.vehicleExists(new VehicleIdInfo(request.getImei(), VehicleIdentifierTypes.IMEI))) {
            throw new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND"));
        }
        VehicleTest vehicleTest = vehicleTestRepository.findById(request.getTestId()).orElseThrow(() -> new VehicleTestException(evMessageBundle.getMessage("TEST_NOT_FOUND")));
        if (vehicleTest.getStatus() != TestStatus.RUNNING) {
            if (vehicleTest.getTestType().getTestTypeName().equals(TestTypeName.DYNO)) {
                return getTestResults_Formatter(vehicleTest.getId(), vehicleTest.getVehicle().toString());
            }
            throw new VehicleTestException(evMessageBundle.getMessage("TEST_COMPLETED"));
        }
        Instant endTime = Instant.now();
        endTime = switch (vehicleTest.getTestType().getTestTypeName()) {
            case CONNECTIVITY -> vehicleTest.getStartTime().plusSeconds(60);
            case TEST_RIDE ->
                    request.getStopTime().isPresent() ? Instant.ofEpochMilli(request.getStopTime().get()) : endTime;
            default -> endTime;
        };
        log.info("current Time or end time in instant " + endTime);
        log.info("current time or end time in epoch milli seconds " + endTime.toEpochMilli());

        if (vehicleTest.getTestType().equals(TestTypeName.CONNECTIVITY)) {
            Boolean isDataPersistenceCompleted = dataPersistenceCompleted(request.getImei(), endTime, vehicleTest.getStartTime());
            log.info("isDataPersistenceCompleted {} ", isDataPersistenceCompleted);
            if (request.getRetryCount() != null && request.getRetryCount() <= 2 && !isDataPersistenceCompleted) {
                return new TestResultsResponse(request.getImei(), vehicleTest.getId(), vehicleTest.getStartTime().toEpochMilli(), null, TestStatus.RUNNING, vehicleTest.getTestType().getTestTypeName(), null, null, null);
            }
        }

        vehicleTest.setEndTime(endTime);
        if (!vehicleTest.getTestType().getTestTypeName().equals(TestTypeName.TEST_RIDE)) {
            log.debug("test type : {}", vehicleTest.getTestType().getTestTypeName().name());
            vehicleTest.setStatus(TestStatus.COMPLETED);
        }
        log.debug("test status : {}", vehicleTest.getStatus().name());
        vehicleTestRepository.save(vehicleTest);

        List<VehicleTestDetails> testResults = currentResult(vehicleTest);
        log.info("test results saved into vehicle_test_details : " + testResults.toString());
        vehicleTestDetailsRepository.saveAll(testResults);
        if (vehicleTest.getTestType().getTestTypeName() == TestTypeName.CONNECTIVITY) {
            saveConnectivityTestSummary(vehicleTest);
        } else if (vehicleTest.getTestType().getTestTypeName() == TestTypeName.DYNO) {
            saveDynoTestSummary(vehicleTest);
        }
        return vehicleTestSummary_toDto(testResults.stream(), vehicleTest);
//        return testResults;
    }

    Boolean dataPersistenceCompleted(String imei, Instant endTime, Instant startTime) {
        boolean result = false;
        Instant currentTime = Instant.now();
        log.info("inside dataPersistenceCompleted function");
        log.info("currentTime : " + currentTime);
        log.info("imei : " + imei);
        log.info("endTime : " + endTime);
        log.info("time difference : " + Duration.between(endTime, startTime));
        Long diff = ChronoUnit.SECONDS.between(startTime, endTime);
        vehicleTelemetryDataCount = vehicleDataRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(imei, startTime, endTime);
        vehicleLocationDataCount = locationDataRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(imei, startTime, endTime);
        vehicleBatteryDataCount = telemetryBatteryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(imei, startTime, endTime);
        log.info("vehicleTelemetryData count {} ", vehicleTelemetryDataCount);
        log.info("vehicleLocationData count  {} ", vehicleLocationDataCount);
        log.info("vehicleBatteryData count {} ", vehicleBatteryDataCount);
        if (vehicleTelemetryDataCount == diff && vehicleLocationDataCount == diff && vehicleBatteryDataCount == diff) {
            result = true;
        }
        return result;
    }


    @Override
    public TestResultsResponse getTestResults_Formatter(Long testId, String identifier) {

        VehicleTest vehicleTest = vehicleTestRepository.findById(testId)
                .orElseThrow(() -> new VehicleTestException(evMessageBundle.getMessage("TEST_NOT_FOUND")));
        List<VehicleTestDetails> testResults = currentResult(vehicleTest);
        testResults.forEach(e -> {
            log.info("test result {} ", e);
        });
        return vehicleTestSummary_toDto(testResults.stream(), vehicleTest);
    }

    @Override
    public VehicleTestListApiDto getTestList(TestTypeName testTypeName, String identifier, Pageable pageable) {
        List<VehicleTest> vehicleTests = new ArrayList<>();
        Long totalTestCount;
        if (Optional.ofNullable(identifier).isPresent()
                && Optional.ofNullable(testTypeName).isPresent()) {
            Vehicle vehicle = vehicleRepository.getVehicleByAnyId(identifier);
            Optional<TestType> testType = Optional.ofNullable(testTypeRepository.findByTestTypeName(testTypeName).orElseThrow(() -> new VehicleTestException(evMessageBundle.getMessage("INCORRECT_TEST_TYPE"))));
            vehicleTests.addAll(vehicleTestRepository.findByVehicleAndTestTypeAndStatus(vehicle, testType.get(), TestStatus.RUNNING));
            vehicleTests.addAll(vehicleTestRepository.findByVehicleAndTestTypeAndStatusOrderByEndTimeDesc(vehicle, testType.get(), TestStatus.COMPLETED, pageable));
            totalTestCount = vehicleTestRepository.countByVehicleAndTestTypeAndStatusOrderByEndTimeDesc(vehicle, testType.get(), TestStatus.COMPLETED);

        } else {
            throw new VehicleTestException(evMessageBundle.getMessage("PROVIDE_PARAMETERS"));
        }
        return vehicleTestDtos(vehicleTests, totalTestCount);
    }

    @Override
    public Map<String, List<DriveModePassPercentage>> getDynoTestPassPercentages(Long orgId) {
        List<DynoTestPassPercentageDto> dynoTestPassPercentages = dynoTestSummaryRepository.getPassPercentagesByOrgId(orgId);
        return dynoTestPassPercentageDtoMapper(dynoTestPassPercentages);
    }

    @Override
    public ConnectivityDataPercentageApiResponseDto getConnectivityDataPercentage(Long orgId) {
        ConnectivityDataPercentageProjection overallConnectivityDataProjection = connectivityTestSummaryRepository.findAvgDataAvailabilityByOrgId(orgId);
        List<ConnectivityDataPercentageProjection> vehicleModelConnectivityDataProjection = connectivityTestSummaryRepository.findAvgDataAvailabilityByOrgIdGroupByVehicleModel(orgId);

        ConnectivityDataPercentageDto overallData = new ConnectivityDataPercentageDto();
        List<ConnectivityDataPercentageDto> modelWiseData = new ArrayList<>();

        BeanUtils.copyProperties(overallConnectivityDataProjection, overallData);
        vehicleModelConnectivityDataProjection.forEach(e -> {
            ConnectivityDataPercentageDto connectivityDataDto = new ConnectivityDataPercentageDto();
            BeanUtils.copyProperties(e, connectivityDataDto);
            modelWiseData.add(connectivityDataDto);
        });

        return new ConnectivityDataPercentageApiResponseDto(overallData, modelWiseData);
    }

    private Map<String, List<DriveModePassPercentage>> dynoTestPassPercentageDtoMapper(List<DynoTestPassPercentageDto> dynoTestPassPercentageDtos) {
        Map<String, List<DriveModePassPercentage>> result = new HashMap<>();
        dynoTestPassPercentageDtos.forEach(e -> {
            if (result.containsKey(e.getPartModelName())) {
                result.get(e.getPartModelName()).add(new DriveModePassPercentage(e.getName(), e.getPassPercentage() != null ? e.getPassPercentage() : 0.0f));
            } else {
                List<DriveModePassPercentage> driveModePassPercentages = new ArrayList<>();
                driveModePassPercentages.add(new DriveModePassPercentage(e.getName(), e.getPassPercentage() != null ? e.getPassPercentage() : 0.0f));
                result.put(e.getPartModelName(), driveModePassPercentages);
            }
        });
        return result;
    }

    private VehicleTestListApiDto vehicleTestDtos(List<VehicleTest> tests, Long totalTestCount) {
        List<VehicleTestDto> vehicleTestDtoList = tests
                .stream()
                .map(e -> new VehicleTestDto(e.getId(), e.getVehicle().getImei(),
                        DateUtils.localDateTimeToLong(e.getStartTime())
                        , e.getEndTime() != null ? DateUtils.localDateTimeToLong(e.getEndTime()) : null
                        , e.getTestType().getTestTypeName(), DateUtils.localDateTimeToLong(e.getCreatedOn()),
                        e.getStatus(), e.getIteration(), getComment(e)))
                .peek(e -> {
                    VehicleIdsProjection vehicleIdsProjection = vehicleRepository.populateVehicleIdentifiers(e.getImei()).get();
                    e.setVehChassisNo(vehicleIdsProjection.getVehChassisNo());
                    e.setVehRegNo(vehicleIdsProjection.getVehRegNo());
                    e.setVehId(vehicleIdsProjection.getVehId());
                })
                .collect(Collectors.toList());
        return new VehicleTestListApiDto(vehicleTestDtoList, totalTestCount);
    }


    @Autowired
    CommentRepository commentRepository;

    private TestResultsResponse vehicleTestSummary_toDto(Stream<VehicleTestDetails> stream, VehicleTest test) {
        log.info("inside the vehicleTestSummary_toDto");
        TestResultsResponse response = new TestResultsResponse();
        Map<String, Integer> availability = new HashMap<>();
        Map<String, Boolean> connection = new HashMap<>();
        Map<String, Boolean> threshold = new HashMap<>();
        Map<String, DynoTestResultDto> dynoTestResults = new HashMap<>();

        CustomOrganisation organisation = (CustomOrganisation) test.getVehicle().getManufacturer();
        Set<com.nichesolv.evahanam.vehicleModel.jpa.DriveMode> driveModes = driveModeRepository.findByOrganisation(organisation);
        log.info("drive modes of the organisation {}", driveModes.size());
        stream.forEach(c -> {
            log.info("name {}  , value {}", c.getName(), c.getValue());
            if (test.getTestType().getTestTypeName() == TestTypeName.DYNO) {
                DynoTestResultDto dynoTestResultDto = dynoTestResults.getOrDefault(c.getDriveMode().name(), new DynoTestResultDto());
                Map<String, Object> modeData = dynoTestResultDto.getModeData() != null ? dynoTestResultDto.getModeData() : new HashMap<>();
                for (com.nichesolv.evahanam.vehicleModel.jpa.DriveMode s : driveModes) {
                    if ((c.getDriveMode().name().equals(s.getName())) && (c.getName().equals("speed"))) {
                        modeData.put(c.getName(), c.getValue());
//                        log.info("mode {} ,name {} ,speed {} ",s,c.getName(),c.getValue());
                    }


                    if ((c.getName().equals(s.getName().toLowerCase() + "_test_passed"))) {
                        modeData.put("dyno_result", c.getValue() == 1);
//                        log.info("mode {} ,name {} ,speed {} ",s,c.getName(),c.getValue());
                    }

                    if (!modeData.isEmpty()) {
                        dynoTestResultDto.setModeData(modeData);
                        dynoTestResults.put(c.getDriveMode().name(), dynoTestResultDto);
                    }
                }

            } else {
                for (String s : partTypes) {
                    if ((c.getName().equals("has_" + s)) && !connection.containsKey(s)) {
                        log.debug("partType {}, value {}", s, c.getValue());
                        log.debug("--- {}", c.getValue() == 1);
                        connection.put(s, (c.getValue() == 1));
                    }
                }

                for (String s : conectivity_dataPoints) {
                    if ((c.getName().equals(s)) && !availability.containsKey(s)) {
                        availability.put(s, c.getValue().intValue());
                    }
                }


                for (String s : thresholdBreach) {
                    if ((c.getName().contains(s)) && !threshold.containsKey(s)) {
                        threshold.put(s, (c.getValue() == 1));
                    }
                }
            }

        });
        Integer soc = Optional.ofNullable(telemetryBatteryRepository.findByImeiAndEndTime(test.getVehicle().getImei(), test.getStartTime(), test.getEndTime())).orElse(0);
        Float dataLag = telemetryRepository.getAvgLagByImeiAndTimestampBetween(test.getVehicle().getImei(), test.getStartTime(), Optional.ofNullable(test.getEndTime()).orElse(Instant.now()));
        if (test.getTestType().getTestTypeName() == TestTypeName.CONNECTIVITY) {
            availability.put("soc", soc);
            availability.put("lag", Optional.ofNullable(dataLag).orElse(0f).intValue());
        }
        TestResultDto testResult = new TestResultDto(availability, threshold, connection, dynoTestResults);


        Optional<Instant> end = Optional.ofNullable(test.getEndTime());
        Long e = end.map(DateUtils::localDateTimeToLong).orElse(null);


        response.setStartTime(DateUtils.localDateTimeToLong(test.getStartTime()));
        response.setStatus(test.getStatus());
        response.setStopTime(e);
        response.setImei(test.getVehicle().getImei());
        response.setTestId(test.getId());
        String comment = getComment(test);
        response.setComment(comment);
        response.setIteration(test.getIteration());
        response.setVehicleImei(test.getVehicle().getImei());

        response.setType(test.getTestType().getTestTypeName());
        response.setData(Optional.of(testResult));
        return response;
    }

    private String getComment(VehicleTest test) {
        return commentRepository.findByVehicleTest(test).stream().map(Comment::getComment).findAny().orElse(null);
    }


    @Autowired
    VehicleDataRepository telemetryRepository;

    @Autowired
    MotorDataRepository motorRepository;

//    @Autowired
//    MotorDataRepository motorDataRepository;


    //lag
    private List<VehicleTestDetails> currentResult(VehicleTest vehicleTest) {

        List<VehicleTestDetails> saveResult = new ArrayList<>();

        if (vehicleTest.getTestType().getTestTypeName() == TestTypeName.CONNECTIVITY) {
            saveResult.addAll(getConnectivitySummary(vehicleTest));
        } else if (vehicleTest.getTestType().getTestTypeName() == TestTypeName.DYNO) {
            saveResult.addAll(addDynoTestDetails(vehicleTest));
        }

        return saveResult;
    }


    private Collection<? extends VehicleTestDetails> addDynoTestDetails(VehicleTest vehicleTest) {
        DecimalFormat decimalFormatter = new DecimalFormat("#.##");
        log.info("inside the addDynoTestDetails");
        log.info("vehicleTest : ", vehicleTest.toString());
        List<VehicleTestDetails> result = vehicleTestDetailsRepository.findByVehicleTest(vehicleTest);
        Instant start = vehicleTest.getStartTime();
        Instant end = Optional.ofNullable(vehicleTest.getEndTime()).orElse(Instant.now());
        List<MotorStatusDriveModeMinMaxTimeDto> driveModeMinMaxSpeed = motorRepository
                .findMinAndMaxTimestampByDriveModeBetweenTimestampAndImei(start, end, vehicleTest.getVehicle().getImei()).stream().filter(e -> e.getCnt() > 0).toList();
//        log.info("size {}",driveModeMinMaxSpeed.size());
//        driveModeMinMaxSpeed.stream().forEach(s->{
//            log.info("min {} ,max {}, driveMode {} count {}",s.getMin(),s.getMax(),s.getDriveMode().name(),s.getCnt());
//        });
        Vehicle vehicle = vehicleTest.getVehicle();
        VehicleTestDetails conectivityDataPoints = null;
        Instant createdOn = Instant.now();
//        end = vehicleTest.getStartTime();
//        start = end;
        List<VehicleTestDetails> saveResult = new ArrayList<>();
//        end = Optional.ofNullable(vehicleTest.getEndTime()).orElse(createdOn);

        Map<String, Double> speed = new HashMap<>();
        for (MotorStatusDriveModeMinMaxTimeDto s : driveModeMinMaxSpeed) {


            VehicleTestDetails baseTestResult = new VehicleTestDetails();
            baseTestResult.setImei(vehicle.getImei());
            baseTestResult.setStatus(vehicleTest.getStatus());
            baseTestResult.setVehicleTest(vehicleTest);
            baseTestResult.setVehicle(vehicle);
            baseTestResult.setCreatedOn(Instant.now());
            baseTestResult.setDriveMode(s.getDriveMode());
            baseTestResult.setTestType(vehicleTest.getTestType().getTestTypeName());
            baseTestResult.setCreatedOn(createdOn);
            baseTestResult.setVehicle(vehicle);
            conectivityDataPoints = null;
            conectivityDataPoints = result.stream()
                    .filter(e -> e.getName().equalsIgnoreCase(s.getDriveMode().name()))
                    .findAny().orElse(baseTestResult);
//            end = Optional.ofNullable(vehicleTest.getEndTime()).orElse(createdOn);

            conectivityDataPoints.setStatus(vehicleTest.getStatus());
            conectivityDataPoints.setName("speed");

            PartModelAttributeDto partModelAttributeDto = vehicleCacheRetrievalService.getAttribute("rearTyreDiameter", PartType.REAR_TYRE.name(), vehicle);

            Double val = Double.valueOf(motorRepository.getTopSpeedByImeiAndBetweenTimestamp(vehicleTest.getVehicle().getImei()
                    , s.getMin().plusSeconds(3)


                    , s.getMax(), s.getDriveMode().name(), Float.parseFloat(partModelAttributeDto.getValue())));
            val = Double.valueOf(decimalFormatter.format(val));

            speed.put(s.getDriveMode().name(), val);
            conectivityDataPoints.setValue(val);
            log.info("name {},start {} ,end {}  ,val {}", s.getDriveMode().name(), start.plusSeconds(3), end, val);
            saveResult.add(conectivityDataPoints);
        }

        Set<DriveModeSpeed> modeSpeeds = vehicleTest.getVehicle().getVehicleModel().getDriveModes();
        modeSpeeds.stream().forEach(e -> {
            log.info("supported mode {} {}", e.getDriveMode().getName(), e.getMax());
        });

        for (MotorStatusDriveModeMinMaxTimeDto s : driveModeMinMaxSpeed) {

            VehicleTestDetails baseTestResult = new VehicleTestDetails();
            baseTestResult.setImei(vehicle.getImei());
            baseTestResult.setVehicle(vehicle);
            baseTestResult.setStatus(vehicleTest.getStatus());
            baseTestResult.setVehicleTest(vehicleTest);
            baseTestResult.setVehicle(vehicle);
            baseTestResult.setCreatedOn(Instant.now());
            baseTestResult.setDriveMode(s.getDriveMode());
            baseTestResult.setTestType(vehicleTest.getTestType().getTestTypeName());
            baseTestResult.setCreatedOn(createdOn);
            conectivityDataPoints = null;
            conectivityDataPoints = result.stream()
                    .filter(e -> e.getName().equalsIgnoreCase(s.getDriveMode().name()))
                    .findAny().orElse(baseTestResult);
//            end = Optional.ofNullable(vehicleTest.getEndTime()).orElse(createdOn);

            conectivityDataPoints.setStatus(vehicleTest.getStatus());
            conectivityDataPoints.setName(s.getDriveMode().name().toLowerCase() + "_test_passed");


            Double val = 0d;
//            modeSpeeds.stream()
//                    .filter(e->e.getMode() == s.getDriveMode()).
//                    map(e->speed.get(s.getDriveMode().name()) > e.getMax()
//                            || speed.get(s.getDriveMode().name()) <= e.getMin())
//                    .findAny().get()?1d:0;

            log.info("drive mode selected {} ", s.getDriveMode());
            DriveModeSpeed e = modeSpeeds.stream()
                    .filter(x -> x.getDriveMode().getName().equals(s.getDriveMode().name())).findAny().get();

            if (Optional.ofNullable(speed.get(s.getDriveMode().name())).isPresent()
                    && speed.get(s.getDriveMode().name()) < e.getMax()
                    && speed.get(s.getDriveMode().name()) > e.getMin()) {
                val = 1d;

            }
            conectivityDataPoints.setValue(val);
            //log.info("name {},start {} ,end {}  ,val {}",s,start,end,val);
            saveResult.add(conectivityDataPoints);
        }
        return saveResult;
    }


    private static final String[] conectivity_dataPoints = {"last5min", "last1hr", "last24hr", "testDuration"};

    private List<VehicleTestDetails> getConnectivityBetweenTimestamps(VehicleTest vehicleTest, List<VehicleTestDetails> result) {
        Vehicle vehicle = vehicleTest.getVehicle();
        VehicleTestDetails conectivityDataPoints = null;
        Instant createdOn = Instant.now();
        Instant end = vehicleTest.getStartTime(), start = end;
        List<VehicleTestDetails> saveResult = new ArrayList<>();
        end = Optional.ofNullable(vehicleTest.getEndTime()).orElse(createdOn);
        VehicleMotorData mcsStatus = motorRepository.findFirstByMotorIdxImeiAndMotorIdxTimestampBetweenOrderByMotorIdxTimestampDesc(vehicle.getImei(), start, end).orElse(null);
        DriveMode mode = Optional.ofNullable(mcsStatus).isPresent() ? mcsStatus.getMotorDrivingMode() : DriveMode.ECO;
        Integer packetPerMin = 60;
        Double perc = 0d;
        for (String s : conectivity_dataPoints) {
            packetPerMin = 60;
            VehicleTestDetails baseTestResult = new VehicleTestDetails();
            baseTestResult.setImei(vehicle.getImei());
            baseTestResult.setVehicle(vehicle);
            baseTestResult.setStatus(vehicleTest.getStatus());
            baseTestResult.setVehicleTest(vehicleTest);
            baseTestResult.setVehicle(vehicle);
            baseTestResult.setCreatedOn(Instant.now());
            baseTestResult.setDriveMode(mode);
            baseTestResult.setTestType(vehicleTest.getTestType().getTestTypeName());
            baseTestResult.setCreatedOn(createdOn);
            conectivityDataPoints = null;
            conectivityDataPoints = result.stream()
                    .filter(e -> e.getName().equalsIgnoreCase(s))
                    .findAny().orElse(baseTestResult);
            end = Optional.ofNullable(vehicleTest.getEndTime()).orElse(createdOn);

            conectivityDataPoints.setStatus(vehicleTest.getStatus());
            conectivityDataPoints.setName(s);
            if (s.equalsIgnoreCase("last5min")) {
                start = end.minusSeconds(300);
                packetPerMin *= 5;
            } else if (s.equalsIgnoreCase("last1hr")) {
                start = end.minusSeconds(60 * 60);
                packetPerMin *= 60;
            } else if (s.equalsIgnoreCase("last24hr")) {
                start = end.minusSeconds(60 * 60 * 24);
                packetPerMin *= 60 * 24;
            } else if (s.equalsIgnoreCase("testDuration")) {
                Long diff = ChronoUnit.SECONDS.between(vehicleTest.getStartTime(), vehicleTest.getEndTime());
                conectivityDataPoints.setValue(Double.valueOf(diff));
                saveResult.add(conectivityDataPoints);
                continue;
            } else {
                Long diff = ChronoUnit.SECONDS.between(vehicleTest.getStartTime(), end);
                start = end.minusSeconds(diff);
                packetPerMin = diff.intValue();
                log.info("diff:" + diff);
            }

            Long val = telemetryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(vehicle.getImei(), start, end);

            perc = ((double) val / packetPerMin) * 100;
            log.info("vehicle {} start {} end {} distinct count{} perc {}", vehicle.getImei(), start, end, val, perc);
            conectivityDataPoints.setValue(Double.isNaN(perc) ? 0d : perc);
            saveResult.add(conectivityDataPoints);
        }
        return saveResult;
    }


    private static final List<String> partTypes = List.of(PartType.ACCELEROMETER
            , PartType.GPS
            , PartType.IO
            , PartType.BATTERY
            , PartType.TCU
            , PartType.MOTOR).stream().map(e -> e.name().toLowerCase()).collect(Collectors.toList());

    private List<VehicleTestDetails> getConnectStatus(VehicleTest vehicleTest, List<VehicleTestDetails> result) {
        Vehicle vehicle = vehicleTest.getVehicle();
        String imei = vehicle.getImei();
        Instant createdOn = Instant.now();
        Instant end = vehicleTest.getStartTime(), start = end;
        List<VehicleTestDetails> saveResult = new ArrayList<>();


        Integer val = 0;
        end = Optional.ofNullable(vehicleTest.getEndTime()).orElse(createdOn);
        VehicleTestDetails conectStatus = null;
        for (String s : partTypes) {
            val = 0;
            VehicleTestDetails baseTestResult = new VehicleTestDetails();
            baseTestResult.setImei(vehicle.getImei());
            baseTestResult.setVehicle(vehicle);
            baseTestResult.setStatus(vehicleTest.getStatus());
            baseTestResult.setVehicleTest(vehicleTest);
            baseTestResult.setVehicle(vehicle);
            baseTestResult.setDriveMode(DriveMode.ECO);
            baseTestResult.setTestType(vehicleTest.getTestType().getTestTypeName());
            baseTestResult.setCreatedOn(createdOn);
            conectStatus = null;
            conectStatus = result.stream()
                    .filter(e -> e.getName().equalsIgnoreCase("has_" + s)).findFirst().orElse(baseTestResult);
            Long diff = ChronoUnit.SECONDS.between(vehicleTest.getStartTime(), end);


            start = end.minusSeconds(diff);
            conectStatus.setName("has_" + s);
            conectStatus.setStatus(vehicleTest.getStatus());
            log.debug("start " + start);
            log.debug("end " + end);
//            conectStatus.setModifiedOn(Instant.now());
            log.debug("vehicleLocationDataCount {}", vehicleLocationDataCount);
            log.debug("vehicleTelemetryDataCount {}", vehicleTelemetryDataCount);
            log.debug("vehicleBatteryDataCount {}", vehicleBatteryDataCount);


            if (!s.equalsIgnoreCase(PartType.ACCELEROMETER.name())) {
                val = vehicleImuRepository.existsByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenAndAccelXAxisIsNotNull(imei, start, end) ? 1 : 0;
            } else if (!s.equalsIgnoreCase(PartType.GPS.name())) {
                val = vehicleLocationDataCount == diff ? 1 : 0;
            } else if (!s.equalsIgnoreCase(PartType.MOTOR.name())) {
                val = vehicleTelemetryDataCount == diff ? 1 : 0;
            } else if (!s.equalsIgnoreCase(PartType.IO.name())) {

                val = telemetryRepository.existsByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenAndAiVoltageInputIsNotNull(imei, start, end) ? 1 : 0;
            } else if (s.equalsIgnoreCase(PartType.BATTERY.name())) {
                log.debug("inside BATTERY");
                val = vehicleBatteryDataCount == diff ? 1 : 0;
            } else {
                log.debug("inside else condition");
                val = telemetryRepository.existsByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(imei, start, end) ? 1 : 0;
            }

            conectStatus.setValue(val.doubleValue());
//            conectStatus.setModifiedOn(LocalDateTime.now());
            log.info("name {},start {} ,end {} ,diff {} ,val {}", s, start, end, diff, val);
            saveResult.add(conectStatus);
        }
        return saveResult;
    }

    private static final List<String> thresholdBreach = List.of("voltage_checked", "current_checked");


    private List<VehicleTestDetails> getMotorThresholdStatus(VehicleTest vehicleTest, List<VehicleTestDetails> result) {
        Vehicle vehicle = vehicleTest.getVehicle();
        String imei = vehicle.getImei();
        Instant createdOn = Instant.now();
        Instant end = vehicleTest.getStartTime(), start = end;
        List<VehicleTestDetails> saveResult = new ArrayList<>();
        Integer val = 0;

        VehicleTestDetails conectStatus = null;
        for (String s : thresholdBreach) {
            end = Optional.ofNullable(vehicleTest.getEndTime()).orElse(createdOn);
            VehicleTestDetails baseTestResult = new VehicleTestDetails();
            baseTestResult.setImei(vehicle.getImei());
            baseTestResult.setVehicle(vehicle);
            baseTestResult.setStatus(vehicleTest.getStatus());
            baseTestResult.setVehicleTest(vehicleTest);
            baseTestResult.setDriveMode(DriveMode.ECO);
            baseTestResult.setTestType(vehicleTest.getTestType().getTestTypeName());
            baseTestResult.setCreatedOn(createdOn);
            conectStatus = null;
            conectStatus = result.stream()
                    .filter(e -> e.getName().equalsIgnoreCase(s)).findFirst().orElse(baseTestResult);
            Long diff = ChronoUnit.SECONDS.between(vehicleTest.getStartTime(), end);


            start = end.minusSeconds(diff);
            conectStatus.setName(s);
            conectStatus.setStatus(vehicleTest.getStatus());
            log.debug("start {} , end {} ", start, end);


            if (s.equalsIgnoreCase("current_checked")) {
                MeasuredStats currentStats = motorRepository.getCurrentStatsByImeiAndBetweenTimestamp(imei, start, end);
                val = currentStats.getMax() > 100d || currentStats.getMax() < 0 ? 0 : 1;
                log.debug("value {} , current {}", val, currentStats.getMax());
            } else {
                MeasuredStats voltStats = motorRepository.getVoltageStatsByImeiAndBetweenTimestamp(imei, start, end);
                val = voltStats.getMax() > 84d || voltStats.getMin() < 64 ? 0 : 1;
            }
            conectStatus.setValue(val.doubleValue());
//            conectStatus.setModifiedOn(LocalDateTime.now());

            saveResult.add(conectStatus);
        }
        return saveResult;
    }

    private List<VehicleTestDetails> getConnectivitySummary(VehicleTest vehicleTest) {
        log.info("inside getConnectivitySummary ");
        log.info("vehicleTest {}", vehicleTest);
        List<VehicleTestDetails> result = vehicleTestDetailsRepository.findByVehicleTest(vehicleTest);
        List<VehicleTestDetails> testDetails = new ArrayList<>();
        if (vehicleTest.getStatus() == TestStatus.RUNNING
                || result.isEmpty()
                || result.stream().filter(e -> e.getStatus() != vehicleTest.getStatus()).findAny().isPresent()) {

            log.info("NOT COMPLETED");

            Instant createdOn = Instant.now();
            Instant start = vehicleTest.getStartTime();
            Instant end = start;

            testDetails.addAll(getConnectivityBetweenTimestamps(vehicleTest, result));

            testDetails.addAll(getConnectStatus(vehicleTest, result));

            testDetails.addAll(getMotorThresholdStatus(vehicleTest, result));
        } else {
            testDetails.addAll(result);
        }

        return testDetails;
    }


    private static final Integer passPercentage = 85;

    private void saveConnectivityTestSummary(VehicleTest vehicleTest) {
        ConnectivityTestSummary testSummary = new ConnectivityTestSummary();
        testSummary.setVehicleTest(vehicleTest);
        testSummary.setVehicle(vehicleTest.getVehicle());
        testSummary.setImei(vehicleTest.getVehicle().getImei());
        testSummary.setTestType(vehicleTest.getTestType().getTestTypeName());

        Instant start = vehicleTest.getStartTime();
        Instant end = vehicleTest.getEndTime();
        String imei = vehicleTest.getVehicle().getImei();
        testSummary.setVehicle(vehicleTest.getVehicle());
        Long totalCount = ChronoUnit.SECONDS.between(start, end);
        Double ioPerc = availabilityPercentageByPartType(imei, start, end, PartType.IO, totalCount);
        testSummary.setIoAvailabilityPerc(ioPerc);
        testSummary.setIoTestResult(ioPerc.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);

        Double battPerc = availabilityPercentageByPartType(imei, start, end, PartType.BATTERY, totalCount);
        testSummary.setBattAvailabilityPerc(battPerc);
        testSummary.setBattTestResult(battPerc.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);

        Double accelPerc = availabilityPercentageByPartType(imei, start, end, PartType.ACCELEROMETER, totalCount);
        testSummary.setAccelAvailabilityPerc(accelPerc);
        testSummary.setAccelTestResult(accelPerc.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);

        Double locPerc = availabilityPercentageByPartType(imei, start, end, PartType.GPS, totalCount);
        testSummary.setLocAvailabilityPerc(locPerc);
        testSummary.setLocTestResult(locPerc.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);

        Double motorPerc = availabilityPercentageByPartType(imei, start, end, PartType.MOTOR, totalCount);
        testSummary.setMotorAvailabilityPerc(motorPerc);
        testSummary.setMotorTestResult(motorPerc.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);


        Long tcuCount = telemetryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(imei, start, end);
        Double tcuPerc = (double) tcuCount / totalCount * 100;
        testSummary.setTcuAvailabilityPerc(tcuPerc);
        testSummary.setTcuTestResult(tcuPerc.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);

        vehicleTest.setFinalResult(TestResult.FAIL);
        if (testSummary.getAccelTestResult() == TestVariableResult.PASS
                && testSummary.getLocTestResult() == TestVariableResult.PASS
                && testSummary.getBattTestResult() == TestVariableResult.PASS
//                && testSummary.getMotorTestResult()
                && testSummary.getTcuTestResult() == TestVariableResult.PASS
                && testSummary.getIoTestResult() == TestVariableResult.PASS
        ) {
            vehicleTest.setFinalResult(TestResult.PASS);
        }
        testSummary.setIteration(vehicleTest.getIteration());

        connectivityTestSummaryRepository.save(testSummary);

    }

    private Double availabilityPercentageByPartType(String vehicleId, Instant start, Instant end, PartType partType, Long totalCount) {
        Long unitCount = 1l;
        if (partType == PartType.ACCELEROMETER) {
            unitCount = vehicleImuRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenAndAccelXAxisIsNotNull(vehicleId, start, end);
        } else if (partType == PartType.TCU) {
            unitCount = telemetryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(vehicleId, start, end);
        } else if (partType == PartType.GPS) {
            unitCount = locationDataRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(vehicleId, start, end);
        } else if (partType == PartType.MOTOR) {
            unitCount = motorRepository.countByMotorIdxImeiAndMotorIdxTimestampBetweenAndMotorSpeedIsNotNull(vehicleId, start, end);
        } else if (partType == PartType.IO) {
            unitCount = telemetryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenAndAiVoltageInputIsNotNull(vehicleId, start, end);
        } else if (partType == PartType.BATTERY) {
            unitCount = telemetryBatteryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(vehicleId, start, end);
        } else {
            unitCount = 0l;
        }
        Double output = (double) unitCount / totalCount * 100;
//        log.info("vehicleId {} ,startTime {} , endTime{} , partType {},totalCountExpected {} ,actualCount {} , output {}",vehicleId,start,end,partType,totalCount,unitCount,output);
        return output;
    }

    private SpeedStats speedStatisticsByTimestampAndImei(Instant start, Instant end, String imei, DriveMode driveMode) {
        return motorRepository.getSpeedStatsByImeiAndBetweenTimestamp(imei, start, end, driveMode.name());
    }

    private void saveDynoTestSummary(VehicleTest vehicleTest) {
        log.debug("inside saveDynoTestSummary");
        List<DynoTestSummaryDriveModeDetails> result = new ArrayList<>();
        TestResult finalResult = TestResult.PASS;
        Instant start = vehicleTest.getStartTime();
        Instant end = Optional.ofNullable(vehicleTest.getEndTime()).orElse(Instant.now());
        log.debug("start {}", start);
        log.debug("end {}", end);
        log.debug("imei {}", vehicleTest.getVehicle().getImei());
        List<MotorStatusDriveModeMinMaxTimeDto> driveModeMinMaxSpeed = motorRepository
                .findMinAndMaxTimestampByDriveModeBetweenTimestampAndImei(start, end, vehicleTest.getVehicle().getImei()).stream().filter(e -> e.getCnt() > 0).collect(Collectors.toList());
        log.debug("size {}", driveModeMinMaxSpeed.size());

        Set<DriveModeSpeed> modeSpeeds = vehicleTest.getVehicle().getVehicleModel().getDriveModes();
        List<com.nichesolv.evahanam.vehicleModel.jpa.DriveMode> driveModes = modeSpeeds.stream().map(e -> e.getDriveMode()).collect(Collectors.toList());
        log.debug("size {}", driveModes.size());
        log.debug("size of modeSpeeds {}", modeSpeeds.size());
        for (MotorStatusDriveModeMinMaxTimeDto motorStatusDriveModeMinMaxTimeDto : driveModeMinMaxSpeed) {
            log.debug("inside loop mode {}", motorStatusDriveModeMinMaxTimeDto.getDriveMode().name());
            DynoTestSummaryDriveModeDetails testSummary = new DynoTestSummaryDriveModeDetails();
            testSummary.setVehicleTest(vehicleTest);
            testSummary.setVehicle(vehicleTest.getVehicle());
            testSummary.setTestType(vehicleTest.getTestType().getTestTypeName());
            testSummary.setIteration(vehicleTest.getIteration());
            testSummary.setImei(vehicleTest.getVehicle().getImei());
            testSummary.setVehicle(vehicleTest.getVehicle());
            DriveMode resultMode = motorStatusDriveModeMinMaxTimeDto.getDriveMode();
            com.nichesolv.evahanam.vehicleModel.jpa.DriveMode resultDriveMode = driveModes.stream().filter(e -> e.getName().equals(resultMode.name())).findFirst().orElse(null);
            log.debug("resultDriveMode {}", resultDriveMode);
            testSummary.setDriveMode(resultDriveMode);
            testSummary.setSpeedTestResult(TestVariableResult.FAIL);
            SpeedStats speedStatistics = null;
            DriveModeSpeed driveModeSpeed = null;
            if (motorStatusDriveModeMinMaxTimeDto.getDriveMode() == DriveMode.REVERSE) {
                speedStatistics = motorRepository.getReverseSpeedStatsByImeiAndBetweenTimestamp(vehicleTest.getVehicle().getImei(), motorStatusDriveModeMinMaxTimeDto.getMin().plusSeconds(3), motorStatusDriveModeMinMaxTimeDto.getMax(), true);
                driveModeSpeed = modeSpeeds.stream()
                        .filter(x -> Objects.equals(x.getDriveMode().getName(), motorStatusDriveModeMinMaxTimeDto.getDriveMode().name())).findAny().get();
            } else {
                speedStatistics = speedStatisticsByTimestampAndImei(motorStatusDriveModeMinMaxTimeDto.getMin().plusSeconds(3), motorStatusDriveModeMinMaxTimeDto.getMax(), vehicleTest.getVehicle().getImei(), motorStatusDriveModeMinMaxTimeDto.getDriveMode());
                driveModeSpeed = modeSpeeds.stream()
                        .filter(x -> x.getDriveMode().getName().equals(motorStatusDriveModeMinMaxTimeDto.getDriveMode().name())).findAny().get();
            }
            log.debug("mode {},max {},min {}, avg {}", motorStatusDriveModeMinMaxTimeDto.getDriveMode(), speedStatistics.getMax(), speedStatistics.getMin(), speedStatistics.getAvg());

            if (speedStatistics.getMin() > -1.0)
                testSummary.setSpeedMin(speedStatistics.getMin());
            if (speedStatistics.getAvg() > -1.0)
                testSummary.setSpeedAvg(speedStatistics.getAvg());
            if (speedStatistics.getMax() > -1.0)
                testSummary.setSpeedMax(speedStatistics.getMax());
            if (speedStatistics.getMedian() > -1.0)
                testSummary.setSpeedMedian(speedStatistics.getMedian());

//        testSummary.setSpeedMedian();

            if (testSummary.getSpeedMax() != null
                    && testSummary.getSpeedMin() != null
                    && testSummary.getSpeedAvg() != null
                    && testSummary.getSpeedMax() < driveModeSpeed.getMax()
                    && testSummary.getSpeedMax() > driveModeSpeed.getMin()
                    && testSummary.getSpeedMin() < driveModeSpeed.getMax()
                    && testSummary.getSpeedMin() > driveModeSpeed.getMin()
                    && testSummary.getSpeedAvg() < driveModeSpeed.getMax()
                    && testSummary.getSpeedAvg() > driveModeSpeed.getMin()) {
                testSummary.setSpeedTestResult(TestVariableResult.PASS);
            }
            if (testSummary.getSpeedTestResult() == TestVariableResult.FAIL || driveModeMinMaxSpeed.size() != modeSpeeds.size()) {
                finalResult = TestResult.FAIL;
            }
            result.add(testSummary);
        }
        if (!driveModeMinMaxSpeed.isEmpty()) {
            vehicleTest.setFinalResult(finalResult);
        }
        log.debug("result size {}", result.size());
        dynoTestSummaryRepository.saveAll(result);
    }

    public List<TestFactor> getTestFactor() {
        return testFactorRepository.findAll();
    }
}