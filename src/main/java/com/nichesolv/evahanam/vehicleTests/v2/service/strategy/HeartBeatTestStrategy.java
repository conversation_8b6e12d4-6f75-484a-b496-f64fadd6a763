package com.nichesolv.evahanam.vehicleTests.v2.service.strategy;

import com.nichesolv.evahanam.common.exception.ActiveSubscriptionPlanException;
import com.nichesolv.evahanam.common.exception.DataFrequencyPlanException;
import com.nichesolv.evahanam.common.exception.VehicleTestDurationException;
import com.nichesolv.evahanam.common.jpa.ActiveVehicleSubscriptionPlan;
import com.nichesolv.evahanam.common.jpa.Comment;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.jpa.VehicleTestDuration;
import com.nichesolv.evahanam.common.repository.ActiveVehicleSubscriptionPlanRepository;
import com.nichesolv.evahanam.common.repository.CommentRepository;
import com.nichesolv.evahanam.common.repository.VehicleTestDurationRepository;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.telemetryData.enums.*;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleMotorData;
import com.nichesolv.evahanam.telemetryData.repository.*;
import com.nichesolv.evahanam.vehicle.exception.VehicleNotFoundException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRegistrationDetailsRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleTests.dto.TestResultDto;
import com.nichesolv.evahanam.vehicleTests.dto.TestResultsResponse;
import com.nichesolv.evahanam.vehicleTests.enums.*;
import com.nichesolv.evahanam.vehicleTests.jpa.*;
import com.nichesolv.evahanam.vehicleTests.repository.*;
import com.nichesolv.evahanam.vehicleTests.v2.dto.ConnectivityTestConditionDto;
import com.nichesolv.evahanam.vehicleTests.v2.dto.ExpectedDataPoints;
import com.nichesolv.evahanam.vehicleTests.v2.dto.TestCalculationResult;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Component
@Slf4j
public class HeartBeatTestStrategy implements VehicleTestStrategy {

    @Autowired
    VehicleDataRepository telemetryRepository;

    @Autowired
    ImuDataRepository imuDataRepository;


    @Autowired
    MotorDataRepository motorDataRepository;

    @Autowired
    TelemetryBatteryRepository vehicleBatteryRepository;

    @Autowired
    LocationDataRepository vehicleLocationDataRepository;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    ActiveVehicleSubscriptionPlanRepository activeVehicleSubscriptionPlanRepository;

    @Autowired
    VehicleTestDurationRepository vehicleTestDurationRepository;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    TestFactorRepository testFactorRepository;

    @Autowired
    VehicleTestDetailsRepository vehicleTestDetailsRepository;

    @Autowired
    VehicleTestRepository vehicleTestRepository;

    @Autowired
    CommentRepository commentRepository;

    @Autowired
    TestFactorLimitRepository testFactorLimitRepository;

    @Autowired
    ConnectivityTestSummaryRepository connectivityTestSummaryRepository;

    @Autowired
    VehicleRegistrationDetailsRepository vehicleRegistrationDetailsRepository;

    @Autowired
    EntityManager entityManager;


    private final Integer telemetryColumnCount = TelemetryTableColumn.values().length;


    private final Integer imuColumnCount = ImuTableColumn.values().length;

    private final Integer motorColumnCount = MotorTableColumn.values().length;

    private final Integer batteryColumnCount = BatteryTableColumn.values().length;


    private final Integer locationColumnCount = LocationTableColumn.values().length;


    private static final Integer passPercentage = 85;


    @Transactional
    public TestResultsResponse evaluateTest(VehicleTest vehicleTest) {
        log.info("inside the heartbeat test");
        log.info(" location {} , motor {} , telemetry {} , battery {}", locationColumnCount, motorColumnCount, telemetryColumnCount, batteryColumnCount);

        Vehicle vehicle = vehicleRepository.findById(vehicleTest.getVehicle().getId()).orElseThrow(() -> new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));
        ActiveVehicleSubscriptionPlan activeVehicleSubscriptionPlan = activeVehicleSubscriptionPlanRepository.findByVehicle(vehicle)
                .orElseThrow(() -> new ActiveSubscriptionPlanException(evMessageBundle.getMessage("ACTIVE_SUBSCRIPTION_PLAN_NOT_FOUND", vehicle.getId())));

        DataFrequencyPlan dataFrequencyPlan = Optional.ofNullable(activeVehicleSubscriptionPlan.getDataFrequencyPlan())
                .orElseThrow(()-> new DataFrequencyPlanException(evMessageBundle.getMessage("DATA_FREQUENCY_PLAN_NOT_PRESENT")));
        log.debug(" data frequency plan value {} ", dataFrequencyPlan.getValue());

        VehicleTestDuration vehicleTestTime = vehicleTestDurationRepository.findByTestTypeNameAndDataFrequencyPlan(vehicleTest.getTestType().getTestTypeName(), dataFrequencyPlan)
                .orElseThrow(() -> new VehicleTestDurationException("VEHICLE_TEST_DURATION_NOT_FOUND"));

        if (vehicleTest.getStatus().equals(TestStatus.COMPLETED)) {
            return processCompletedVehicleTest(vehicleTest, vehicle , vehicleTestTime.getDuration() ,dataFrequencyPlan );
        }


        TestCalculationResult testCalculationResult = performTestCalculations(vehicleTest, vehicle, vehicleTestTime.getDuration(), dataFrequencyPlan);
        List<ConnectivityTestConditionDto> connectivityTestCondtionDtoList = evaluateTestFactors(vehicleTest,
                testCalculationResult.getTelemetryDataPercentage(),
                testCalculationResult.getMotorDataPercentage(),
                testCalculationResult.getBatteryDataPercentage(),
                testCalculationResult.getLocationDataPercentage(),
                testCalculationResult.getOverallDataPercentage(),
                testCalculationResult.getLatestMotorDcVoltage(),
                testCalculationResult.getLatestMotorDcCurrent(),
                testCalculationResult.getLatestBatteryVoltage(),
                testCalculationResult.getLatestAiVoltage(),
                testCalculationResult.getLatestBatteryCurrent(),
                testCalculationResult.getSoc(),
                testCalculationResult.getDataLag());

        Map<TestCategory, List<String>> connectivityMetaData =  getConnectivityMetaData();
        TestResultDto testResultDto = new TestResultDto(connectivityTestCondtionDtoList, connectivityMetaData);
        return new TestResultsResponse(
                vehicle.getImei(),
                vehicleTest.getId(),
                testCalculationResult.getStartTime().toEpochMilli(),
                testCalculationResult.getEndTime().toEpochMilli(),
                vehicleTest.getStatus(),
                vehicleTest.getTestType().getTestTypeName(),
                Optional.of(testResultDto),
                vehicleTest.getIteration(),
                null
        );
    }

    private TestCalculationResult performTestCalculations(VehicleTest vehicleTest, Vehicle vehicle, Integer duration, DataFrequencyPlan dataFrequencyPlan) {
        Instant startTime = vehicleTest.getStartTime();
        Instant endTime = vehicleTest.getStatus().equals(TestStatus.COMPLETED)
                ? vehicleTest.getEndTime()
                : Instant.now().isBefore(vehicleTest.getEndTime())
                ? Instant.now()
                : vehicleTest.getEndTime();

        ExpectedDataPoints expectedDataPoints = calculateExpectedDataPoints(duration, dataFrequencyPlan, telemetryColumnCount,
                motorColumnCount, batteryColumnCount, locationColumnCount,imuColumnCount);

        Float soc = Optional.ofNullable(vehicleBatteryRepository.findLatestSoc(vehicle.getImei(), startTime, endTime)).orElse(0f);
        Float latestAiVoltage = findLatestMotorFieldData(vehicle.getImei(), "ai_voltage_input", startTime, endTime,false);
        Float latestMotorDcCurrent = findLatestMotorFieldData(vehicle.getImei(), "motor_dc_current", startTime, endTime,true);
        Float latestMotorDcVoltage = findLatestMotorFieldData(vehicle.getImei(), "motor_dc_voltage", startTime, endTime,true);
        Float latestBatteryCurrent = findLatestBatteryFieldData(vehicle.getImei(), "current", startTime, endTime);
        Float latestBatteryVoltage = findLatestBatteryFieldData(vehicle.getImei(), "battery_volt", startTime, endTime);

        Integer telemetryDataCounts = telemetryRepository.getTotalTelemetryFieldCount(vehicle.getImei(), startTime, endTime);
        Integer motorDataCounts = motorDataRepository.getTotalMotorFieldCount(vehicle.getImei(), startTime, endTime);
        Integer batteryDataCounts = vehicleBatteryRepository.getTotalBatteryFieldCount(vehicle.getImei(), startTime, endTime);
        Integer locationDataCounts = vehicleLocationDataRepository.getTotalLocationFieldCount(vehicle.getImei(), startTime, endTime);
        Integer imuDataCounts = imuDataRepository.getTotalImuFieldCount(vehicle.getImei(), startTime, endTime);
        Integer overallDataCounts = telemetryDataCounts + motorDataCounts + batteryDataCounts + locationDataCounts;

        Float telemetryDataPercentage = calculateDataPercentage(telemetryDataCounts, expectedDataPoints.getTelemetry());
        Float motorDataPercentage = calculateDataPercentage(motorDataCounts, expectedDataPoints.getMotor());
        Float batteryDataPercentage = calculateDataPercentage(batteryDataCounts, expectedDataPoints.getBattery());
        Float locationDataPercentage = calculateDataPercentage(locationDataCounts, expectedDataPoints.getLocation());
        Float imuDataPercentage = calculateDataPercentage(imuDataCounts, expectedDataPoints.getImu());
        Float overallDataPercentage = calculateDataPercentage(overallDataCounts, expectedDataPoints.getOverall());
        log.debug("telemetryPercentage {} , startTime :{} , endTime :{} , telemetryDataCounts {} , expectedTelemetryCounts {}  ", telemetryDataPercentage, startTime, endTime , telemetryDataCounts , expectedDataPoints.getTelemetry());
        log.debug("motorPercentage {} , startTime :{} , endTime :{} , motorDataCounts {} , expectedMotorCounts {} ", motorDataPercentage, startTime, endTime , motorDataCounts , expectedDataPoints.getMotor());
        log.debug("locationPercentage {}, startTime :{} , endTime :{} , locationDataCounts {} , expectedLocationCounts {} ", locationDataPercentage, startTime, endTime , locationDataCounts , expectedDataPoints.getLocation());
        log.debug("imuDataPercentage {} , startTime :{} , endTime :{} , imuDataCounts {} , expectedImuCounts {} ", imuDataPercentage, startTime, endTime , imuDataCounts , expectedDataPoints.getImu());
        log.debug("batteryPercentage {}, startTime :{} , endTime :{} , batteryDataCounts {} , expectedBatteryCounts {} ", batteryDataPercentage, startTime, endTime , batteryDataCounts , expectedDataPoints.getBattery());

        Float dataLag = Optional.ofNullable(telemetryRepository.getAvgLagByImeiAndTimestampBetween(vehicle.getImei(), startTime, endTime))
                .map(value -> (float) Math.round(value * 100) / 100)
                .orElse(null);

        return new TestCalculationResult(telemetryDataPercentage, imuDataPercentage,motorDataPercentage, locationDataPercentage, batteryDataPercentage,
                overallDataPercentage, latestMotorDcCurrent , latestMotorDcVoltage , latestBatteryVoltage , latestBatteryCurrent ,
                soc , latestAiVoltage , dataLag , startTime , endTime);
    }

    @Transactional
    private void saveVehicleTestDetails(VehicleTest vehicleTest, List<ConnectivityTestConditionDto> connectivityTestCondtionDtoList) {
        List<VehicleTestDetails> finalTestDetails = new ArrayList<>();
        for (ConnectivityTestConditionDto connectivity : connectivityTestCondtionDtoList) {
            VehicleTestDetails vehicleTestDetails = new VehicleTestDetails();
            vehicleTestDetails.setVehicleTest(vehicleTest);
            vehicleTestDetails.setTestType(vehicleTest.getTestType().getTestTypeName());
            vehicleTestDetails.setImei(vehicleTest.getVehicle().getImei());
            vehicleTestDetails.setVehicle(vehicleTest.getVehicle());
            vehicleTestDetails.setStatus(vehicleTest.getStatus());
            Optional<VehicleMotorData> vehicleTelemetryData = motorDataRepository.findFirstByMotorIdxImeiAndMotorIdxTimestampBetweenOrderByMotorIdxTimestampDesc(vehicleTest.getVehicle().getImei(), vehicleTest.getStartTime(), vehicleTest.getEndTime());
            vehicleTelemetryData.ifPresent(telemetryData ->
                    vehicleTestDetails.setDriveMode(telemetryData.getMotorDrivingMode())
            );
            vehicleTestDetails.setName(connectivity.getName().name());
            vehicleTestDetails.setValue(connectivity.getValue() != null ? connectivity.getValue().doubleValue() : null);
            finalTestDetails.add(vehicleTestDetails);
            log.info(" size of the list {} : ", finalTestDetails.size());
        }
        if(!vehicleTestDetailsRepository.existsByVehicleTest(vehicleTest)) {
            vehicleTestDetailsRepository.saveAll(finalTestDetails);
        }
        if (!connectivityTestSummaryRepository.existsByVehicleTest(vehicleTest)) {
            saveConnectivityTestSummary(vehicleTest);
        }
    }

    private Float calculateDataPercentage(Integer actualDataCount, Integer expectedDataCount) {
        return (float) Math.round(((float) actualDataCount / expectedDataCount) * 100);
    }


    private List<ConnectivityTestConditionDto> evaluateTestFactors(VehicleTest vehicleTest, Float telemetryDataPercentage, Float motorDataPercentage, Float batteryDataPercentage, Float locationDataPercentage,
                                                                   Float overallDataPercentage, Float latestMotorDcVoltage, Float latestMotorDcCurrent, Float latestBatteryVoltage, Float latestAiVoltage, Float latestBatteryCurrent, Float soc, Float dataLag) {
        List<TestFactor> testFactorList = testFactorRepository.findByTestType(vehicleTest.getTestType());
        List<ConnectivityTestConditionDto> connectivityTestCondtionDtoList = new ArrayList<>();
        Map<TestCondition, Float> conditionValueMap = new HashMap<>();
        conditionValueMap.put(TestCondition.TCU_DATA_AVAILABILITY, telemetryDataPercentage);
        conditionValueMap.put(TestCondition.MOTOR_DATA_AVAILABILITY, motorDataPercentage);
        conditionValueMap.put(TestCondition.BATTERY_DATA_AVAILABILITY, batteryDataPercentage);
        conditionValueMap.put(TestCondition.LOCATION_DATA_AVAILABILITY, locationDataPercentage);
        conditionValueMap.put(TestCondition.OVERALL_DATA_AVAILABILITY, overallDataPercentage);
        conditionValueMap.put(TestCondition.MOTOR_CURRENT_IN_RANGE, latestMotorDcCurrent);
        conditionValueMap.put(TestCondition.MOTOR_VOLTAGE_IN_RANGE, latestMotorDcVoltage);
        conditionValueMap.put(TestCondition.BATTERY_CURRENT_IN_RANGE, latestBatteryCurrent);
        conditionValueMap.put(TestCondition.AI_VOLTAGE_IN_RANGE, latestAiVoltage);
        conditionValueMap.put(TestCondition.BATTERY_VOLTAGE_IN_RANGE, latestBatteryVoltage);
        conditionValueMap.put(TestCondition.LATEST_SOC, soc);
        conditionValueMap.put(TestCondition.DATA_LAG, dataLag);

        conditionValueMap.forEach((key, value) ->
                log.debug("Condition: {}, Value: {}", key, value)
        );

        for (TestFactor testFactor : testFactorList) {
            TestCondition testCondition = testFactor.getTestCondition();
            Float conditionValue = conditionValueMap.get(testCondition);
            ConnectivityTestConditionDto dto = new ConnectivityTestConditionDto();
            dto.setName(testCondition);
            dto.setValue(conditionValue);
            dto.setCategory(testFactor.getTestCategory());
            dto.setUnit(testFactor.getUnit());
            if (vehicleTest.getStatus().equals(TestStatus.COMPLETED)) {
                evaluateTestConditionLimits(testFactor, dto);
            }
            connectivityTestCondtionDtoList.add(dto);
        }
        return connectivityTestCondtionDtoList;
    }

    private Map<TestCategory, List<String>> getConnectivityMetaData() {
        return Map.ofEntries(
                Map.entry(TestCategory.AVAILABILITY, List.of("GOOD", "AVERAGE", "POOR")),
                Map.entry(TestCategory.RANGE, List.of("IN_LIMIT", "UPPER_LIMIT", "LOWER_LIMIT")),
                Map.entry(TestCategory.SINGLE_VALUE, List.of()),
                Map.entry(TestCategory.PASS_FAIL, List.of())
        );
    }


    private void evaluateTestConditionLimits(TestFactor testFactor, ConnectivityTestConditionDto dto) {
        List<TestFactorLimit> testFactorLimitList = testFactorLimitRepository.findByTestFactor(testFactor);
        Float value = dto.getValue();
        log.debug("inside the evaluateTest Condition");
        if(value == null)
        {
            log.warn("value null for testFactor {}" , dto.getName());
            dto.setResult(null);
        }
        else {
            for (TestFactorLimit limit : testFactorLimitList) {
                if (limit.getRangeLabel().equals(RangeLabel.POOR) && value >= limit.getMinValue() && value < limit.getMaxValue()) {
                    dto.setResult(RangeLabel.POOR.name());
                } else if (limit.getRangeLabel().equals(RangeLabel.AVERAGE) && value >= limit.getMinValue() && value < limit.getMaxValue()) {
                    dto.setResult(RangeLabel.AVERAGE.name());
                } else if (limit.getRangeLabel().equals(RangeLabel.GOOD) && value >= limit.getMinValue() && value <= limit.getMaxValue()) {
                    dto.setResult(RangeLabel.GOOD.name());
                } else if (limit.getRangeLabel().equals(RangeLabel.LOWER_LIMIT) && value >= limit.getMinValue() && value < limit.getMaxValue()) {
                    dto.setResult(RangeLabel.LOWER_LIMIT.name());
                } else if (limit.getRangeLabel().equals(RangeLabel.UPPER_LIMIT) && value > limit.getMinValue() && value <= limit.getMaxValue()) {
                    dto.setResult(RangeLabel.UPPER_LIMIT.name());
                } else if (limit.getRangeLabel().equals(RangeLabel.IN_LIMIT) && value >= limit.getMinValue() && value <= limit.getMaxValue()) {
                    dto.setResult(RangeLabel.IN_LIMIT.name());
                }
            }
        }
    }
    private TestResultsResponse processCompletedVehicleTest(VehicleTest vehicleTest, Vehicle vehicle , Integer duration , DataFrequencyPlan dataFrequencyPlan) {
        List<VehicleTestDetails> testDetails = vehicleTestDetailsRepository.findByVehicleTest(vehicleTest);
        List<ConnectivityTestConditionDto> connectivityTestConditionDtoList = null;
        Float telemetryDataPercentage = null, batteryDataPercentage = null, motorDataPercentage = null,
                locationDataPercentage = null, overallDataPercentage = null, latestAiVoltage = null,
                latestBatteryVoltage = null, latestBatteryCurrent = null, latestMotorDcCurrent = null,
                latestMotorDcVoltage = null, dataLag = null, soc = null;

        if (!testDetails.isEmpty()) {
            log.info("Inside the status completed after saving to vehicle test details table");

            for (VehicleTestDetails vehicleTestDetail : testDetails) {
                TestCondition condition = TestCondition.valueOf(vehicleTestDetail.getName());
                Float value = vehicleTestDetail.getValue() != null ? vehicleTestDetail.getValue().floatValue() : null;

                switch (condition) {
                    case TCU_DATA_AVAILABILITY -> telemetryDataPercentage = value;
                    case BATTERY_DATA_AVAILABILITY -> batteryDataPercentage = value;
                    case MOTOR_DATA_AVAILABILITY -> motorDataPercentage = value;
                    case LOCATION_DATA_AVAILABILITY -> locationDataPercentage = value;
                    case OVERALL_DATA_AVAILABILITY -> overallDataPercentage = value;
                    case AI_VOLTAGE_IN_RANGE -> latestAiVoltage = value;
                    case BATTERY_VOLTAGE_IN_RANGE -> latestBatteryVoltage = value;
                    case BATTERY_CURRENT_IN_RANGE -> latestBatteryCurrent = value;
                    case MOTOR_CURRENT_IN_RANGE -> latestMotorDcCurrent = value;
                    case MOTOR_VOLTAGE_IN_RANGE -> latestMotorDcVoltage = value;
                    case DATA_LAG -> dataLag = value;
                    case LATEST_SOC -> soc = value;
                }
            }
            connectivityTestConditionDtoList = evaluateTestFactors(
                    vehicleTest, telemetryDataPercentage, motorDataPercentage, batteryDataPercentage,
                    locationDataPercentage, overallDataPercentage, latestMotorDcVoltage, latestMotorDcCurrent,
                    latestBatteryVoltage, latestAiVoltage, latestBatteryCurrent, soc, dataLag);
        } else {
            TestCalculationResult testCalculationResult = performTestCalculations(vehicleTest, vehicle, duration, dataFrequencyPlan);
            connectivityTestConditionDtoList = evaluateTestFactors(vehicleTest,
                    testCalculationResult.getTelemetryDataPercentage(),
                    testCalculationResult.getMotorDataPercentage(),
                    testCalculationResult.getBatteryDataPercentage(),
                    testCalculationResult.getLocationDataPercentage(),
                    testCalculationResult.getOverallDataPercentage(),
                    testCalculationResult.getLatestMotorDcVoltage(),
                    testCalculationResult.getLatestMotorDcCurrent(),
                    testCalculationResult.getLatestBatteryVoltage(),
                    testCalculationResult.getLatestAiVoltage(),
                    testCalculationResult.getLatestBatteryCurrent(),
                    testCalculationResult.getSoc(),
                    testCalculationResult.getDataLag());
            saveVehicleTestDetails(vehicleTest , connectivityTestConditionDtoList);
        }
        return new TestResultsResponse(
                vehicle.getImei(),
                vehicleTest.getId(),
                vehicleTest.getStartTime().toEpochMilli(),
                vehicleTest.getEndTime().toEpochMilli(),
                vehicleTest.getStatus(),
                vehicleTest.getTestType().getTestTypeName(),
                Optional.of(new TestResultDto(connectivityTestConditionDtoList, getConnectivityMetaData())),
                vehicleTest.getIteration(),
                getComment(vehicleTest)
        );
    }



    private String getComment(VehicleTest test) {
        return commentRepository.findByVehicleTest(test).stream().map(Comment::getComment).findAny().orElse(null);
    }

    public Float findLatestMotorFieldData(String imei, String motorField, Instant startTime, Instant endTime, boolean isMotorField) {

        String tableName = "vehicle_motor_data";
        if (!isMotorField) {
            tableName = "vehicle_telemetry_data";
        }
        String queryStr = "SELECT " + motorField + " AS latestData " +
                "FROM " + tableName + " " +
                "WHERE imei = :imei " +
                "AND timestamp BETWEEN :startTime AND :endTime " +
                "AND " + motorField + " IS NOT NULL " +
                "ORDER BY timestamp DESC LIMIT 1";

        Query query = entityManager.createNativeQuery(queryStr);
        query.setParameter("imei", imei);
        query.setParameter("startTime", startTime);
        query.setParameter("endTime", endTime);

        try {
            return ((Number) query.getSingleResult()).floatValue();  // No need for null check
        } catch (NoResultException e) {
            return null;
        }
    }


    public Float findLatestBatteryFieldData(String imei, String motorField, Instant startTime, Instant endTime) {
        String queryStr = "SELECT " + motorField + " AS latestData " +
                "FROM vehicle_battery_data " +
                "WHERE imei = :imei " +
                "AND timestamp BETWEEN :startTime AND :endTime " +
                "AND " + motorField + " IS NOT NULL " +
                "ORDER BY timestamp DESC LIMIT 1";

        Query query = entityManager.createNativeQuery(queryStr);
        query.setParameter("imei", imei);
        query.setParameter("startTime", startTime);
        query.setParameter("endTime", endTime);

        try {
            return ((Number) query.getSingleResult()).floatValue();  // No need for null check
        } catch (NoResultException e) {
            return null;
        }
    }

    private ExpectedDataPoints calculateExpectedDataPoints(
            Integer vehicleTestDuration, DataFrequencyPlan dataFrequencyPlan, Integer telemetryColumnCount, Integer motorColumnCount,
            Integer batteryColumnCount, Integer locationColumnCount,Integer imuColumnCount) {
        Integer frequency = dataFrequencyPlan.getValue();
        return new ExpectedDataPoints(
                (vehicleTestDuration / frequency) * telemetryColumnCount,
                (vehicleTestDuration / frequency) * motorColumnCount,
                (vehicleTestDuration / frequency) * batteryColumnCount,
                (vehicleTestDuration / frequency) * locationColumnCount,
                (vehicleTestDuration / frequency) * imuColumnCount,
                (vehicleTestDuration / frequency) * (telemetryColumnCount + motorColumnCount + batteryColumnCount + locationColumnCount + imuColumnCount)
        );
    }


    private void saveConnectivityTestSummary(VehicleTest vehicleTest) {
        ConnectivityTestSummary testSummary = new ConnectivityTestSummary();
        testSummary.setVehicleTest(vehicleTest);
        testSummary.setVehicle(vehicleTest.getVehicle());
        testSummary.setImei(vehicleTest.getVehicle().getImei());
        testSummary.setTestType(vehicleTest.getTestType().getTestTypeName());

        Instant start = vehicleTest.getStartTime();
        Instant end = vehicleTest.getEndTime();
        String imei = vehicleTest.getVehicle().getImei();

        Long totalCount = ChronoUnit.SECONDS.between(start, end);
        Double ioPercentage = availabilityPercentageByPartType(imei, start, end, PartType.IO, totalCount);
        testSummary.setIoAvailabilityPerc(ioPercentage);
        testSummary.setIoTestResult(ioPercentage.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);

        Double batteryPercentage = availabilityPercentageByPartType(imei, start, end, PartType.BATTERY, totalCount);
        testSummary.setBattAvailabilityPerc(batteryPercentage);
        testSummary.setBattTestResult(batteryPercentage.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);

        Double accelPercentage = availabilityPercentageByPartType(imei, start, end, PartType.ACCELEROMETER, totalCount);
        testSummary.setAccelAvailabilityPerc(accelPercentage);
        testSummary.setAccelTestResult(accelPercentage.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);

        Double locationPercentage = availabilityPercentageByPartType(imei, start, end, PartType.GPS, totalCount);
        testSummary.setLocAvailabilityPerc(locationPercentage);
        testSummary.setLocTestResult(locationPercentage.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);

        Double motorPercentage = availabilityPercentageByPartType(imei, start, end, PartType.MOTOR, totalCount);
        testSummary.setMotorAvailabilityPerc(motorPercentage);
        testSummary.setMotorTestResult(motorPercentage.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);


        Long tcuCount = telemetryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(imei, start, end);
        Double tcuPercentage = (double) tcuCount / totalCount * 100;
        testSummary.setTcuAvailabilityPerc(tcuPercentage);
        testSummary.setTcuTestResult(tcuPercentage.intValue() >= passPercentage ? TestVariableResult.PASS : TestVariableResult.FAIL);

        vehicleTest.setFinalResult(TestResult.FAIL);
        if (testSummary.getAccelTestResult() == TestVariableResult.PASS
                && testSummary.getLocTestResult() == TestVariableResult.PASS
                && testSummary.getBattTestResult() == TestVariableResult.PASS
//                && testSummary.getMotorTestResult()
                && testSummary.getTcuTestResult() == TestVariableResult.PASS
                && testSummary.getIoTestResult() == TestVariableResult.PASS
        ) {
            vehicleTest.setFinalResult(TestResult.PASS);
        }
        vehicleTestRepository.save(vehicleTest);
        testSummary.setIteration(vehicleTest.getIteration());
        connectivityTestSummaryRepository.save(testSummary);

    }

    private Double availabilityPercentageByPartType(String vehicleId, Instant start, Instant end, PartType partType, Long totalCount) {
        Long unitCount = switch (partType) {
            case ACCELEROMETER -> imuDataRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenAndAccelXAxisIsNotNull(vehicleId, start, end);
            case TCU -> telemetryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(vehicleId, start, end);
            case GPS -> vehicleLocationDataRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(vehicleId, start, end);
            case MOTOR -> motorDataRepository.countByMotorIdxImeiAndMotorIdxTimestampBetweenAndMotorSpeedIsNotNull(vehicleId, start, end);
            case IO -> telemetryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenAndAiVoltageInputIsNotNull(vehicleId, start, end);
            case BATTERY -> vehicleBatteryRepository.countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(vehicleId, start, end);
            default -> 0L;
        };
        return totalCount == 0 ? 0.0 : (double) unitCount / totalCount * 100;
    }



    @Override
    public boolean supportsTest(VehicleTest vehicleTest) {
        return vehicleTest.getTestType().getTestTypeName().equals(TestTypeName.CONNECTIVITY);
    }
}


