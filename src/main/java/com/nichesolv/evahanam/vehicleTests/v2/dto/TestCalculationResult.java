package com.nichesolv.evahanam.vehicleTests.v2.dto;

import lombok.Data;

import java.time.Instant;

@Data
public class TestCalculationResult {
    Float telemetryDataPercentage;
    Float imuDataPercentage;
    Float motorDataPercentage;
    Float locationDataPercentage;
    Float batteryDataPercentage;
    Float overallDataPercentage;
    Float latestMotorDcCurrent;
    Float latestMotorDcVoltage;
    Float latestBatteryVoltage;
    Float latestBatteryCurrent;
    Float soc;
    Float latestAiVoltage;
    Float dataLag;
    Instant startTime;
    Instant endTime;
    public TestCalculationResult(Float telemetryDataPercentage, Float imuDataPercentage, Float motorDataPercentage, Float locationDataPercentage, Float batteryDataPercentage, Float overallDataPercentage, Float latestMotorDcCurrent, Float latestMotorDcVoltage, Float latestBatteryVoltage, Float latestBatteryCurrent, Float soc, Float latestAiVoltage, Float dataLag, Instant startTime, Instant endTime) {
        this.telemetryDataPercentage = telemetryDataPercentage;
        this.motorDataPercentage = motorDataPercentage;
        this.locationDataPercentage = locationDataPercentage;
        this.batteryDataPercentage = batteryDataPercentage;
        this.overallDataPercentage = overallDataPercentage;
        this.latestMotorDcCurrent = latestMotorDcCurrent;
        this.latestMotorDcVoltage = latestMotorDcVoltage;
        this.latestBatteryVoltage = latestBatteryVoltage;
        this.latestBatteryCurrent = latestBatteryCurrent;
        this.soc = soc;
        this.latestAiVoltage = latestAiVoltage;
        this.dataLag = dataLag;
        this.startTime = startTime;
        this.endTime = endTime;
        this.imuDataPercentage = imuDataPercentage;
    }
}
