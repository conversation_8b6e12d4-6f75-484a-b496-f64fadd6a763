package com.nichesolv.evahanam.vehicleTests.v2.dto;

import lombok.Getter;

@Getter
public class ExpectedDataPoints {

    Integer telemetry;
    Integer motor;
    Integer battery;
    Integer location;
    Integer imu;
    Integer overall;

    public ExpectedDataPoints(Integer telemetry, Integer motor, Integer battery, Integer location, Integer imu, Integer overall) {
        this.telemetry = telemetry;
        this.battery = battery;
        this.motor = motor;
        this.location = location;
        this.overall = overall;
        this.imu = imu;
    }
}
