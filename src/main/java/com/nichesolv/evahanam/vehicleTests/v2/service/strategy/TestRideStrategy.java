package com.nichesolv.evahanam.vehicleTests.v2.service.strategy;

import com.nichesolv.evahanam.vehicleTests.dto.TestResultsResponse;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import org.springframework.stereotype.Component;

@Component
public class TestRideStrategy implements VehicleTestStrategy {

    @Override
    public TestResultsResponse evaluateTest(VehicleTest vehicleTest)
    {
        return null;
    }


    @Override
    public boolean supportsTest(VehicleTest vehicleTest) {
        return vehicleTest.getTestType().getTestTypeName().equals(TestTypeName.TEST_RIDE);
    }
}
