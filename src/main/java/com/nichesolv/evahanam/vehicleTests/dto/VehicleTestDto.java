package com.nichesolv.evahanam.vehicleTests.dto;

import com.nichesolv.evahanam.vehicle.dto.VehicleIdentifiers;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class VehicleTestDto extends VehicleIdentifiers {
    Long testId;
    String imei;
    Long startTime;
    Long stopTime;
    TestTypeName type;

    Long testDate;
    TestStatus status;

    Integer iteration;

    String comment;


    public VehicleTestDto(Long testId,String imei, Long startTime, Long stopTime, TestTypeName type, Long testDate, TestStatus status) {
        super(imei);
        this.imei = imei;
        this.startTime = startTime;
        this.stopTime = stopTime;
        this.type = type;
        this.testDate = testDate;
        this.status = status;
        this.testId = testId;
    }

    public VehicleTestDto(Long testId, String imei, Long startTime, Long stopTime, TestTypeName type, Long testDate, TestStatus status, Integer iteration, String comment) {
        super(imei);
        this.testId = testId;
        this.imei = imei;
        this.startTime = startTime;
        this.stopTime = stopTime;
        this.type = type;
        this.testDate = testDate;
        this.status = status;
        this.iteration = iteration;
        this.comment = comment;
    }
}
