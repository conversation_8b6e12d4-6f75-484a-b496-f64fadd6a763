package com.nichesolv.evahanam.vehicleTests.controller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.evahanam.util.UserOrganisationUtils;
import com.nichesolv.evahanam.vehicleTests.dto.*;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.exception.VehicleTestException;
import com.nichesolv.evahanam.vehicleTests.jpa.TestFactor;
import com.nichesolv.evahanam.vehicleTests.service.IVehicleEolService;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController("v1")
@RequestMapping("/vehicle-tests")
@SecurityRequirement(name = "Bearer Authentication")
public class VehicleTestsController {

    @Autowired
    IVehicleEolService iVehicleEolService;

    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;

    @Autowired
    UserOrganisationUtils userOrganisationUtils;

    @PostMapping({"/start"})
    public StartTestResponse startTest(@Validated @RequestBody StartTestRequest request, @AuthenticationPrincipal CustomUser user, HttpServletRequest httpServletRequest) {
        httpRequestOriginUtil.checkVehicleBelongToOrganisation(request.getImei(), null, httpServletRequest);
        return iVehicleEolService.startTest(request,user);
    }


    @GetMapping("/results")
    @ReadOnly
    public TestResultsResponse getResult(@RequestParam("testId") Long testId
            , @RequestParam(value = "imei",required = false) String imei, HttpServletRequest request,
                                         @RequestAttribute (value = "identifier",required = false) String identifier,
                                         @RequestParam(value = "vIdVal",required = false) String idValue) throws JsonProcessingException {
        return iVehicleEolService.getTestResults_Formatter(testId, identifier);

    }

    @PostMapping({"/stop"})
    public TestResultsResponse stop(@Validated @RequestBody StopTestRequest request, HttpServletRequest httpServletRequest) throws VehicleTestException {
        httpRequestOriginUtil.checkVehicleBelongToOrganisation(request.getImei(), null, httpServletRequest);
        return iVehicleEolService.stopTest_Formatted(request);

    }

    @GetMapping
    @ReadOnly
    public VehicleTestListApiDto getTestList(@RequestParam(value = "type") String testType,
                                            @RequestParam(value = "imei",required = false) String imei,
                                            @PageableDefault(size = 10, page = 0, direction = Sort.Direction.DESC) Pageable pageable,
                                             @RequestAttribute (value = "identifier") String identifier,
                                             @RequestParam(value = "vIdVal",required = false) String idValue,
                                            HttpServletRequest request) {

        return iVehicleEolService.getTestList(TestTypeName.valueOf(testType), identifier, pageable);


    }

    @GetMapping("/dyno")
    @ReadOnly
    public Map<String, List<DriveModePassPercentage>> getDynoTestPassPercentage(@RequestParam Optional<Long> orgId, HttpServletRequest request) {
        CustomOrganisation organisation = orgId.map(e -> userOrganisationUtils.getOrganisationById(e)).orElse(httpRequestOriginUtil.getUserOrganisation(request));
        return iVehicleEolService.getDynoTestPassPercentages(organisation.getId());
    }

    @GetMapping("/connectivity")
    @ReadOnly
    public ConnectivityDataPercentageApiResponseDto getConnectivityDataPercentage(@RequestParam Optional<Long> orgId, HttpServletRequest request) {
        CustomOrganisation organisation = orgId.map(e -> userOrganisationUtils.getOrganisationById(e)).orElseGet(()-> httpRequestOriginUtil.getUserOrganisation(request));
        return iVehicleEolService.getConnectivityDataPercentage(organisation.getId());
    }

    @GetMapping("/supported-tests")
    @ReadOnly
    public List<TestFactor> getSupportedTests(){
        return iVehicleEolService.getTestFactor();
    }
}
