package com.nichesolv.evahanam.telemetryData.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.telemetryData.dto.ExportRequestDto;
import com.nichesolv.evahanam.telemetryData.dto.ExportDataMatrixDto;
import com.nichesolv.evahanam.telemetryData.dto.ExportRequestProjection;
import com.nichesolv.evahanam.telemetryData.dto.ExportStatusDto;
import com.nichesolv.evahanam.telemetryData.enums.*;
import com.nichesolv.evahanam.telemetryData.jpa.ExportRequest;
import com.nichesolv.evahanam.telemetryData.service.telemetryData.ExportService;
import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.evahanam.vehicle.exception.OrganisationException;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/***
 * Controller to export telemetry data as CSV,track status and some metadata around what dates are available for export
 */
@Slf4j
@RestController
@RequestMapping("/telemetry-data")
@SecurityRequirement(name = "Bearer Authentication")
public class ExportController {
    @Autowired
    ExportService exportService;

    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;

    @PostMapping("/export")
    public Long createExportRequest(@RequestBody @Validated ExportRequestDto csvRequest, HttpServletRequest request) throws OrganisationException {
        httpRequestOriginUtil.checkVehicleBelongToOrganisation(csvRequest.getImei(), null, request);
        return exportService.createExportRequest(csvRequest);
    }

    @GetMapping("/export/status")
    @ReadOnly
    public ExportStatusDto getExportRequestStatus(@RequestParam("requestId") Long id) {
        return exportService.getExportRequestStatus(id);
    }

    @GetMapping("/downloads")
    @ReadOnly
    public Page<ExportRequestProjection> getDownloadHistory(@RequestParam(required = false) String imei,
                                                            @RequestAttribute (value = "identifier") String identifier,
                                                            @RequestParam(value = "vIdVal",required = false) String idValue, @PageableDefault(size = 10, page = 0, direction = Sort.Direction.DESC) Pageable pageable) {
        return exportService.getDownloadHistory(identifier, pageable);
    }

    /**
     * The method returns a list of dates in a given month for which the csvTableName contains data in the specified fields.
     * @param exportDataMatrixDto
     * @return list of dates
     */
    @PostMapping("/data-matrix")
    public List<LocalDate> getDataMatrix(@RequestBody ExportDataMatrixDto exportDataMatrixDto){
        Set<LocalDate> dateList = new HashSet<>();

        for (int i = 0; i < exportDataMatrixDto.getExportDataTypes().size(); i++) {
                dateList.addAll(exportService.getDataMatrix(exportDataMatrixDto.getImei(), exportDataMatrixDto.getYearMonth(), exportDataMatrixDto.getExportDataTypes().get(i), exportDataMatrixDto.getRawFlag())
                        .stream()
                        .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
                        .collect(Collectors.toSet()));
        }
        return dateList.stream()
                .sorted()
                .collect(Collectors.toList());
    }
}