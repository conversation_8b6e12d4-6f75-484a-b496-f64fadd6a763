package com.nichesolv.evahanam.telemetryData.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.telemetryData.enums.ExportDataType;
import com.nichesolv.evahanam.telemetryData.service.vehicleData.VehicleDataService;
import com.nichesolv.evahanam.vehicle.dto.DataAvailabilityDto;
import com.nichesolv.evahanam.vehicle.enums.TimeFilter;
import com.nichesolv.evahanam.vehicleTests.v2.dto.FieldListDto;
import com.nichesolv.evahanam.vehicleTests.v2.dto.ResultDto;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Optional;

@RequestMapping("/telemetry-data")
@Slf4j
@RestController
@SecurityRequirement(name = "Bearer Authentication")
public class VehicleDataController {

    @Autowired
    VehicleDataService vehicleDataService;



    @GetMapping("/fields")
    @ReadOnly
    public List<String> getColumns(@RequestParam ExportDataType vehicleDataTable) {
        return vehicleDataService.getColumnName(vehicleDataTable);
    }

    @PostMapping("/field-values")
    @ReadOnly
    public ResultDto dataVehicle(@RequestParam(value = "vIdVal") String vehicleId,
                                 @RequestParam(required = false) Optional<Long> testId,
                                 @RequestParam(required = false) Optional<Long> startTime,
                                 @RequestParam(required = false) Optional<Long> endTime,
                                 @RequestParam ExportDataType dataTable,
                                 @RequestBody(required = false) FieldListDto requestDto,
                                 @Parameter(description = "Sampling frequency: valid values are '1m', '10m', or '1h'")
                                 @RequestParam(value = "samplingFrequency",required = false) String samplingFrequency,
                                 @RequestParam Boolean showErrors
    ) {
        log.debug(" inside the data controller ");
        return vehicleDataService.getData(vehicleId, testId, startTime, endTime, dataTable, requestDto,samplingFrequency, showErrors);
    }

    @GetMapping("/availability")
    @ReadOnly
    public DataAvailabilityDto getDataPercentage(@RequestParam Long from, @RequestParam Long to, @RequestParam TimeFilter timeFilter,
                                                 @RequestParam ExportDataType vehicleDataTable,
                                                 @RequestAttribute(value = "identifier") String identifier,
                                                 @RequestParam(value = "vIdVal") String idValue) {
        return vehicleDataService.getPercentageBetween(identifier, from, to, timeFilter, vehicleDataTable);
    }



}

