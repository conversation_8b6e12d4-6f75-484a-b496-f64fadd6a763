package com.nichesolv.evahanam.telemetryData.dto;

import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MotorDataDto extends TelemetryDto {
  @NotNull
  String imei;
  @NotNull
  Long timestamp;

  String coRelationId;
  DriveMode motorDrivingMode;
  Boolean motorRegeneration;
  Boolean motorReadySign;
  Boolean motorParkingSign;
  Boolean motorSideStand;
  Boolean motorBrake;
  Boolean motorCruise;
  Boolean motorReverse;
  Float motorThrottle;
  Float motorSpeed;
  Float motorDcVoltage;
  Float motorDcCurrent;
  Float motorTemperature;
  Float motorMcsTemperature;
}
