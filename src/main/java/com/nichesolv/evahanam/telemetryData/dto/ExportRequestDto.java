package com.nichesolv.evahanam.telemetryData.dto;


import com.nichesolv.evahanam.telemetryData.enums.ExportDataType;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.*;
import lombok.*;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ExportRequestDto {
    @NotNull
    @Enumerated(EnumType.STRING)
    List<ExportDataType> exportDataTypes;

    @NotNull
    List<List<String>> columnLists;

    @NotNull
    Long orgId;

    @NotBlank
    String imei;

    @NotNull
    Long startTime;

    @NotNull
    Long endTime;

    @Hidden
    Long reportId;

    @Hidden
    String query;

    @NotNull
    Boolean rawFlag = true;

    public void setQuery(String commonHeaders, String headers, int tableNumber) {
        String[] headerColumns = headers.split(",");
        String notNullCondition = String.join(" IS NOT NULL OR ", headerColumns);
        notNullCondition += " IS NOT NULL";
        this.query = String.format("SELECT %s FROM %s WHERE imei = '%s' AND timestamp BETWEEN '%s' AND '%s' AND (%s) ORDER BY timestamp", commonHeaders + ',' + headers, getTableName(exportDataTypes.get(tableNumber), rawFlag), imei, Instant.ofEpochMilli(startTime), Instant.ofEpochMilli(endTime), notNullCondition);
    }

    public static String getTableName(ExportDataType exportDataType, Boolean rawFlag) {
        return rawFlag ? exportDataType.getDataTableName() + "_raw" : exportDataType.getDataTableName();
    }
}
