package com.nichesolv.evahanam.telemetryData.dto;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImuDataDto {
    @NotNull
    String imei;
    @NotNull
    Long timestamp;
    String coRelationId;
    Boolean diMotion;
    Boolean diIgnition;
    Float accelXAxis;
    Float accelYAxis;
    Float accelZAxis;
    Float gyroXAxis;
    Float gyroYAxis;
    Float gyroZAxis;
    Float grvXAxis;
    Float grvYAxis;
    Float grvZAxis;
    Integer aiLeanAngle;
}
