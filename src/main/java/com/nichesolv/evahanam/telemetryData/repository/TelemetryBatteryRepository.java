package com.nichesolv.evahanam.telemetryData.repository;

import com.nichesolv.evahanam.telemetryData.dto.*;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleBatteryData;
import com.nichesolv.evahanam.vehicle.dto.BatteryDischargeCycleProjection;
import com.nichesolv.evahanam.vehicle.dto.BatteryTemperatureSensorData;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface TelemetryBatteryRepository extends AbstractTelemetryBatteryRepository<VehicleBatteryData> {


    @Query(value = "select soc from vehicle_battery_data where imei=?1 and timestamp between ?2 and ?3 and soc is not null order by timestamp desc limit 1", nativeQuery = true)
    Integer findByImeiAndEndTime(String imei, Instant start, Instant end);

    @Query(value = "select stack_id as stackId, round(cast(min(min_temperature) as decimal), 2) as minTemperature " +
            "            , round(cast(max(max_temperature) as decimal), 2) as maxTemperature " +
            "            , round(cast(avg(avg_temperature) as decimal), 2) as avgTemperature " +
            "            from view_battery_sensor_aggregate_data aggr " +
            "            where  aggr.mfr_org_id=?1 and aggr.bucket between ?2 and ?3 " +
            "            group by aggr.stack_id " +
            "            order by aggr.stack_id ", nativeQuery = true)
    List<BatteryTemperatureSensorData> getBatterySensorData(Long id, Instant from, Instant to);

    boolean existsByTelemetryIdxImeiAndTelemetryIdxTimestampGreaterThanEqual(String imei, Instant timestamp);

    Long countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(String imei, Instant start, Instant end);

    @Query(value = "SELECT max_temperature_max FROM battery_tempmax_agg_per_min WHERE imei=?1 AND timestamp BETWEEN ?2 AND ?3 AND avg_temperature_max > ?4  ORDER BY timestamp DESC FETCH FIRST 1 ROW ONLY", nativeQuery = true)
    Float getTop1TemperatureMaxByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenOrderByTelemetryIdxTimestampDesc(String imei, Instant start, Instant end, Float tempLimit);

    @Query(value = "SELECT COUNT(DISTINCT btmax.imei) FROM battery_tempmax_agg_per_min btmax JOIN vehicle_battery_thresholds vbt ON btmax.imei = vbt.imei WHERE btmax.timestamp BETWEEN ?1 AND ?2 AND btmax.avg_temperature_max >= vbt.tempmaxalertlow", nativeQuery = true)
    int countVehiclesWithAlerts(Instant startTime, Instant endTime);

    @Query(value = "SELECT btmax.imei AS imei, AVG(btmax.avg_temperature_max) AS avgTempMax, MAX(btmax.max_temperature_max) AS maxTempMax, COUNT(CASE WHEN btmax.avg_temperature_max >= vbt.tempmaxalertlow THEN 1 END) AS duration, CASE WHEN AVG(btmax.avg_temperature_max) >= MAX(vbt.tempmaxalerthigh) THEN 'HIGH' WHEN AVG(btmax.avg_temperature_max) >= MAX(vbt.tempmaxalertmedium) THEN 'MEDIUM' WHEN AVG(btmax.avg_temperature_max) >= MAX(vbt.tempmaxalertlow) THEN 'LOW' END AS status FROM battery_tempmax_agg_per_min btmax JOIN vehicle_battery_thresholds vbt ON btmax.imei = vbt.imei WHERE btmax.timestamp BETWEEN ?1 AND ?2 AND btmax.avg_temperature_max >= vbt.tempmaxalertlow GROUP BY btmax.imei", nativeQuery = true)
    List<VehicleAlertsResponseDto> getVehiclesWithAlerts(Instant startTime, Instant endTime, Pageable pageable);

    @Query(value = "select " +
            "round(coalesce(cast(max(max_temperature_max) as decimal),0),2) as maxBatteryTemp, " +
            "round(coalesce(cast(min(min_temperature_min) as decimal),0),2) as minBatteryTemp " +
            "from view_vehicle_battery_aggregate_data vbd " +
            "where vbd.mfr_org_id=?3 and vbd.bucket between ?1 and ?2 ", nativeQuery = true)
    VehicleBatteryProjection findMinMaxByOrgIdAndTimeBetween(Instant from, Instant to, Long orgId);

    @Query(value = "select count(distinct(aggr.imei)) from view_vehicle_battery_aggregate_data aggr " +
            "            join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "            join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "            where aggr.bucket between ?3 and ?4 " +
            "            and aggr.mfr_org_id= ?2 " +
            "            and attr.attr_name= ?1 " +
            "            and aggr.min_temperature_min < CAST (attr.attr_value as INT) ", nativeQuery = true)
    Float getCountOfBatteryTempBelowAverage(String attributeName, Long organisationId, Instant from, Instant to);

    @Query(value = "select count(distinct(aggr.imei)) from view_vehicle_battery_aggregate_data aggr " +
            "            join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "            join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "            where aggr.bucket between ?3 and ?4 " +
            "            and aggr.mfr_org_id= ?2 " +
            "            and attr.attr_name= ?1 " +
            "            and aggr.max_temperature_max > CAST (attr.attr_value as INT) ", nativeQuery = true)
    Float getCountOfBatteryTempAboveAverage(String attributeName, Long organisationId, Instant from, Instant to);


    @Query(value = "select * from vehicle_battery_data where imei=?1 and timestamp between ?2 and ?3 order by timestamp desc limit 1 ", nativeQuery = true)
    Optional<VehicleBatteryData> findFirstByTelemetryIdxImeiAndTelemetryIdxTimestampLessThanOrEqualsOrderByTelemetryIdxTimestampDesc(String imei, Instant start, Instant end);

    @Query(value = "select max(soc) from vehicle_battery_data where imei =?1 " +
            "and timestamp between ?2 and ?3 ", nativeQuery = true)
    Float getAverageSocByImeiAndTimestampBetween(String imei, Instant startTime, Instant endTime);

    Optional<VehicleBatteryData> findFirstByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenAndSocIsNotNullOrderByTelemetryIdxTimestampDesc(String imei, Instant start, Instant end);



    @Query(value = "SELECT imei, vehicle_id AS vehicleId, time_bucket_1min AS timeBucketMin, max_battery_volt AS maxBatteryVolt, min_battery_volt AS minBatteryVolt, max_current AS maxCurrent, min_current AS minCurrent, max_temperature_max AS maxTemperatureMax, min_temperature_min AS minTemperatureMin " +
            "FROM view_vehicle_battery_data_aggregate_per_1min " +
            "WHERE imei = ?1 AND time_bucket_1min BETWEEN ?2 AND ?3 " +
            "ORDER BY time_bucket_1min DESC " +
            "LIMIT 1", nativeQuery = true)
    VehicleBatteryDataProjection findBatteryAggregateFields(String imei, Instant timestamp1, Instant timestamp2);


    @Query(value = "SELECT battery_volt_above_nominal_info as batteryVoltAboveNominalInfo , battery_volt_above_nominal_warning as batteryVoltAboveNominalWarning ,battery_volt_above_nominal_critical as batteryVoltAboveNominalCritical ,battery_volt_below_nominal_info as batteryVoltBelowNominalInfo, " +
            "battery_volt_below_nominal_warning as batteryVoltBelowNominalWarning ,battery_volt_below_nominal_critical as batteryVoltBelowNominalCritical , current_above_nominal_info as currentAboveNominalInfo , " +
            "current_above_nominal_warning as currentAboveNominalWarning , current_above_nominal_critical as currentAboveNominalCritical , current_below_nominal_info currentBelowNominalInfo , " +
            "current_below_nominal_warning as currentBelowNominalWarning , current_below_nominal_critical as currentBelowNominalCritical, temperature_below_nominal_info as temperatureBelowNominalInfo , temperature_below_nominal_warning as temperatureBelowNominalWarning , temperature_below_nominal_critical as temperatureBelowNominalCritical," +
            "temperature_above_nominal_info as temperatureAboveNominalInfo , temperature_above_nominal_warning as temperatureAboveNominalWarning , temperature_above_nominal_critical as temperatureAboveNominalCritical FROM vehicle_battery_data_thresholds WHERE imei = ?1 ", nativeQuery = true)
    VehicleBatteryThresholdsProjection getThreshold(String imei);


    @Query(value = "SELECT motor_dc_current_above_nominal_info as motorDcCurrentAboveNominalInfo ,motor_dc_current_above_nominal_warning as motorDcCurrentAboveNominalWarning ,motor_dc_current_above_nominal_critical as motorDcCurrentAboveNominalCritical , " +
            "motor_dc_voltage_below_nominal_info as motorDcVoltageBelowNominalInfo , motor_dc_voltage_below_nominal_warning as motorDcVoltageBelowNominalWarning  , motor_dc_voltage_below_nominal_critical as  motorDcVoltageBelowNominalCritical , " +
            "motor_dc_voltage_above_nominal_info as motorDcVoltageAboveNominalInfo , motor_dc_voltage_above_nominal_warning as motorDcVoltageAboveNominalWarning , motor_dc_voltage_above_nominal_critical as motorDcVoltageAboveNominalCritical ," +
            "mcs_temperature_below_nominal_info as mcsTemperatureBelowNominalInfo , mcs_temperature_below_nominal_warning as mcsTemperatureBelowNominalWarning , mcs_temperature_below_nominal_critical as mcsTemperatureBelowNominalCritical , " +
            "mcs_temperature_above_nominal_info as mcsTemperatureAboveNominalInfo , mcs_temperature_above_nominal_warning as mcsTemperatureAboveNominalWarning , mcs_temperature_above_nominal_critical as mcsTemperatureAboveNominalCritical FROM motor_thresholds WHERE imei = ?1 ", nativeQuery = true)
    MotorThresholdsProjection getMotorThreshold(String imei);


    @Query(value = "select soc from vehicle_battery_data where imei=?1 and timestamp between ?2 and ?3 and soc is not null order by timestamp desc limit 1", nativeQuery = true)
    Float findLatestSoc(String imei, Instant start, Instant end);


    @Query(value = "SELECT ( " +
            " COUNT(battery_volt) + COUNT(cell_volt_max) + COUNT(cell_volt_min) + " +
            " COUNT(chg_cycle_count) + COUNT(current) + COUNT(dsg_cycle_count) + " +
            " COUNT(soc) + COUNT(soh) + COUNT(temperature_max) + " +
            " COUNT(temperature_min) + COUNT(remaining_capacity) + COUNT(mosfet_temperature) " +
            ") AS totalBatteryCount " +
            " FROM vehicle_battery_data " +
            " WHERE imei = ?1 AND timestamp >= ?2 AND timestamp <= ?3 ", nativeQuery = true)
    Integer getTotalBatteryFieldCount(String imei, Instant startTime, Instant endTime);


    @Query(value = "SELECT timestamp,imei, remaining_capacity AS remainingCapacity, soc\n" +
            "FROM vehicle_battery_data\n" +
            "WHERE timestamp between ?4 and now() and imei = ?1\n" +
            "AND created_on between ?2 AND ?3 and soc is not null and remaining_capacity is not null order by timestamp desc\n",nativeQuery = true)
    List<BatteryDataProjection> findSocAndRemainingCapacityByImeiAndCreatedBetween(String imei, Instant start, Instant end,Instant bufferTime);


    @Query(value = "SELECT timestamp,imei, remaining_capacity AS remainingCapacity, soc\n" +
            "FROM vehicle_battery_data\n" +
            "WHERE timestamp between ?3 and now() and imei = ?2\n" +
            "AND timestamp < ?1 and soc is not null and remaining_capacity is not null order by timestamp desc limit 1",nativeQuery = true)
    BatteryDataProjection findBatteryDataById(Instant timestamp,String imei,Instant bufferTime);


    @Query(value = "SELECT dsg_cycle_count AS dsgCycleCount " +
            "FROM vehicle_battery_data " +
            "WHERE imei = ?1 " +
            "ORDER BY timestamp DESC " +
            "LIMIT 1",
            nativeQuery = true)
    BatteryDischargeCycleProjection findLatestChgAndDsgCycleCountForImei(String imei);

}
