package com.nichesolv.evahanam.telemetryData.repository;

import com.nichesolv.evahanam.telemetryData.jpa.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.time.Instant;
import java.util.Optional;

@NoRepositoryBean
public interface AbstractMotorDataRepository<T extends AbstractVehicleMotorData> extends JpaRepository<T, MotorIdx> {

    Optional<T> findFirstByMotorIdxImeiAndMotorIdxTimestampBetweenOrderByMotorIdxTimestampDesc(
            String imei, Instant start, Instant end);

    Optional<T> findByMotorIdxMotorIdAndMotorIdxImeiAndMotorIdxTimestamp(
            Long motorId, String imei, Instant timestamp);

    Optional<T> findById(MotorIdx motorDataIdx);
}

