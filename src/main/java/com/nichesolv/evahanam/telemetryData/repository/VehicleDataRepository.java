package com.nichesolv.evahanam.telemetryData.repository;

import com.nichesolv.evahanam.common.dto.TimestampProjection;
import com.nichesolv.evahanam.telemetryData.dto.*;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleTelemetryData;
import com.nichesolv.evahanam.vehicle.dto.BatteryStackSparklingData;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface VehicleDataRepository extends AbstractVehicleDataRepository<VehicleTelemetryData> {


    @Query(value = "SELECT di_motion FROM vehicle_telemetry_data WHERE imei=?1 and di_motion is not null and timestamp between ?2 and ?3 order by timestamp desc limit 1 ", nativeQuery = true)
    Boolean getLatestDiMotionData(String imei, Instant startTime, Instant endTime);

    List<AiVinDto> findFirst100ByTelemetryIdxImeiAndTelemetryIdxTimestampLessThanOrderByTelemetryIdxTimestampDesc(String imei, Instant currentTime);

    List<AiVinDto> findFirst100ByTelemetryIdxImeiOrderByTelemetryIdxTimestampDesc(String imei);

    boolean existsByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenAndAiVoltageInputIsNotNull(String imei, Instant start, Instant end);


    boolean existsByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(String imei, Instant start, Instant end);

    boolean existsByTelemetryIdxImeiAndTelemetryIdxTimestampGreaterThanEqual(String imei, Instant timestamp);

    @Query(value = "select avg(extract(SECONDS from (packet_received_on - timestamp))) from vehicle_telemetry_data " +
            "where imei=?1 and timestamp between ?2 and ?3", nativeQuery = true)
    Float getAvgLagByImeiAndTimestampBetween(String imei, Instant startTime,
                                             Instant endTime);

    Long countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(String imei, Instant start, Instant end);


    Long countByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenAndAiVoltageInputIsNotNull(String vehicleId, Instant start, Instant end);


    @Modifying(flushAutomatically = true)
    @Query(value = "update vehicle_telemetry_data set do_usr1=?1,do_usr2=?2 where timestamp=?3 and imei=?4", nativeQuery = true)
    int updateDigitalIo(Boolean usr1, Boolean usr2, Instant timestamp, String imei);


    @Modifying(flushAutomatically = true)
    @Query(value = "update vehicle_telemetry_data set ai_voltage_input=?1," +
            "ai_temperature=?2,ai_system_voltage=?3,ai_vbuck=?4,ai_vusr1=?5," +
            "ai_vusr2=?6,ai_lean_angle=?7 where timestamp=?8 and imei=?9", nativeQuery = true)

    int updateAnalogIo(Float voltageInput, Float temperature, Float systemVoltage,
                       Float vbuck, Integer vusr1, Integer vusr2, Integer leanAngle,
                       Instant timestamp, String imei);

    @Modifying(flushAutomatically = true)
    @Query(value = "update vehicle_telemetry_data set di_usr1=?1," +
            "di_usr2=?2,di_motion=?3,di_tamper=?4,di_main_power=?5,di_ignition=?6 where timestamp=?7 and imei=?8", nativeQuery = true)
    int updateDigitalInput(Boolean usr1, Boolean usr2, Boolean motion, Boolean tamper,
                           Boolean mainPower, Boolean ignition, Instant timestamp, String imei);

    @Query(value = "select distinct(imei) from vehicle_telemetry_data where timestamp between ?1 and ?2", nativeQuery = true)
    List<String> findDistinctImeiListBetweenTimestamps(Instant startTime, Instant endTime);

    @Query(value = "select MAX(timestamp) from vehicle_telemetry_data where imei = ?1", nativeQuery = true)
    Instant getLatestTelemetryTimestamp(String imei);


    @Query(value = "SELECT temperature, " +
            "time_bucket(cast(:interval as interval), bucket) as timestamp " +
            "FROM view_battery_sensor bst, vehicle v " +
            "WHERE bst.bucket BETWEEN :startTime and :endTime " +
            "AND bst.imei = v.imei and v.mfr_org_id=:orgId and stack_id=:stackId " +
            "GROUP BY stack_id, temperature, timestamp " +
            " order by timestamp desc limit 100", nativeQuery = true)
    List<Float> getBatteryStackDta(@Param("interval") String interval,
                                   @Param("startTime") Instant startTime,
                                   @Param("endTime") Instant endTime,
                                   @Param("orgId") Long orgId,
                                   @Param("stackId") Long stackId);


    @Query(value = "select distinct on (stack_id) stack_id from battery_stack bst , vehicle v where timestamp between ?1 and ?2 and v.imei=bst.imei and v.mfr_org_id=?3", nativeQuery = true)
    List<Long> findDistinctSensorIds(Instant from, Instant to, Long orgId);

    @Query(value = "select count(distinct(aggr.imei)) as temperature,aggr.stack_id as stackId from view_battery_sensor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?3 and ?4 " +
            "and aggr.mfr_org_id=?2 " +
            "and attr.attr_name=?1 " +
            "and aggr.min_temperature < CAST (attr.attr_value as INT) " +
            "group by aggr.stack_id " +
            "order by aggr.stack_id ", nativeQuery = true)
    List<BatteryStackSparklingData> getCountOfSensorBelowAverage(String attributeName, Long organisationId, Instant from, Instant to);

    @Query(value = "select count(distinct(aggr.imei)) as temperature,aggr.stack_id as stackId from view_battery_sensor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?3 and ?4 " +
            "and aggr.mfr_org_id=?2 " +
            "and attr.attr_name=?1 " +
            "and aggr.max_temperature > CAST (attr.attr_value as INT) " +
            "group by aggr.stack_id " +
            "order by aggr.stack_id ", nativeQuery = true)
    List<BatteryStackSparklingData> getCountOfSensorAboveAverage(String attributeName, Long organisationId, Instant from, Instant to);

    TimestampProjection findFirstByTelemetryIdxTimestampLessThanOrderByTelemetryIdxTimestampDesc(Instant currentTimestamp);

    /*
   xStill, yStill, zStill refers to median of grv when vehicle is stationery, similarly for running.
    */
    @Query(value =
            "WITH most_recent_still AS (" +
                    "    SELECT imei, grv_x_still AS xStill, grv_y_still AS yStill, grv_z_still AS zStill, bucket AS time " +
                    "    FROM median_grv_still_1day_aggregate " +
                    "    WHERE imei = ?1 " +
                    "    ORDER BY bucket DESC " +
                    "    LIMIT 1 " +
                    ")," +
                    "most_recent_running AS ( " +
                    "    SELECT imei, grv_x_running AS xRunning, grv_y_running AS yRunning, grv_z_running AS zRunning, bucket AS time " +
                    "    FROM median_grv_running_1day_aggregate " +
                    "    WHERE imei = ?1 " +
                    "    ORDER BY bucket DESC " +
                    "    LIMIT 1 " +
                    ")" +
                    "SELECT " +
                    "    s.imei, " +
                    "    s.xStill, " +
                    "    s.yStill, " +
                    "    s.zStill, " +
                    "    r.xRunning," +
                    "    r.yRunning, " +
                    "    r.zRunning " +
                    "FROM " +
                    "    most_recent_still s " +
                    "FULL OUTER JOIN " +
                    "    most_recent_running r " +
                    "ON " +
                    "    s.imei = r.imei; ", nativeQuery = true)
    Optional<GrvDataDto> getLatestGrvAggregate(String imei);

    /*
   xStill, yStill, zStill refers to median of grv when vehicle is stationery, similarly for running.
    */
    @Query(value =
            "WITH most_recent_still AS (" +
                    "    SELECT imei, grv_x_still AS xStill, grv_y_still AS yStill, grv_z_still AS zStill, bucket AS time " +
                    "    FROM median_grv_still_1day_aggregate_raw " +
                    "    WHERE imei = ?1 " +
                    "    ORDER BY bucket DESC " +
                    "    LIMIT 1 " +
                    ")," +
                    "most_recent_running AS ( " +
                    "    SELECT imei, grv_x_running AS xRunning, grv_y_running AS yRunning, grv_z_running AS zRunning, bucket AS time " +
                    "    FROM median_grv_running_1day_aggregate_raw " +
                    "    WHERE imei = ?1 " +
                    "    ORDER BY bucket DESC " +
                    "    LIMIT 1 " +
                    ")" +
                    "SELECT " +
                    "    s.imei, " +
                    "    s.xStill, " +
                    "    s.yStill, " +
                    "    s.zStill, " +
                    "    r.xRunning," +
                    "    r.yRunning, " +
                    "    r.zRunning " +
                    "FROM " +
                    "    most_recent_still s " +
                    "FULL OUTER JOIN " +
                    "    most_recent_running r " +
                    "ON " +
                    "    s.imei = r.imei; ", nativeQuery = true)
    Optional<GrvDataDto> getLatestGrvRawAggregate(String imei);

    @Query(value = "SELECT ( " +

            "  COUNT(ai_system_voltage) + COUNT(ai_temperature) + " +
            " COUNT(ai_vbuck) + COUNT(ai_voltage_input) + COUNT(ai_vusr1) + COUNT(ai_vusr2) + " +
            " COUNT(di_ignition) + COUNT(di_main_power) + COUNT(di_motion) + " +
            " COUNT(di_tamper) + COUNT(di_usr1) + COUNT(di_usr2) + COUNT(do_usr1) + " +
            " COUNT(do_usr2) " +
            ") AS totalCount " +
            " FROM vehicle_telemetry_data " +
            " WHERE imei = ?1 AND timestamp >= ?2 AND timestamp <= ?3 ", nativeQuery = true)
    Integer getTotalTelemetryFieldCount(String imei, Instant startTime, Instant endTime);


    @Query(value = "SELECT timestamp FROM vehicle_telemetry_data ORDER BY timestamp ASC LIMIT 1", nativeQuery = true)
    Instant findEarliestTimestamp();
}