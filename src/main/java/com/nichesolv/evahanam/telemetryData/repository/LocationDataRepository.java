package com.nichesolv.evahanam.telemetryData.repository;

import com.nichesolv.evahanam.telemetryData.dto.LocationDataProjection;
import com.nichesolv.evahanam.telemetryData.dto.RunningMetricsDataProjection;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleLocationData;
import com.nichesolv.evahanam.trip.dto.TripLocationProjection;
import org.locationtech.jts.geom.Point;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface LocationDataRepository extends AbstractVehicleLocationDataRepository<VehicleLocationData> {

    boolean existsByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(String imei, Instant start, Instant end);

    boolean existsByTelemetryIdxImeiAndTelemetryIdxTimestampGreaterThanEqual(String imei, Instant timestamp);

    Long countByTelemetryIdxImeiAndTelemetryIdxTimestampBetween(String imei, Instant start, Instant end);

    Optional<VehicleLocationData> findTop1SpeedByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenOrderByTelemetryIdxTimestampDesc(String imei, Instant start, Instant end);

    VehicleLocationData findFirstByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenOrderByTelemetryIdxTimestampDesc(String imei, Instant start, Instant end);


    @Query(value =
            "SELECT timestamp, imei, latitude, longitude, speed\n" +
                    "FROM vehicle_location_data\n" +
                    "WHERE timestamp between ?4 and now() and imei = ?1\n" +
                    "AND latitude IS NOT NULL AND longitude IS NOT NULL\n" +
                    "AND created_on between ?2 AND ?3\n", nativeQuery = true)
    List<LocationDataProjection> findLocationByImeiAndCreatedOnBetween(String imei, Instant start, Instant end,Instant bufferTime);

    @Query(value = "SELECT t.imei " +
            "FROM ( " +
            "FROM ( " +
            "SELECT tel.imei, MAX(tel.timestamp) AS timestamp FROM vehicle_location_data tel INNER JOIN vehicle v ON v.imei = tel.imei WHERE v.owner_org_id = ?1 GROUP BY tel.imei " +
            ") AS t " +
            "INNER JOIN vehicle_location_data AS l ON l.imei = t.imei AND l.timestamp = t.timestamp " +
            "WHERE l.latitude BETWEEN ?2 AND ?3 AND l.longitude BETWEEN ?4 AND ?5 ", nativeQuery = true)
    List<String> getAvailableImeiByLatitudeBetweenAndLongitudeBetween(Long orgId, Float startLat, Float endLat, Float startLong, Float endLong);


    @Query(value = "select latitude, longitude from  vehicle_location_data where timestamp>=?1 and imei=?2 " +
            "and latitude is not null and longitude is not null order by timestamp limit 1", nativeQuery = true)
    TripLocationProjection getStartLocationDataByTimeStampAndImei(Instant startTime, String imei);

    @Query(value = "select latitude, longitude from  vehicle_location_data where timestamp<=?1 and imei=?2 " +
            "and latitude is not null and longitude is not null order by timestamp desc limit 1", nativeQuery = true)
    TripLocationProjection getEndLocationDataByTimeStampAndImei(Instant startTime, String imei);


    @Query(value = "WITH uncompressed_chunks AS (\n" +
            "    SELECT concat(concat(chunk_schema,'.'),chunk_name) AS chunk_name,hypertable_name as hypertable_name\n" +
            "    FROM timescaledb_information.chunks \n" +

            "    WHERE hypertable_name in ( 'vehicle_motor_data', 'vehicle_location_data', 'vehicle_battery_data')\n" +
            "    AND is_compressed = FALSE\n" +
            "    and range_start > now() - interval '2 week'\n" +
            "    and range_end < now() + interval '1 week'\n" +
            ")\n" +
            "SELECT t.timestamp, t.di_ignition AS diIgnition, m.motor_driving_mode AS motorDrivingMode, \n" +
            "       l.latitude, l.longitude, l.speed\n" +
            "       ,b.soc AS soc, b.remaining_capacity AS remainingCapacity\n" +
            "       ,(prev.remaining_capacity - b.remaining_capacity) AS calRemainingCapacityDiff, \n" +
            "       (prev.soc - b.soc) AS discharge\n" +
            "FROM (\n" +
            "    SELECT timestamp, di_ignition, motor_driving_mode, imei\n" +
            "    FROM vehicle_motor_data\n" +
            "    WHERE timestamp between now()\\:\\:date-interval'5 days' and now() and imei = ?1 \n" +
            "     AND di_ignition = TRUE \n" +
            "      AND di_motion = TRUE\n" +
            "      AND created_on between ?2 AND ?3\n" +
            "      AND tableoid\\:\\:regclass\\:\\:text IN (select chunk_name from uncompressed_chunks where hypertable_name = 'vehicle_motor_data') " +
            "      \n" +
            ") AS t\n" +
            "JOIN (\n" +
            "    SELECT timestamp, motor_driving_mode, imei\n" +
            "    FROM vehicle_motor_data\n" +
            "    WHERE imei = ?1\n" +
            "    AND created_on between ?2 AND ?3\n" +
            "    AND tableoid\\:\\:regclass\\:\\:text IN (select chunk_name from uncompressed_chunks where hypertable_name = 'vehicle_motor_data')" +
            ") AS m\n" +
            "ON m.imei = t.imei AND m.timestamp = t.timestamp\n" +
            "JOIN (\n" +
            "    SELECT timestamp, latitude, longitude, speed\n" +
            "    FROM vehicle_location_data\n" +
            "    WHERE timestamp between now()\\:\\:date-interval'5 days' and now() and imei = ?1\n" +
            "      AND latitude IS NOT NULL AND longitude IS NOT NULL\n" +
            "      AND created_on between ?2 AND ?3\n" +
            "     AND tableoid\\:\\:regclass\\:\\:text IN (select chunk_name from uncompressed_chunks where hypertable_name = 'vehicle_location_data')" +
            ") AS l\n" +
            "ON l.timestamp = t.timestamp\n" +
            "LEFT JOIN (\n" +
            "    SELECT timestamp, remaining_capacity, soc\n" +
            "    FROM vehicle_battery_data\n" +
            "    WHERE timestamp between now()\\:\\:date-interval'5 days' and now() and imei = ?1\n" +
            "      AND created_on between ?2 AND ?3\n" +
            "      AND tableoid\\:\\:regclass\\:\\:text IN (select chunk_name from uncompressed_chunks   where hypertable_name = 'vehicle_battery_data') " +
            ") AS b\n" +
            "ON b.timestamp = t.timestamp \n" +
            "LEFT JOIN LATERAL (  -- Use LATERAL JOIN for the previous value\n" +
            "    SELECT remaining_capacity, soc, timestamp\n" +
            "    FROM vehicle_battery_data\n" +
            "    WHERE timestamp between now()\\:\\:date-interval'5 days' and now() and imei = ?1 AND timestamp = t.timestamp-CAST(?4 as INTERVAL) --Previous timestamp\n" +
            "    ORDER BY timestamp DESC\n" +
            "    LIMIT 1\n" +
            ") AS prev ON TRUE\n" +
            "ORDER BY t.timestamp ASC", nativeQuery = true)
    List<RunningMetricsDataProjection> findByTelemetryIdxImeiAndTimestampBetweenOrderByTelemetryIdxTimestampAsc(String imei, Instant start, Instant end, String chunkFrequency);


    @Query(value = """
            SELECT COUNT(*) > 0 AS data_exists
            FROM (
                SELECT bucket
                FROM vehicle_telemetry_first_aggregate_1m
                WHERE imei = ?1
                  AND mfr_org_id = ?4
                  AND bucket BETWEEN ?2 AND ?3
                  AND ai_temperature IS NOT NULL
            ) temp
            JOIN (
                SELECT bucket
                FROM vehicle_motor_first_aggregate_1m
                WHERE imei = ?1
                  AND mfr_org_id = ?4
                  AND bucket BETWEEN ?2 AND ?3
                  AND motor_temperature IS NOT NULL
                  AND motor_mcs_temperature IS NOT NULL
            ) motor ON temp.bucket = motor.bucket
            JOIN (
                SELECT bucket
                FROM vehicle_location_first_aggregate_1m
                WHERE imei = ?1
                  AND mfr_org_id = ?4
                  AND bucket BETWEEN ?2 AND ?3
            ) loc ON temp.bucket = loc.bucket
            LIMIT 1
            """, nativeQuery = true)
    boolean validateTemperaturesQuery(String imei, Instant start, Instant end, Long mfrOrgId);


    @Query(value = """
            SELECT COUNT(*) > 0 AS data_exists
            FROM vehicle_motor_first_aggregate_1m vm
            JOIN vehicle_location_first_aggregate_1m vl
              ON vm.imei = vl.imei AND vm.bucket = vl.bucket
            WHERE vm.imei = ?1
              AND vm.mfr_org_id = ?4
              AND vm.bucket BETWEEN ?2 AND ?3
              AND vm.motor_driving_mode IS NOT NULL
            LIMIT 1
            """, nativeQuery = true)
    boolean validateGeoDriveModeQuery(String imei, Instant start, Instant end, Long mfrOrgId);



    @Query(value = """
            SELECT COUNT(*) > 0 AS data_exists
            FROM vehicle_battery_first_aggregate_1m vb,
                 vehicle_location_first_aggregate_1m vl
            WHERE vb.imei = ?1
              AND vb.mfr_org_id = ?4
              AND vb.temperature_max IS NOT NULL
              AND vb.bucket BETWEEN ?2 AND ?3
              AND vb.imei = vl.imei
              AND vb.bucket = vl.bucket
            LIMIT 1
            """, nativeQuery = true)
    boolean validateBatteryTemperatureQuery(String imei, Instant start, Instant end, Long mfrOrgId);



    @Query(value = """
            SELECT COUNT(*) > 0 AS data_exists
            FROM vehicle_battery_first_aggregate_1m vb,
                 vehicle_location_first_aggregate_1m vl
            WHERE vb.imei = ?1
              AND vb.mfr_org_id = ?4
              AND vb.soc IS NOT NULL
              AND vb.bucket BETWEEN ?2 AND ?3
              AND vb.imei = vl.imei
              AND vb.bucket = vl.bucket
            LIMIT 1
            """, nativeQuery = true)
    boolean validateBatterySocQuery(String imei, Instant start, Instant end, Long mfrOrgId);


    @Query(value = """
            SELECT COUNT(*) > 0 AS data_exists
            FROM vehicle_location_first_aggregate_1m
            WHERE imei = ?1
              AND mfr_org_id = ?4
              AND bucket BETWEEN ?2 AND ?3
              AND speed IS NOT NULL
              AND altitude IS NOT NULL
            LIMIT 1
            """, nativeQuery = true)
    boolean validateLocSpeedAndAltitude(String imei, Instant start, Instant end, Long mfrOrgId);



    @Query(value = "SELECT ( " +
            " COUNT(altitude) + COUNT(brg) + COUNT(hdop) + " +
            " COUNT(latitude) + COUNT(longitude) + COUNT(pdop) + " +
            " COUNT(speed) + COUNT(track_sats) + COUNT(vdop) + " +
            " COUNT(view_sats) " +
            ") AS totalLocationCount " +
            " FROM vehicle_location_data " +
            " WHERE imei = ?1 AND timestamp >= ?2 AND timestamp <= ?3 ", nativeQuery = true)
    Integer getTotalLocationFieldCount(String imei, Instant startTime, Instant endTime);

    @Query(value = """
                SELECT *
                FROM vehicle_location_data
                WHERE imei = ?1
                ORDER BY "timestamp" DESC
                LIMIT 1
            """, nativeQuery = true)
    VehicleLocationData findLatestLocation(String imei);

    @Query(value = "select COALESCE(sum(gps_distance)/1000.0,0.0) from vehicle_location_data where imei= ?3 and " +
            "timestamp between ?1 and ?2", nativeQuery = true)
    Float getTotalDistanceTravelledBetweenTimeAndImei(Instant from, Instant to, String imei);

}
