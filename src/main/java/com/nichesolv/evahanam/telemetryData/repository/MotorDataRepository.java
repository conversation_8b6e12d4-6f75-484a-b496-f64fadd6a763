package com.nichesolv.evahanam.telemetryData.repository;

import com.nichesolv.evahanam.telemetryData.dto.*;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleMotorData;
import com.nichesolv.evahanam.vehicle.dto.MotorResultProjection;
import com.nichesolv.evahanam.vehicle.dto.SparklingData;
import com.nichesolv.evahanam.vehicle.dto.metadata.VehicleMinMaxMotorData;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface MotorDataRepository extends AbstractMotorDataRepository<VehicleMotorData> {

    @Query(value = "select motor_dc_current as motorDcCurrent from vehicle_motor_data where imei=?1 and motor_dc_current is not null and timestamp between ?2 and ?3 order by timestamp asc", nativeQuery = true)
    List<MotorDcCurrentDto> getDcMotorCurrentByImeiBetweenTimestamps(String imei, Instant start, Instant end);

    Long countByMotorIdxImeiAndMotorIdxTimestampBetween(String imei, Instant start,

                                                        Instant end);

    boolean existsByMotorIdxImeiAndMotorIdxTimestampBetweenAndMotorSpeedIsNotNull(String imei, Instant start, Instant end);

    boolean existsByMotorIdxImeiAndMotorIdxTimestampBetween(String imei, Instant start, Instant end);

    boolean existsByMotorIdxImeiAndMotorIdxTimestampGreaterThanEqual(String imei, Instant timestamp);


    @Query(value = "select COALESCE(max(motor_dc_current),-1.0) as max," +
            "COALESCE(min(motor_dc_current),-1.0) as min," +
            "COALESCE(avg(motor_dc_current),-1.0) as avg " +
            "from vehicle_motor_data t where t.timestamp>=?2 and t.timestamp <?3 " +
            "and t.imei=?1 and t.motor_driving_mode != 'NULL_DRIVE_SELECTION' and t.motor_dc_current is not null", nativeQuery = true)
    MeasuredStats getCurrentStatsByImeiAndBetweenTimestamp(String imei, Instant start, Instant end);

    @Query(value = "select COALESCE(max(motor_dc_voltage),-1.0) as max," +
            "COALESCE(min(motor_dc_voltage),-1.0) as min," +
            "COALESCE(avg(motor_dc_voltage),-1.0) as avg " +
            "from vehicle_motor_data t where t.timestamp>=?2 and t.timestamp <?3 and t.imei=?1 " +
            "and t.motor_driving_mode != 'NULL_DRIVE_SELECTION' and t.motor_dc_voltage is not null", nativeQuery = true)
    MeasuredStats getVoltageStatsByImeiAndBetweenTimestamp(String imei, Instant start, Instant end);

    @Query(value = "SELECT imei,vehicle_id as vehicleId, time_bucket_1min as timeBucketMin, max_motor_dc_current as maxMotorDcCurrent, min_motor_dc_current as minMotorDcCurrent, max_motor_dc_voltage as maxMotorDcVoltage, min_motor_dc_voltage as minMotorDcVoltage, max_motor_mcs_temperature as maxMotorMcsTemperature, min_motor_mcs_temperature as minMotorMcsTemperature " +
            "FROM view_motor_data_aggregate_per_1min " +
            "WHERE imei = ?1 AND time_bucket_1min BETWEEN ?2 AND ?3 " +
            "ORDER BY time_bucket_1min desc " +
            "LIMIT 1", nativeQuery = true)
    MotorDataProjection findMotorAggregateFields(String imei, Instant timestamp1, Instant timestamp2);

    @Query(value = "select avg(extract(SECONDS from (packet_received_on - timestamp))) from vehicle_motor_data " +
            "where imei=?1 and timestamp between ?2 and ?3", nativeQuery = true)
    Float getAvgLagByImeiAndTimestampBetween(String imei, Instant startTime,
                                             Instant endTime);

    @Query(value = "select min(t.timestamp) as min,max(t.timestamp) as max," +
            "t.motor_driving_mode as driveMode,count(*) as cnt from vehicle_motor_data t " +
            "where t.timestamp >=?1 and t.timestamp < ?2 and t.imei=?3 " +
            "and t.motor_driving_mode != 'NULL_DRIVE_SELECTION' and t.motor_reverse = false " +
            "group by t.motor_driving_mode " +
            "union " +
            "select min(t.timestamp) as min,max(t.timestamp) as max," +
            "'REVERSE' as driveMode,count(*) as cnt from vehicle_motor_data t " +
            "where t.timestamp >=?1 and t.timestamp < ?2 and t.imei=?3 " +
            "and t.motor_reverse = true", nativeQuery = true)
    List<MotorStatusDriveModeMinMaxTimeDto> findMinAndMaxTimestampByDriveModeBetweenTimestampAndImei(Instant start, Instant end, String imei);


    @Query(value = "select COALESCE(max(motor_speed/11),0.0) as speed from vehicle_motor_data t " +
            "where timestamp>=?2 and timestamp <?3 and imei=?1", nativeQuery = true)
    Float getTopSpeedByImeiAndBetweenTimestamp(String imei, Instant start, Instant end , String drivingMode , Float rearTyreDiameter);



    Long countByMotorIdxImeiAndMotorIdxTimestampBetweenAndMotorSpeedIsNotNull(String imei, Instant start, Instant end);



    @Query(value = "select round(COALESCE(cast(max(motor_speed/11)as decimal),-1.0),2) as max, " +
            "            round(COALESCE(cast(min(motor_speed/11) as decimal),-1.0),2) as min, " +
            "            round(COALESCE(cast(avg(motor_speed/11)as decimal),-1.0),2) as avg, " +
            "            round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY motor_speed)as decimal),-1.0),2) AS median " +
            "            from vehicle_motor_data t " +
            "            where t.timestamp>= ?2 and t.timestamp < ?3 and t.imei=?1 " +
            "            and t.motor_driving_mode = ?4 ", nativeQuery = true)
    SpeedStats getSpeedStatsByImeiAndBetweenTimestamp(String imei, Instant start, Instant end, String driveMode);

    @Query(value = "select round(COALESCE(cast(max(motor_speed/11)as decimal),-1.0),2) as max, " +
            "            round(COALESCE(cast(min(motor_speed/11) as decimal),-1.0),2) as min, " +
            "            round(COALESCE(cast(avg(motor_speed/11) as decimal),-1.0),2) as avg, " +
            "            round(coalesce(cast(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY motor_speed)as decimal),-1.0),2) AS median " +
            "            from vehicle_motor_data t " +
            "            where t.timestamp>= ?2 and t.timestamp < ?3 and t.imei= ?1" +
            "            and t.motor_reverse = ?4 ", nativeQuery = true)
    SpeedStats getReverseSpeedStatsByImeiAndBetweenTimestamp(String imei, Instant start, Instant end, Boolean reverse);


    @Modifying(flushAutomatically = true)
    @Query(value = "update vehicle_motor_data set motor_speed=?1,motor_dc_voltage=?2,motor_dc_current=?3,motor_temperature=?4,motor_mcs_temperature=?5 where timestamp=?6 and imei=?7", nativeQuery = true)
    int updateMotorData(Float motorSpeed, Float dcVoltage, Float dcCurrent, Float motorTemp, Float mcsTemp, Instant timestamp, String imei);

    @Modifying(flushAutomatically = true)
    @Query(value = "update vehicle_motor_data set motor_fault_feedback=?1,motor_driving_mode=?2,motor_regeneration=?3,motor_ready_sign=?4,motor_parking_sign=?5,motor_side_stand=?6,motor_brake=?7,motor_cruise=?8,motor_reverse=?9,motor_throttle=?10 where timestamp=?11 and imei=?12", nativeQuery = true)
    int updateMotorStatus(String faultFeedback, DriveMode drivingMode, Boolean regeneration, Boolean readySign, Boolean parkingSign, Boolean sideStand, Boolean brake, Boolean cruise, Boolean reverse, Float throttle, Instant timestamp, String imei);

    @Query(value = "select distinct(imei) from vehicle_motor_data where timestamp between ?1 and ?2", nativeQuery = true)
    List<String> findDistinctImeiListBetweenTimestamps(Instant startTime, Instant endTime);

    @Query(value = "select motor_speed as motorSpeed, timestamp from vehicle_motor_data where imei = ?1 and timestamp between ?2 and now() and motor_speed between 0 and 1000 order by timestamp asc", nativeQuery = true)
    List<SpeedDto> getDistanceTravelled(String imei, Instant timestamp);

    @Query(value = "select sum(motor_speed/11)/3600 as odo from vehicle_motor_data where imei = ?1 and timestamp between ?2 and ?3 and motor_speed between 0 and 1000", nativeQuery = true)
    Float getDistanceTravelled(String imei, Instant startTime, Instant endTime);

    @Query(value = "select MAX(timestamp) from vehicle_motor_data where imei = ?1", nativeQuery = true)
    Instant getLatestMotorTimestamp(String imei);

    @Query(value = "SELECT round(coalesce(cast(MAX(max_motor_dc_voltage) as decimal), 0),2) as maxDcVoltage, " +
            "                           round(coalesce(cast(MIN(min_motor_dc_voltage) as decimal), 0),2) as minDcVoltage, " +
            "                           round(coalesce(cast(AVG(avg_motor_dc_voltage) as decimal), 0),2)   as avgDcVoltage, " +
            "                           round(coalesce(cast(MAX(max_motor_dc_current) as decimal), 0),2)   as maxDcCurrent, " +
            "                           round(coalesce(cast(MIN(min_motor_dc_current) as decimal), 0),2)   as minDcCurrent, " +
            "                           round(coalesce(cast(AVG(avg_motor_dc_current) as decimal), 0),2)   as avgDcCurrent, " +
            "                           round(coalesce(cast(MAX(max_motor_temperature) as decimal), 0),2)   as maxMotorTemp, " +
            "                           round(coalesce(cast(MIN(min_motor_temperature) as decimal), 0),2)   as minMotorTemp, " +
            "                           round(coalesce(cast(AVG(avg_motor_temperature) as decimal), 0),2)   as avgMotorTemp, " +
            "                           round(coalesce(cast(MAX(max_motor_mcs_temperature) as decimal), 0),2)   as maxMcsTemp, " +
            "                           round(coalesce(cast(MIN(min_motor_mcs_temperature) as decimal), 0),2)   as minMcsTemp, " +
            "                           round(coalesce(cast(AVG(avg_motor_mcs_temperature) as decimal), 0),2)   as avgMcsTemp " +
            "            FROM view_vehicle_motor_aggregate_data vt " +
            "            WHERE vt.bucket >= ?1 AND vt.bucket <= ?2 and vt.mfr_org_id=?3", nativeQuery = true)
    MotorResultProjection findByImeiInAndTimestampGreaterThanAndTimestampLessThan(
            Instant from, Instant to, Long orgId);


    @Query(value = "select count(distinct(aggr.imei)) from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?3 and ?4 " +
            "and aggr.mfr_org_id=?2 " +
            "and attr.attr_name=?1 " +
            "and aggr.max_motor_dc_voltage > CAST (attr.attr_value as INT) ", nativeQuery = true)
    Float getCountOfDcVoltageAboveAverage(String attributeName, Long organisationId, Instant from, Instant to);

    @Query(value = "select count(distinct(aggr.imei)) from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?3 and ?4 " +
            "and aggr.mfr_org_id=?2 " +
            "and attr.attr_name=?1 " +
            "and aggr.min_motor_dc_voltage < CAST (attr.attr_value as INT) ", nativeQuery = true)
    Float getCountOfDcVoltageBelowAverage(String attributeName, Long organisationId, Instant from, Instant to);

    @Query(value = "select count(distinct(aggr.imei)) from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?3 and ?4 " +
            "and aggr.mfr_org_id=?2 " +
            "and attr.attr_name=?1 " +
            "and aggr.max_motor_dc_current > CAST (attr.attr_value as INT) ", nativeQuery = true)
    Float getCountOfDcCurrentAboveAverage(String attributeName, Long organisationId, Instant from, Instant to);

    @Query(value = "select count(distinct(aggr.imei)) from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?3 and ?4 " +
            "and aggr.mfr_org_id=?2 " +
            "and attr.attr_name=?1 " +
            "and aggr.min_motor_dc_current < CAST (attr.attr_value as INT) ", nativeQuery = true)
    Float getCountOfDcCurrentBelowAverage(String attributeName, Long organisationId, Instant from, Instant to);

    @Query(value = "select count(distinct(aggr.imei)) from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?3 and ?4 " +
            "and aggr.mfr_org_id=?2 " +
            "and attr.attr_name=?1 " +
            "and aggr.max_motor_mcs_temperature > CAST (attr.attr_value as INT) ", nativeQuery = true)
    Float getCountOfMcsTempAboveAverage(String attributeName, Long organisationId, Instant from, Instant to);

    @Query(value = "select count(distinct(aggr.imei)) from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?3 and ?4 " +
            "and aggr.mfr_org_id=?2 " +
            "and attr.attr_name=?1 " +
            "and aggr.min_motor_mcs_temperature < CAST (attr.attr_value as INT) ", nativeQuery = true)
    Float getCountOfMcsTempBelowAverage(String attributeName, Long organisationId, Instant from, Instant to);

    @Query(value = "select count(distinct(aggr.imei)) from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?3 and ?4 " +
            "and aggr.mfr_org_id=?2 " +
            "and attr.attr_name=?1 " +
            "and aggr.max_motor_temperature > CAST (attr.attr_value as INT) ", nativeQuery = true)
    Float getCountOfMotorTempAboveAverage(String attributeName, Long organisationId, Instant from, Instant to);

    @Query(value = "select count(distinct(aggr.imei)) from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?3 and ?4 " +
            "and aggr.mfr_org_id=?2 " +
            "and attr.attr_name=?1 " +
            "and aggr.min_motor_temperature < CAST (attr.attr_value as INT) ", nativeQuery = true)
    Float getCountOfMotorTempBelowAverage(String attributeName, Long organisationId, Instant from, Instant to);

    @Query(value = "SELECT round(cast(motor_dc_voltage as decimal),2) as dcVoltage, round(cast(motor_dc_current as decimal),2) as dcCurrent," +
            "round(cast(motor_mcs_temperature as decimal),2) as mcsTemperature, round(cast(motor_temperature as decimal),2) as motorTemperature, " +
            "time_bucket(cast(:intervalTime as interval), bucket) as timestamp FROM view_motor_data t, vehicle v " +
            "WHERE bucket BETWEEN :startTime and :endTime  " +
            "AND t.imei = v.imei and v.mfr_org_id=:orgId " +
            "and motor_dc_current is not null and motor_mcs_temperature is not null and motor_dc_current is not null and " +
            "motor_dc_voltage is not null " +
            " group by motor_dc_voltage, motor_dc_current, motor_mcs_temperature, " +
            "motor_temperature, timestamp  order by timestamp desc limit 100", nativeQuery = true)
    List<SparklingData> getSparklingData(@Param("intervalTime") String intervalTime,
                                         @Param("startTime") Instant startTime,
                                         @Param("endTime") Instant endTime,
                                         @Param("orgId") Long orgId);





    @Query(value = "select distinct on(aggr.imei) aggr.imei, max(aggr.max_motor_dc_voltage) as value " +
            "from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?2 and ?3 " +
            "and aggr.mfr_org_id=?1 " +
            "and attr.attr_name=?4 " +
            "and aggr.max_motor_dc_voltage > CAST (attr.attr_value as INT) " +
            "group by aggr.imei " +
            "order by aggr.imei limit 10", nativeQuery = true)
    List<VehicleMinMaxMotorData> getMaxVehicleDataByDcVoltage(Long id, Instant startTime, Instant endTime, String attributeName);

    @Query(value = "select distinct on(aggr.imei) aggr.imei, max(aggr.min_motor_dc_voltage) as value " +
            "from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?2 and ?3 " +
            "and aggr.mfr_org_id=?1 " +
            "and attr.attr_name=?4 " +
            "and aggr.min_motor_dc_voltage < CAST (attr.attr_value as INT) " +
            "group by aggr.imei " +
            "order by aggr.imei limit 10", nativeQuery = true)
    List<VehicleMinMaxMotorData> getMinVehicleDataByDcVoltage(Long id, Instant startTime, Instant endTime, String attributeName);

    @Query(value = "select distinct on(aggr.imei) aggr.imei, max(aggr.max_motor_dc_current) as value " +
            "from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?2 and ?3 " +
            "and aggr.mfr_org_id=?1 " +
            "and attr.attr_name=?4 " +
            "and aggr.max_motor_dc_current > CAST (attr.attr_value as INT) " +
            "group by aggr.imei " +
            "order by aggr.imei limit 10", nativeQuery = true)
    List<VehicleMinMaxMotorData> getMaxVehicleDataByDcCurrent(Long id, Instant startTime, Instant endTime, String attributeName);

    @Query(value = "select distinct on(aggr.imei) aggr.imei, max(aggr.min_motor_dc_current) as value " +
            "from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?2 and ?3 " +
            "and aggr.mfr_org_id=?1 " +
            "and attr.attr_name=?4 " +
            "and aggr.min_motor_dc_current < CAST (attr.attr_value as INT) " +
            "group by aggr.imei " +
            "order by aggr.imei limit 10", nativeQuery = true)
    List<VehicleMinMaxMotorData> getMinVehicleDataByDcCurrent(Long id, Instant startTime, Instant endTime, String attributeName);

    @Query(value = "select distinct on(aggr.imei) aggr.imei, max(aggr.max_motor_mcs_temperature) as value " +
            "from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?2 and ?3 " +
            "and aggr.mfr_org_id=?1 " +
            "and attr.attr_name=?4 " +
            "and aggr.max_motor_mcs_temperature > CAST (attr.attr_value as INT) " +
            "group by aggr.imei " +
            "order by aggr.imei limit 10", nativeQuery = true)
    List<VehicleMinMaxMotorData> getMaxVehicleDataByMcsTemp(Long id, Instant startTime, Instant endTime, String attributeName);

    @Query(value = "select distinct on(aggr.imei) aggr.imei, max(aggr.min_motor_mcs_temperature) as value " +
            "from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?2 and ?3 " +
            "and aggr.mfr_org_id=?1 " +
            "and attr.attr_name=?4 " +
            "and aggr.min_motor_mcs_temperature < CAST (attr.attr_value as INT) " +
            "group by aggr.imei " +
            "order by aggr.imei limit 10", nativeQuery = true)
    List<VehicleMinMaxMotorData> getMinVehicleDataByMcsTemp(Long id, Instant startTime, Instant endTime, String attributeName);

    @Query(value = "select distinct on(aggr.imei) aggr.imei, max(aggr.max_motor_temperature) as value " +
            "from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?2 and ?3 " +
            "and aggr.mfr_org_id=?1 " +
            "and attr.attr_name=?4 " +
            "and aggr.max_motor_temperature > CAST (attr.attr_value as INT) " +
            "group by aggr.imei " +
            "order by aggr.imei limit 10", nativeQuery = true)
    List<VehicleMinMaxMotorData> getMaxVehicleDataByMotorTemp(Long id, Instant startTime, Instant endTime, String attributeName);

    @Query(value = "select distinct on(aggr.imei) aggr.imei, max(aggr.max_temperature) as value from view_battery_sensor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?2 and ?3 " +
            "and aggr.mfr_org_id=?1 " +
            "and attr.attr_name=?5 " +
            "and aggr.stack_id=?4 " +
            "and aggr.max_temperature > CAST (attr.attr_value as INT) " +
            "group by aggr.imei " +
            "order by aggr.imei limit 10 ", nativeQuery = true)
    List<VehicleMinMaxMotorData> getSensorMaxData(Long id, Instant startTime, Instant endTime, Long stackId, String attributeName);

    @Query(value = "select distinct on(aggr.imei) aggr.imei, max(aggr.min_temperature) as value from view_battery_sensor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?2 and ?3 " +
            "and aggr.mfr_org_id=?1 " +
            "and attr.attr_name=?5 " +
            "and aggr.stack_id=?4 " +
            "and aggr.min_temperature < CAST (attr.attr_value as INT) " +
            "group by aggr.imei " +
            "order by aggr.imei limit 10 ", nativeQuery = true)
    List<VehicleMinMaxMotorData> getSensorMinData(Long id, Instant startTime, Instant endTime, Long stackId, String attributeName);

    @Query(value = "select distinct on(aggr.imei) aggr.imei, max(aggr.min_motor_temperature) as value " +
            "from view_vehicle_motor_aggregate_data aggr " +
            "join view_vehicle_part_part_model vvppm on vvppm.imei=aggr.imei " +
            "join view_vehicle_model_part_model_attribute attr on attr.vehicle_model_org_part_type_part_model_id = vvppm.vehicle_model_org_part_type_part_model_id " +
            "where aggr.bucket between ?2 and ?3 " +
            "and aggr.mfr_org_id=?1 " +
            "and attr.attr_name=?4 " +
            "and aggr.min_motor_temperature < CAST (attr.attr_value as INT) " +
            "group by aggr.imei " +
            "order by aggr.imei limit 10", nativeQuery = true)
    List<VehicleMinMaxMotorData> getMinVehicleDataByMotorTemp(Long id, Instant startTime, Instant endTime, String attributeName);

    Optional<CurrentDriveModeDto> findFirstByMotorIdxImeiAndMotorDrivingModeIsNotNullOrderByMotorIdxTimestampDesc(String imei);

    /**
     * Get the avg speed of the vehicle between the given timestamps using the motor rpm & rear tyre diameter
     * speed in meter/min = (avg(motor_speed) * pi() * tyreDiameter)
     * speed in km/hr = avg(motor_speed) * pi() * tyreDiameter * 60/1000
     * @param imei
     * @param startTime
     * @param endTime
     * @param tyreDiameter
     * @return
     */
    @Query(value = "select (avg(motor_speed) * pi() * 0.06 * ?4) as avgSpeed from vehicle_motor_data where imei = ?1 and timestamp between ?2 and ?3 and motor_speed between 0 and 1000", nativeQuery = true)
    Float findAvgMotorSpeedByImeiAndTimestampBetween(String imei, Instant startTime, Instant endTime,Float tyreDiameter);


    @Query(value = "SELECT SUM(motor_speed * pi() * ( ?4 ) * (1.0 / 60) / 1000) AS totalDistanceInKm " +
            "FROM vehicle_motor_data WHERE imei = ?1 AND timestamp BETWEEN ?2 AND ?3 AND motor_speed > 0", nativeQuery = true)
    Float getTotalDistanceTravelledForImeiBetweenStartTimeAndEndTimeUsingTyreDiameter(String imei, Instant startTime, Instant endTime, Float rearTyreDiameter);

    @Query(value = "SELECT ( " +
            " COUNT(motor_brake) + COUNT(motor_cruise) + COUNT(motor_dc_current) + " +
            " COUNT(motor_dc_voltage) + COUNT(motor_mcs_temperature) + COUNT(motor_parking_sign) + " +
            " COUNT(motor_ready_sign) + COUNT(motor_regeneration) + COUNT(motor_reverse) + " +
            " COUNT(motor_side_stand) + COUNT(motor_speed) + COUNT(motor_temperature) + COUNT(motor_throttle) + " +
            " COUNT(motor_driving_mode) + COUNT(motor_fault_feedback) " +
            ") AS totalMotorCount " +
            " FROM vehicle_motor_data " +
            " WHERE imei = ?1 AND timestamp >= ?2 AND timestamp <= ?3 ", nativeQuery = true)
    Integer getTotalMotorFieldCount(String imei, Instant startTime, Instant endTime);

    @Query(value = "SELECT timestamp, motor_driving_mode AS motorDrivingMode, imei\n" +
            "FROM vehicle_motor_data\n" +
            "WHERE timestamp between ?4 and now() and imei = ?1 \n" +
            "AND di_ignition = TRUE \n" +
            "AND di_motion = TRUE\n" +
            "AND created_on between ?2 AND ?3 order by timestamp desc\n", nativeQuery = true)
    List<MotorTelemetryDataProjection> findDriveModeByImeiAndTimestampBetween(String imei, Instant startTime, Instant endTime,Instant bufferTime);

}