package com.nichesolv.evahanam.telemetryData.repository;

import com.nichesolv.evahanam.telemetryData.jpa.AbstractVehicleImuData;
import com.nichesolv.evahanam.telemetryData.jpa.TelemetryIdx;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.Optional;
@NoRepositoryBean
public interface AbstractImuDataRepository<T extends AbstractVehicleImuData> extends JpaRepository<T, TelemetryIdx> {

    Optional<T> findById(TelemetryIdx id);

    Optional<T> findFirstByTelemetryIdxImeiOrderByTelemetryIdxTimestampDesc(String imei);

}
