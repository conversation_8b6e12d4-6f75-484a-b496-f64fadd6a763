package com.nichesolv.evahanam.telemetryData.service.vehicleData;

import com.nichesolv.evahanam.common.exception.ActiveSubscriptionPlanException;
import com.nichesolv.evahanam.common.jpa.ActiveVehicleSubscriptionPlan;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.repository.ActiveVehicleSubscriptionPlanRepository;
import com.nichesolv.evahanam.common.repository.DataFrequencyPlanDetailsRepository;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.telemetryData.enums.*;
import com.nichesolv.evahanam.vehicle.dto.DataAvailabilityDto;
import com.nichesolv.evahanam.vehicle.dto.DataPercentageDto;
import com.nichesolv.evahanam.vehicle.enums.TimeFilter;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicle.service.VehicleService;
import com.nichesolv.evahanam.vehicleTests.exception.VehicleTestException;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import com.nichesolv.evahanam.vehicleTests.v2.dto.FieldListDto;
import com.nichesolv.evahanam.vehicleTests.v2.dto.ResultDto;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Slf4j
@Service
public class VehicleDataServiceImpl implements VehicleDataService {

    @Autowired
    EntityManager entityManager;

    @Autowired
    VehicleTestRepository vehicleTestRepository;

    @Autowired
    VehicleService vehicleService;

    @Autowired
    DataFrequencyPlanDetailsRepository dataFrequencyPlanDetailsRepository;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    ActiveVehicleSubscriptionPlanRepository activeVehicleSubscriptionPlanRepository;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;

    @Override
    public List<String> getColumnName(ExportDataType vehicleDataTable) {
        return switch (vehicleDataTable) {
            case MOTOR -> MotorTableColumn.getColumns();
            case BATTERY -> BatteryTableColumn.getColumns();
            case LOCATION -> LocationTableColumn.getColumns();
            case TELEMETRY -> TelemetryTableColumn.getColumns();
            case IMU -> ImuTableColumn.getColumns();
            case BATTERY_CELL -> BatteryCellTableColumn.getColumns();
            case BATTERY_STACK ->BatteryStackTableColumn.getColumns();
            default -> throw new IllegalArgumentException("Unknown table: " + vehicleDataTable.name());
        };
    }

    @Override
    public ResultDto getData(String vehicleId, Optional<Long> testId, Optional<Long> startTime, Optional<Long> endTime, ExportDataType dataTable, FieldListDto requestDto,String samplingFrequency, Boolean showErrors) {
        if (testId.isEmpty() && (startTime.isEmpty() || endTime.isEmpty())) {
            throw new VehicleTestException("Either testId or both startTime and endTime must be provided");
        }
        Instant resultStartTime = null, resultEndTime = null;
        List<String> field = null;
        if (testId.isPresent()) {
            VehicleTest vehicleTest = vehicleTestRepository.findById(testId.get()).orElseThrow(() -> new VehicleTestException("TEST_NOT_FOUND"));
            resultStartTime = vehicleTest.getStartTime();
            resultEndTime = vehicleTest.getEndTime();
        } else {
            resultStartTime = Instant.ofEpochMilli(startTime.get());
            resultEndTime = Instant.ofEpochMilli(endTime.get());
        }
        Vehicle vehicle = vehicleService.getVehicleByAnyId(vehicleId);
        if (requestDto != null) {
            field = requestDto.getField();
        }
        List<String> columnList = (field != null && !field.isEmpty()) ? field : getColumnName(dataTable);
        List<String> validColumns = getColumnName(dataTable);
        List<String> invalidColumnList = columnList.stream().filter(column -> !validColumns.contains(column)).toList();

        if (!invalidColumnList.isEmpty()) {
            throw new IllegalArgumentException(
                    String.format("The following fields are invalid: %s. Valid fields are: %s", invalidColumnList, validColumns)
            );
        }

        Set<Object> missingColumns = new HashSet<>();
        String timeColumn = samplingFrequency == null ? "timestamp":"bucket";
        String selectedTable;

        // For BATTERY_CELL and BATTERY_STACK, only aggregate tables are available (raw table is null)
        if (dataTable.getDataTableName() == null || dataTable.getDataTableName().isEmpty()) {
            if (samplingFrequency == null) {
                throw new IllegalArgumentException("Sampling frequency is required for " + dataTable.name() + " data type. Available options: 1m, 10m, 1h");
            }
            selectedTable = switch (samplingFrequency) {
                case "1m" -> dataTable.getOneMinAggregateTable();
                case "10m" -> dataTable.getTenMinAggregateTable();
                case "1h" -> dataTable.getOneHourAggregateTable();
                default -> throw new IllegalArgumentException("Invalid sampling frequency for " + dataTable.name() + ". Available options: 1m, 10m, 1h");
            };
        } else {
            // For other data types, handle both raw and aggregate tables
            if (samplingFrequency == null) {
                selectedTable = dataTable.getDataTableName();
            } else {
                selectedTable = switch (samplingFrequency) {
                    case "1m" -> dataTable.getOneMinAggregateTable();
                    case "10m" -> dataTable.getTenMinAggregateTable();
                    case "1h" -> dataTable.getOneHourAggregateTable();
                    default -> dataTable.getDataTableName();
                };
            }
        }

        String columnString = String.join(", ", columnList);
        String sql;

        switch (dataTable) {
            case BATTERY_STACK:
                sql = String.format("SELECT imei, %s, stack_id, %s FROM %s WHERE imei = :imei AND %s BETWEEN :start AND :end ORDER BY %s, stack_id ASC",
                        timeColumn, columnString, selectedTable, timeColumn, timeColumn);
                break;
            case BATTERY_CELL:
                sql = String.format("SELECT imei, %s, cell_id, %s FROM %s WHERE imei = :imei AND %s BETWEEN :start AND :end ORDER BY %s, cell_id ASC",
                        timeColumn, columnString, selectedTable, timeColumn, timeColumn);
                break;
            default:
                sql = String.format("SELECT imei, %s, %s FROM %s WHERE imei = :imei AND %s BETWEEN :start AND :end ORDER BY %s ASC",
                        timeColumn, columnString, selectedTable, timeColumn, timeColumn);
                break;
        }

        log.info(" sql query data table : {}", sql);
        Query query = entityManager.createNativeQuery(sql);
        query.setParameter("imei", vehicle.getImei());
        query.setParameter("start", resultStartTime);
        query.setParameter("end", resultEndTime);
        List<Object[]> result = query.getResultList();
        log.info("error result set size : {} ", result.size());
        List<Map<String, Object>> dataList = new ArrayList<>();
        Set<String> missingFieldSet = new HashSet<>();
        for (Object[] row : result) {
            Map<String, Object> data = new HashMap<>();
            int index = 0;
            data.put("imei", row[index++]);
            Instant timestamp = (Instant) row[index++];
            data.put("timestamp", timestamp.toEpochMilli());
            log.info(" timestamp data {} ", timestamp.toEpochMilli());

            // Handle additional group columns for battery stack and cell
            if (dataTable == ExportDataType.BATTERY_STACK) {
                data.put("stack_id", row[index++]);
            } else if (dataTable == ExportDataType.BATTERY_CELL) {
                data.put("cell_id", row[index++]);
            }

            for (String column : columnList) {
                Object value = row[index++];
                data.put(column, value);
                if (showErrors && value == null) {
                    log.info(" column name {} ", column);
                    missingFieldSet.add(column);
                }
            }
            dataList.add(data);
        }
        Set<String> errorFieldSet = new HashSet<>(); // using set to avoid duplicate columns
        List<Map<String, Object>> errorList = new ArrayList<>();

        if (showErrors) {
            String errorColumnSqlError = String.join(", ", columnList);
            String errorSql = String.format("select imei , timestamp , %s from %s where imei = :imei and timestamp between :start and :end ", columnString, dataTable.getErrorTableName());
            log.info(" sql query error table : {}", errorSql);
            Query errorQuery = entityManager.createNativeQuery(errorSql);
            errorQuery.setParameter("imei", vehicle.getImei());
            errorQuery.setParameter("start", resultStartTime);
            errorQuery.setParameter("end", resultEndTime);
            List<Object[]> errorResult = errorQuery.getResultList();
            log.info("error result set size : {} ", errorResult.size());
            for (Object[] row : errorResult) {
                int index = 0;
                Map<String, Object> error = new HashMap<>();
                error.put("imei", row[index++]);
                Instant timestamp = (Instant) row[index++];
                error.put("timestamp", timestamp.toEpochMilli());

                for (String column : columnList) {
                    Object value = row[index++];
                    error.put(column, value);
                    if (value != null) {
                        errorFieldSet.add(column);
                        log.info(" error field value {} ", value);
                    }
                }
                errorList.add(error);
            }
        }
        log.info(" missing field set : {}", missingFieldSet);
        log.info(" error field set : {}", errorFieldSet);
        ResultDto resultDto = new ResultDto();
        resultDto.setData(dataList);
        resultDto.setError(errorList);
        resultDto.setErrorField(errorFieldSet);
        resultDto.setMissingField(missingFieldSet);
        return resultDto;
    }

    @Override
    public DataAvailabilityDto getPercentageBetween(String identifier, Long from, Long to, TimeFilter timeFilter, ExportDataType vehicleDataTable) {
        Vehicle vehicle = vehicleService.getVehicleByAnyId(identifier);
        String imei = vehicle.getImei();
        Instant startTime = Instant.ofEpochMilli(from);
        Instant endTime = Instant.ofEpochMilli(to);
        ActiveVehicleSubscriptionPlan activeVehicleSubscriptionPlan = activeVehicleSubscriptionPlanRepository.findByVehicle(vehicle)
                .orElseThrow(() -> new ActiveSubscriptionPlanException(evMessageBundle.getMessage("ACTIVE_SUBSCRIPTION_PLAN_NOT_FOUND", vehicle.getId())));

        DataFrequencyPlan dataFrequencyPlan = activeVehicleSubscriptionPlan.getDataFrequencyPlan();


        int dataFrequency = dataFrequencyPlan.getValue();
        ChronoUnit dataFrequencyUnit = dataFrequencyPlan.getUnit();

        // allowing the unit of data frequency as seconds for now
        if (!dataFrequencyUnit.equals(ChronoUnit.SECONDS)) {
            throw new RuntimeException("Error in the Unit of Frequency Plan");
        }

        List<DataPercentageDto> breakups = new ArrayList<>();

        // allow for DAY , WEEK , MONTH filters
        Set<TimeFilter> allowedFilters = Set.of(TimeFilter.DAY, TimeFilter.WEEK, TimeFilter.MONTH);
        if (!allowedFilters.contains(timeFilter)) {
            throw new IllegalArgumentException("Unsupported Time Filter: " + timeFilter);
        }

        ChronoUnit unit = ChronoUnit.DAYS;
        Instant current = startTime;
        while (current.isBefore(endTime)) {
            Instant next;
            if (timeFilter == TimeFilter.DAY) {
                next = endTime;
            } else {
                next = current.plus(1, unit);
                if (next.isAfter(endTime)) {
                    next = endTime;
                }
            }

            DataPercentageDto dataPercentageDto = calculateDataPercentageForInterval(imei, current, next, vehicleDataTable, dataFrequency);
            breakups.add(dataPercentageDto);
            log.debug(" dataPercentage :{}", dataPercentageDto.getDataPercentage());

            if (timeFilter == TimeFilter.DAY)
                break;
            current = next;
        }
        return new DataAvailabilityDto(imei, breakups);
    }

    private DataPercentageDto calculateDataPercentageForInterval(String imei, Instant start, Instant end, ExportDataType vehicleDataTable, int dataFrequency) {
        String aggregateName = vehicleDataTable.getAggregateTable();
        String aggregateColumnName = vehicleDataTable.getDayCountColumn();

        log.debug(" Instant start {} , Instant end {}" , start , end);
        long durationInSeconds = Duration.between(start, end).getSeconds();
        long expectedRecords = durationInSeconds / dataFrequency;

        ZoneId istZone = ZoneId.of("Asia/Kolkata");
        LocalDate startDate = start.atZone(istZone).toLocalDate();
        LocalDate endDate = end.atZone(istZone).toLocalDate();


        boolean isSingleDay = startDate.equals(endDate);

        String sql = String.format(
                "SELECT day, %s FROM %s WHERE imei = :imei AND day %s",
                aggregateColumnName,
                aggregateName,
                isSingleDay ? "= :start" : ">= :start AND day < :end"
        );

        Query query = entityManager.createNativeQuery(sql);
        query.setParameter("imei", imei);
        query.setParameter("start", startDate);
        if (!isSingleDay) {
            query.setParameter("end", endDate);
        }

        List<Object[]> result = query.getResultList();
        long actualRecords = 0;
        log.debug("from : {} , localDate :{} , to :{} , localDate :{} , aggregateName :{} ,aggregateColumnName :{} ", start, startDate, end, endDate, aggregateName, aggregateColumnName);
        if (!result.isEmpty()) {
            Object[] row = result.get(0);
            actualRecords = ((Number) row[1]).longValue();
            log.debug("Row - count: {}", actualRecords);
        } else {
            log.debug("No aggregate data found for IMEI {} between {} and {}", imei, start, end);
        }
        double percentage = expectedRecords == 0 ? 0.0 :
                Math.round((actualRecords * 100.0 / expectedRecords) * 100.0) / 100.0;

        return new DataPercentageDto(
                start.toEpochMilli(),
                end.toEpochMilli(),
                percentage,
                expectedRecords,
                actualRecords
        );
    }
}
