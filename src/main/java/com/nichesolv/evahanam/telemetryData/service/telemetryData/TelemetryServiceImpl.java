package com.nichesolv.evahanam.telemetryData.service.telemetryData;

import com.nichesolv.evahanam.common.service.DateUtils;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.telemetryData.dto.TelemetryDto;
import com.nichesolv.evahanam.telemetryData.jpa.TelemetryIdx;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleTelemetryData;
import com.nichesolv.evahanam.telemetryData.repository.ImuDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.MotorDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.vehicle.exception.VehicleException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
public class TelemetryServiceImpl implements TelemetryService {

  @Autowired
  VehicleRepository vehicleRepository;

  @Autowired
  EvMessageBundle evMessageBundle;

  @Autowired
  VehicleDataRepository vehicleDataRepository;


  @Autowired
  ImuDataRepository imuDataRepository;

  @Autowired
  MotorDataRepository motorDataRepository;

  public VehicleTelemetryData getDataPacket(TelemetryDto objectDto){
    TelemetryIdx dataIdx = new TelemetryIdx(DateUtils.longToLocalDateTime(objectDto.getTimestamp()),objectDto.getImei());
    Optional<VehicleTelemetryData> vehicleDataOpts = vehicleDataRepository
            .findById(dataIdx);

    VehicleTelemetryData object = null;
    if(vehicleDataOpts.isPresent()) {
//      log.info("Record found {} {}",objectDto.getImei(),objectDto.getTimestamp());
      object  = vehicleDataOpts.get();
    }else{
//      log.info("Record not found {} {}",objectDto.getImei(),objectDto.getTimestamp());
      Vehicle vehicle = vehicleRepository.findByImei(
              objectDto.getImei()).orElseThrow(()->new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND_WITH_INFO",objectDto.getImei())));
      object = new VehicleTelemetryData();
      object.setVehicle(vehicle);
      object.setOwnerOrg(vehicle.getOwner());
      object.setMfrOrg(vehicle.getManufacturer());
      object.setTelemetryIdx(dataIdx);
      object.setPacketReceivedOn(DateUtils.longToLocalDateTime(Optional.ofNullable(objectDto.getPacketReceivedOn()).orElse(System.currentTimeMillis()/1000)));
      object.setCoRelationId(UUID.fromString(objectDto.getCoRelationId()));
    }
    return object;
  }

  @Override
  @Transactional

  public void saveVehicleData(VehicleTelemetryData vehicleTelemetryData)  {
    log.info("Saving record for {} {}",vehicleTelemetryData.getTelemetryIdx().getImei(),vehicleTelemetryData.getTelemetryIdx().getTimestamp());
    vehicleDataRepository.saveAndFlush(vehicleTelemetryData);

    log.info("Saved record for {} {}",vehicleTelemetryData.getTelemetryIdx().getImei(),vehicleTelemetryData.getTelemetryIdx().getTimestamp());

  }

  public int updateMotorData(Float motorSpeed, Float dcVoltage, Float dcCurrent, Float motorTemp, Float mcsTemp, Instant timestamp, String imei){
    return motorDataRepository.updateMotorData(motorSpeed,dcVoltage,dcCurrent,motorTemp,mcsTemp,timestamp,imei);
  }

  @Override
  public int updateMotorStatus(String faultFeedback, DriveMode drivingMode, Boolean regeneration, Boolean readySign, Boolean parkingSign, Boolean sideStand, Boolean brake, Boolean cruise, Boolean reverse, Float throttle, Instant timestamp, String imei) {
    return motorDataRepository.updateMotorStatus(faultFeedback,drivingMode, regeneration, readySign,  parkingSign,
            sideStand,  brake,  cruise,  reverse,  throttle,  timestamp,  imei);
  }


  @Override
  public int updateDigitalIo(Boolean usr1, Boolean usr2, Instant timestamp, String imei) {
    return vehicleDataRepository.updateDigitalIo(usr1, usr2,  timestamp,  imei);
  }


  @Override
  public int updateAnalogIo(Float voltageInput, Float temperature, Float systemVoltage, Float vbuck, Integer vusr1, Integer vusr2, Integer leanAngle, Instant timestamp, String imei) {
    return vehicleDataRepository.updateAnalogIo(voltageInput,  temperature,  systemVoltage,
            vbuck,  vusr1,  vusr2,  leanAngle,  timestamp,  imei);
  }


  @Override
  public int updateAccelerometer(Float xAxis, Float yAxis, Float zAxis, Instant timestamp, String imei) {
    return imuDataRepository.updateAccelerometer( xAxis, yAxis, zAxis,  timestamp,  imei) ;
  }

  @Override
  public int updateDigitalInputData(Boolean usr1, Boolean usr2, Boolean motion, Boolean tamper, Boolean mainPower, Boolean ignition, Instant timestamp, String imei) {
    return vehicleDataRepository.updateDigitalInput(usr1, usr2, motion, tamper, mainPower, ignition, timestamp, imei);
  }



}
