package com.nichesolv.evahanam.telemetryData.service.telemetryData;

import com.nichesolv.evahanam.evApp.exception.OrganisationNotFoundException;
import com.nichesolv.evahanam.telemetryData.dto.*;
import com.nichesolv.evahanam.telemetryData.enums.*;
import com.nichesolv.evahanam.telemetryData.exception.ExportReportException;
import com.nichesolv.evahanam.telemetryData.jpa.ExportRequest;
import com.nichesolv.evahanam.telemetryData.repository.ExportRequestStatusRepository;
import com.nichesolv.evahanam.telemetryData.util.CommonQueryUtility;
import com.nichesolv.evahanam.vehicle.dto.VehicleIdsProjection;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.util.rabbitmq.producer.RabbitMQProducer;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ExportServiceImpl implements ExportService {
    @Autowired
    RabbitMQProducer queueProducer;
    @Value("${rabbitmq.queue.csv.request.save}")
    String csvRequestQueue;
    @Autowired
    ExportRequestStatusRepository exportRequestStatusRepository;
    @Autowired
    CommonQueryUtility commonQueryUtility;
    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    IVehicleService vehicleService;

    @Override
    @Transactional
    public Long createExportRequest(ExportRequestDto exportRequestDto) {
        customOrganisationRepository.findById(exportRequestDto.getOrgId()).orElseThrow(() -> new OrganisationNotFoundException("Organisation not found"));
        Vehicle vehicle = vehicleService.getVehicleByAnyId(exportRequestDto.getImei());

        List<List<String>> fieldLists = new ArrayList<>();
        Map<ExportDataType, Class<? extends Enum<?>>> columnMapping = Map.of(
                ExportDataType.TELEMETRY, TelemetryTableColumn.class,
                ExportDataType.BATTERY, BatteryTableColumn.class,
                ExportDataType.MOTOR, MotorTableColumn.class,
                ExportDataType.LOCATION, LocationTableColumn.class,
                ExportDataType.IMU, ImuTableColumn.class
        );

        for (int i = 0; i < exportRequestDto.getExportDataTypes().size(); i++) {
            ExportDataType dataType = exportRequestDto.getExportDataTypes().get(i);
            Class<? extends Enum> enumClass = columnMapping.get(dataType);
            List<String> fields = exportRequestDto.getColumnLists().get(i)
                            .stream()
                                    .map(e->Enum.valueOf(enumClass,e).name()).toList();

            fieldLists.add(fields);
        }
        ExportRequest exportRequest = new ExportRequest();
        exportRequest.setStatus(ExportReportRequestStatus.PROCESSING);
        exportRequest.setStartTime(Instant.ofEpochMilli(exportRequestDto.getStartTime()));
        exportRequest.setEndTime(Instant.ofEpochMilli(exportRequestDto.getEndTime()));
        exportRequest.setImei(exportRequestDto.getImei());
        exportRequest.setVehicle(vehicle);
        ExportRequest savedExportRequest = exportRequestStatusRepository.save(exportRequest);
        exportRequestDto.setReportId(savedExportRequest.getId());
        exportRequestDto.setColumnLists(fieldLists);

        queueProducer.sendMessage(csvRequestQueue, exportRequestDto);
        return exportRequestDto.getReportId();
    }

    @Override
    @Transactional
    public void updateReportUrl(ExportResponseDto exportResponseDto) {
        ExportRequest exportRequest = exportRequestStatusRepository.findById(exportResponseDto.getId())
                .orElseThrow(() -> new ExportReportException("Export request status not found for id: " + exportResponseDto.getId()));
        exportRequest.setUrl(exportResponseDto.getUrl());
        exportRequest.setStatus(ExportReportRequestStatus.READY);
        exportRequestStatusRepository.save(exportRequest);
    }

    @Override
    @Transactional
    public void updateFailedOrExpiredStatus(ExportResponseDto exportResponseDto, ExportReportRequestStatus exportReportRequestStatus) {
        ExportRequest exportRequest = exportRequestStatusRepository.findById(exportResponseDto.getId())
                .orElseThrow(() -> new ExportReportException("Export request status not found for id: " + exportResponseDto.getId()));
        exportRequest.setStatus(exportReportRequestStatus);
        exportRequest.setUrl(null);
        exportRequestStatusRepository.save(exportRequest);
    }

    @Override
    public ExportStatusDto getExportRequestStatus(Long id) {
        ExportRequest exportRequest = exportRequestStatusRepository.findById(id)
                .orElseThrow(() -> new ExportReportException("Export request status not found for id: " + id));
        return new ExportStatusDto(exportRequest.getStatus(), exportRequest.getUrl());
    }

    @Override
    public Page<ExportRequestProjection> getDownloadHistory(String identifier, Pageable pageable) {
        String imei = vehicleService.populateVehicleIdentifiers(identifier).map(VehicleIdsProjection::getVehImei).get();
        return exportRequestStatusRepository.findByImei(imei, pageable);

    }

    @Override
    public List<Date> getDataMatrix(String imei, YearMonth yearMonth, ExportDataType exportDataType, Boolean rawFlag) {
        String queryString = "SELECT day FROM " + exportDataType.getAggregateTable() + " e WHERE e.imei=:imei AND e.day >= DATE(:startDate) AND e.day < DATE(:startDate) + INTERVAL '1 month' AND " + exportDataType.getDayCountColumn() + " > 0";
        return commonQueryUtility.getDataMatrix(queryString, imei, exportDataType, yearMonth);
    }
}
