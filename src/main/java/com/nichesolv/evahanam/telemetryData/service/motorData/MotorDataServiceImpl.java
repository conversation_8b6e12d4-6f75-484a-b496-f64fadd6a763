package com.nichesolv.evahanam.telemetryData.service.motorData;

import com.nichesolv.evahanam.telemetryData.dto.MotorDataDto;
import com.nichesolv.evahanam.telemetryData.jpa.MotorIdx;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleMotorData;
import com.nichesolv.evahanam.telemetryData.repository.MotorDataRepository;
import com.nichesolv.evahanam.telemetryData.service.motorData.MotorDataService;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;

import com.nichesolv.evahanam.vehicle.service.VehicleService;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
public class MotorDataServiceImpl implements MotorDataService {

  @Autowired
  MotorDataRepository motorDataRepository;

  @Autowired
  VehicleService vehicleService;


  @Override
  @Transactional
  public void addData(MotorDataDto motorDataDto) {
    VehicleMotorData vehicleMotorData = null;
    MotorIdx motorIdx = new MotorIdx(Instant.ofEpochSecond(motorDataDto.getTimestamp()), motorDataDto.getImei());
    Optional<VehicleMotorData> vehicleData = motorDataRepository.findById(motorIdx);
    if (vehicleData.isPresent()) {
      vehicleMotorData = vehicleData.get();
    } else {
      Vehicle vehicle = vehicleService.getVehicleByAnyId(motorDataDto.getImei());
      vehicleMotorData = new VehicleMotorData();
      vehicleMotorData.setVehicle(vehicle);
      vehicleMotorData.setMfrOrg(vehicle.getManufacturer());
      vehicleMotorData.setOwnerOrg(vehicle.getOwner());
      vehicleMotorData.setMotorIdx(motorIdx);
      vehicleMotorData.setPacketReceivedOn(Instant.now());
      log.debug("current time :{}", vehicleMotorData.getPacketReceivedOn());
    }

    vehicleMotorData.setMotorSpeed(motorDataDto.getMotorSpeed());
    vehicleMotorData.setMotorDcVoltage(motorDataDto.getMotorDcVoltage());
    vehicleMotorData.setMotorDcCurrent(motorDataDto.getMotorDcCurrent());
    vehicleMotorData.setMotorTemperature(motorDataDto.getMotorTemperature());
    vehicleMotorData.setMotorMcsTemperature(motorDataDto.getMotorMcsTemperature());

    vehicleMotorData.setMotorRegeneration(motorDataDto.getMotorRegeneration());
    vehicleMotorData.setMotorReadySign(motorDataDto.getMotorReadySign());
    vehicleMotorData.setMotorParkingSign(motorDataDto.getMotorParkingSign());
    vehicleMotorData.setMotorThrottle(motorDataDto.getMotorThrottle());
    vehicleMotorData.setMotorCruise(motorDataDto.getMotorCruise());
    vehicleMotorData.setMotorReverse(motorDataDto.getMotorReverse());
    vehicleMotorData.setMotorSideStand(motorDataDto.getMotorSideStand());
    vehicleMotorData.setMotorBrake(motorDataDto.getMotorBrake());
    if (Optional.ofNullable(motorDataDto.getMotorDrivingMode()).isPresent() && !motorDataDto.getMotorDrivingMode().name().isEmpty()) {
      vehicleMotorData.setMotorDrivingMode(DriveMode.valueOf(motorDataDto.getMotorDrivingMode().name().toUpperCase()));
    }

    vehicleMotorData.setCoRelationId(UUID.fromString(motorDataDto.getCoRelationId()));
    motorDataRepository.save(vehicleMotorData);
    log.debug("imei : {} ,timestamp : {} packetReceivedOn :{} , co_relation_id :{}", vehicleMotorData.getMotorIdx().getImei(), vehicleMotorData.getMotorIdx().getTimestamp(), vehicleMotorData.getPacketReceivedOn() ,vehicleMotorData.getCoRelationId());
  }
}

