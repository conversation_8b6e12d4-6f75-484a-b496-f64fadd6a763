package com.nichesolv.evahanam.telemetryData.service.telemetryData;

import com.nichesolv.evahanam.telemetryData.dto.TelemetryDataDto;
import com.nichesolv.evahanam.telemetryData.jpa.TelemetryIdx;
import com.nichesolv.evahanam.telemetryData.repository.ImuDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleTelemetryData;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
public class TelemetryDataServiceImpl implements TelemetryDataService {

    @Autowired
    VehicleDataRepository vehicleDataRepository;



    @Autowired
    VehicleRepository vehicleRepository;




    @Override
    @Transactional
    public void addData(TelemetryDataDto telemetryDataDto) {
        VehicleTelemetryData vehicleTelemetryData = null;
        TelemetryIdx telemetryIdx = new TelemetryIdx(Instant.ofEpochSecond(telemetryDataDto.getTimestamp()), telemetryDataDto.getImei());
        Optional<VehicleTelemetryData> vehicleData = vehicleDataRepository.findById(telemetryIdx);
        if (vehicleData.isPresent()) {
            vehicleTelemetryData = vehicleData.get();
        } else {
            Optional<Vehicle> vehicle = vehicleRepository.findByImei(telemetryDataDto.getImei());
            vehicleTelemetryData = new VehicleTelemetryData();
            vehicleTelemetryData.setVehicle(vehicle.get());
            vehicleTelemetryData.setMfrOrg(vehicle.get().getManufacturer());
            vehicleTelemetryData.setOwnerOrg(vehicle.get().getOwner());
            vehicleTelemetryData.setTelemetryIdx(telemetryIdx);
            vehicleTelemetryData.setPacketReceivedOn(Instant.now());
            log.debug("current time :{}", vehicleTelemetryData.getPacketReceivedOn());
        }

        vehicleTelemetryData.setAiVoltageInput(telemetryDataDto.getAiVoltageInput());
        vehicleTelemetryData.setAiSystemVoltage(telemetryDataDto.getAiSystemVoltage());
        vehicleTelemetryData.setAiTemperature(telemetryDataDto.getAiTemperature());
        vehicleTelemetryData.setAiVbuck(telemetryDataDto.getAiVbuck());
        vehicleTelemetryData.setAiVusr1(telemetryDataDto.getAiVusr1());
        vehicleTelemetryData.setAiVusr2(telemetryDataDto.getAiVusr2());

        vehicleTelemetryData.setDiUsr1(telemetryDataDto.getDiUsr1());
        vehicleTelemetryData.setDiUsr2(telemetryDataDto.getDiUsr2());
        vehicleTelemetryData.setDiMotion(telemetryDataDto.getDiMotion());
        vehicleTelemetryData.setDiMainPower(telemetryDataDto.getDiMainPower());
        vehicleTelemetryData.setDiTamper(telemetryDataDto.getDiTamper());
        vehicleTelemetryData.setDiIgnition(telemetryDataDto.getDiIgnition());

        vehicleTelemetryData.setDoUsr1(telemetryDataDto.getDoUsr1());
        vehicleTelemetryData.setDoUsr2(telemetryDataDto.getDoUsr2());
        vehicleTelemetryData.setCoRelationId(UUID.fromString(telemetryDataDto.getCoRelationId()));
        vehicleDataRepository.save(vehicleTelemetryData);
        log.debug("imei : {} ,timestamp : {} packetReceivedOn :{} , co_relation_id :{}", vehicleTelemetryData.getTelemetryIdx().getImei(), vehicleTelemetryData.getTelemetryIdx().getTimestamp(), vehicleTelemetryData.getPacketReceivedOn() ,vehicleTelemetryData.getCoRelationId());
    }
}

