package com.nichesolv.evahanam.telemetryData.jpa;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.locationtech.jts.geom.Point;

import java.time.Instant;
import java.util.UUID;

@AllArgsConstructor
@NoArgsConstructor
@Data
@MappedSuperclass
public abstract class AbstractVehicleLocationData {
    @EmbeddedId
    TelemetryIdx telemetryIdx;

    @ManyToOne
    Vehicle vehicle;

    @ManyToOne(targetEntity = CustomOrganisation.class)
    @JoinColumn(name = "owner_org_id")
    Organisation ownerOrg;

    @ManyToOne(targetEntity = CustomOrganisation.class)
    @JoinColumn(name = "mfr_org_id")
    Organisation mfrOrg;

    @CreationTimestamp
    private Instant createdOn;

    Instant packetReceivedOn;
    @Column(name = "co_relation_id", columnDefinition = "UUID")
    UUID coRelationId;


    Float latitude;
    Float longitude;
    Float altitude;
    Float speed;
    Float brg;
    Float pdop;
    Float hdop;
    Float vdop;
    Float viewSats;
    Float trackSats;

    @Column(name = "geo_point_zm", columnDefinition = "geography(PointZM,4326)")
    Point geoPointZM;
    @Column(name = "gps_distance", columnDefinition = "float")
    Float gpsDistance;
    @Column(name = "gps_acceleration", columnDefinition = "float")
    Float gpsAcceleration;
    @Column(name = "delta_altitude", columnDefinition = "float")
    Float deltaAltitude;
    @Column(name = "delta_bearing", columnDefinition = "float")
    Float deltaBearing;
    Float gradient;
}
