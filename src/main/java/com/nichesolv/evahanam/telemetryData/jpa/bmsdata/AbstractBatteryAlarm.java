package com.nichesolv.evahanam.telemetryData.jpa.bmsdata;

import com.nichesolv.evahanam.telemetryData.AbstractPersistableEntity;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MappedSuperclass;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@MappedSuperclass
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AbstractBatteryAlarm extends AbstractPersistableEntity<BatteryAlarmIdx> {

    Boolean alarmPinSet;
    Boolean protectionPinSet;

    @EmbeddedId
    BatteryAlarmIdx id;

    @ManyToOne
    @JoinColumn(name = "vehicle_id")
    Vehicle vehicle;

    @ManyToOne(targetEntity = CustomOrganisation.class)
    @JoinColumn(name = "owner_org_id")
    Organisation ownerOrg;

    @ManyToOne(targetEntity = CustomOrganisation.class)
    @JoinColumn(name = "mfr_org_id")
    Organisation mfrOrg;

    @Override
    public boolean isNew() {
        return true;
    }
}
