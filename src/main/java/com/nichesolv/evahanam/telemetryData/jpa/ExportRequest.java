package com.nichesolv.evahanam.telemetryData.jpa;

import com.nichesolv.evahanam.telemetryData.enums.ExportReportRequestStatus;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.Instant;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Table(name = "export_request", indexes = {
        @Index(name = "export_request_imei_idx", columnList = "imei"),
        @Index(name = "export_request_veh_id_idx", columnList = "vehicle_id"),
})
public class ExportRequest {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;


    @ManyToOne
    @JoinColumn(name = "vehicle_id",foreignKey = @ForeignKey(name = "fk_vehicle_id"))
    Vehicle vehicle;

    String imei;

    @Enumerated(EnumType.STRING)
    ExportReportRequestStatus status;

    String url;

    @CreationTimestamp
    Instant modifiedOn;

    @DateTimeFormat
    Instant startTime;

    @DateTimeFormat
    Instant endTime;

    public Long getStartTime() {
        return startTime.toEpochMilli();
    }

    public Long getEndTime() {
        return endTime.toEpochMilli();
    }
}

