package com.nichesolv.evahanam.telemetryData.jpa;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.Instant;
import java.util.UUID;

@AllArgsConstructor
@NoArgsConstructor
@Data
@MappedSuperclass
public abstract class AbstractBatteryData {

    @EmbeddedId
    TelemetryIdx telemetryIdx;

    @ManyToOne
    Vehicle vehicle;

    @ManyToOne(targetEntity = CustomOrganisation.class)
    @JoinColumn(name = "owner_org_id")
    Organisation ownerOrg;

    @ManyToOne(targetEntity = CustomOrganisation.class)
    @JoinColumn(name = "mfr_org_id")
    Organisation mfrOrg;

    @CreationTimestamp
    private Instant createdOn;

    Instant packetReceivedOn;

    @Column(name = "co_relation_id", columnDefinition = "UUID")
    UUID coRelationId;

    Float batteryVolt;
    Float soc;
    Float soh;
    Float current;
    Float cellVoltMin;
    Float cellVoltMax;
    Float temperatureMin;
    Float temperatureMax;
    Integer chgCycleCount;
    Integer dsgCycleCount;
    Integer remainingCapacity;
    Float mosfetTemperature;
    Float discharge;
    @Column(name = "temperature_median")
    Float temperatureMedian;
    @Column(name = "cell_voltage_median")
    Float cellVoltageMedian;
    Integer alarm;
}
