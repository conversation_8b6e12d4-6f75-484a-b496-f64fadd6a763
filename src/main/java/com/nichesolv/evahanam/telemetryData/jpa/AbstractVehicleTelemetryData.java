package com.nichesolv.evahanam.telemetryData.jpa;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;

@MappedSuperclass
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AbstractVehicleTelemetryData implements Serializable {

    @EmbeddedId
    TelemetryIdx telemetryIdx;

    @ManyToOne
    Vehicle vehicle;


    @ManyToOne(targetEntity = CustomOrganisation.class)
    @JoinColumn(name = "owner_org_id")
    Organisation ownerOrg;

    @ManyToOne(targetEntity = CustomOrganisation.class)
    @JoinColumn(name = "mfr_org_id")
    Organisation mfrOrg;


    //    @Version
//    Integer version;
    @CreationTimestamp
    private Instant createdOn;

    Instant packetReceivedOn;

    @Column(name = "co_relation_id")
    UUID coRelationId;

    /**
     * vehicle digital output data
     */

    Boolean doUsr1;
    Boolean doUsr2;

    /**
     * digital input data
     */

    Boolean diUsr1;
    Boolean diUsr2;

    Boolean diTamper;
    Boolean diMainPower;

    Boolean diIgnition;

    Boolean diMotion;

    /**
     * analog input data
     */

    Float aiTemperature;
    /**
     * Analog Input Voltage Input, this is important as this is vehicle battery voltage, State of
     * Charge can be calculated from this.
     */

    Float aiVoltageInput;
    /**
     * Analog System Voltage this is device internal battery voltage.
     */

    Float aiSystemVoltage;

    Float aiVbuck;

    Integer aiVusr1;

    Integer aiVusr2;
}
