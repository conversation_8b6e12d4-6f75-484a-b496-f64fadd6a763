package com.nichesolv.evahanam.telemetryData.enums;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum LocationTableColumn {
    ALTITUDE,
    BRG,
    HDOP,
    LATITUDE,
    LONGITUDE,
    PDOP,
    SPEED,
    TRACK_SATS,
    VDOP,
    VIEW_SATS;

    public static List<String> getColumns() {
        return Stream.of(LocationTableColumn.values())
                .map(e -> e.name().toLowerCase())
                .collect(Collectors.toList());
    }
}
