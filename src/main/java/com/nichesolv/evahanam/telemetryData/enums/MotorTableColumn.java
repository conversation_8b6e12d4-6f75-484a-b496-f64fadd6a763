package com.nichesolv.evahanam.telemetryData.enums;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum MotorTableColumn {
    MOTOR_BRAKE,
    MOTOR_CRUISE,
    MOTOR_DC_CURRENT,
    MOTOR_DC_VOLTAGE,
    MOTOR_DRIVING_MODE,
    MOTOR_FAULT_FEEDBACK,
    MOTOR_MCS_TEMPERATURE,
    MOTOR_PARKING_SIGN,
    MOTOR_READY_SIGN,
    MOTOR_REGENERATION,
    MOTOR_REVERSE,
    MOTOR_SIDE_STAND,
    MOTOR_SPEED,
    MOTOR_TEMPERATURE,
    MOTOR_THROTTLE,
    DI_MOTION,
    DI_IGNITION;;

    public static List<String> getColumns(){
        return Stream.of(MotorTableColumn.values())
                .map(e->e.name().toLowerCase())
                .collect(Collectors.toList());
    }
}
