package com.nichesolv.evahanam.telemetryData.enums;

import lombok.ToString;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@ToString
public enum TelemetryTableColumn {
    AI_SYSTEM_VOLTAGE,
    AI_TEMPERATURE,
    AI_VBUCK,
    AI_VOLTAGE_INPUT,
    AI_VUSR1,
    AI_VUSR2,
    DI_IGNITION,
    DI_MAIN_POWER,
    DI_MOTION,
    DI_TAMPER,
    DI_USR1,
    DI_USR2,
    DO_USR1,
    DO_USR2,
   ;

    public static List<String> getColumns() {
        return Stream.of(TelemetryTableColumn.values())
                .map(e -> e.name().toLowerCase())
                .collect(Collectors.toList());
    }
}
