package com.nichesolv.evahanam.exceptions;


import com.nichesolv.evahanam.common.exception.ActiveSubscriptionPlanException;
import com.nichesolv.evahanam.common.exception.DataFrequencyPlanException;
import com.nichesolv.evahanam.common.exception.ImageException;
import com.nichesolv.evahanam.common.exception.ParsingPlanException;
import com.nichesolv.evahanam.common.exception.*;
import com.nichesolv.evahanam.evApp.exception.*;
import com.nichesolv.evahanam.exceptions.model.VehicleTestRunningExceptionResponse;
import com.nichesolv.evahanam.promos.exception.PromotionNotFoundException;
import com.nichesolv.evahanam.telemetryData.exception.RabbitMQException;
import com.nichesolv.evahanam.vehicle.exception.*;
import com.nichesolv.evahanam.vehicleModel.exception.DriveModeException;
import com.nichesolv.evahanam.vehicleModel.exception.PartAttributeNotFoundException;
import com.nichesolv.evahanam.vehicleModel.exception.PartModelException;
import com.nichesolv.evahanam.vehicleModel.exception.VehicleModelException;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.exception.*;
import com.nichesolv.nds.exception.UserNotFoundException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.io.IOException;
import java.nio.file.AccessDeniedException;

@ControllerAdvice
public class GlobalExceptionsHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception ex) {
        logger.error("Unexpected error occurred", ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Internal Server Error: " + ex.getMessage());
    }

    @ExceptionHandler(value = {IllegalStateException.class, IOException.class,
            IllegalArgumentException.class, IllegalStateException.class, FleetException.class, InvalidVehicleLocationException.class
            , CurrentTimeLimitExceedException.class, ImageException.class
    })
    protected ResponseEntity<Object> handleIllegalStates(RuntimeException ex, WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getMessage());
        return handleExceptionInternal(ex, responseBody, new HttpHeaders(), HttpStatus.BAD_REQUEST,
                request);
    }

    @ExceptionHandler(value = {UserAlreadyPairedException.class, VehicleAlreadyPairedException.class, UserProfileExistException.class,VehicleRiderAssociationAlreadyExistException.class,
            DriveModeException.class, ChassisNumberException.class, DataIntegrityViolationException.class})
    protected ResponseEntity<Object> handleConflicts(RuntimeException ex, WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getMessage());
        return handleExceptionInternal(ex, responseBody, new HttpHeaders(), HttpStatus.CONFLICT,
                request);
    }


    @ExceptionHandler(value = {UserVehicleConnectionException.class, UserProfileNotFoundException.class,EmergencyTypeException.class,EmergencyEventException.class,
            UserNotFoundException.class, VehicleNotFoundException.class, OrganisationNotFoundException.class,
            VehicleRegistrationDetailsNotFound.class, BatteryStatusNotFoundException.class, VehicleNotFoundInRangeException.class, PartAttributeNotFoundException.class, PromotionNotFoundException.class

    })
    protected ResponseEntity<Object> handleNotFound(RuntimeException ex, WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getMessage());
        return handleExceptionInternal(ex, responseBody, new HttpHeaders(), HttpStatus.NOT_FOUND,
                request);
    }


    @ExceptionHandler(value = {HttpClientErrorException.Forbidden.class})
    protected ResponseEntity<Object> handleForbiddenException(RuntimeException ex,
                                                              WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getMessage());
        return handleExceptionInternal(ex, responseBody, new HttpHeaders(), HttpStatus.FORBIDDEN,
                request);
    }

    @ExceptionHandler(value = {HttpClientErrorException.Unauthorized.class})
    protected ResponseEntity<Object> handleUnauthorizedException(RuntimeException ex,
                                                                 WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getMessage());
        return handleExceptionInternal(ex, responseBody, new HttpHeaders(), HttpStatus.UNAUTHORIZED,
                request);
    }


    @ExceptionHandler(value = {PartException.class, VehicleException.class, VehicleModelException.class
            , PartModelException.class, ColorException.class, VehicleTestException.class , ActiveSubscriptionPlanException.class ,RabbitMQException.class})
    protected ResponseEntity<Object> handleErrors(RuntimeException ex, WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getMessage());
        return handleExceptionInternal(ex, responseBody, new HttpHeaders(), HttpStatus.BAD_REQUEST, request);
    }


    @ExceptionHandler(value = {TripSummaryException.class})
    protected ResponseEntity<Object> handleTripSummaryErrors(RuntimeException ex, WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getMessage());
        return handleExceptionInternal(ex, responseBody, new HttpHeaders(), HttpStatus.CREATED, request);
    }

    @ExceptionHandler(value = {VehicleTestRunningException.class})
    protected ResponseEntity<Object> handleVehicleTestExists(VehicleTestRunningException ex) {
        VehicleTestRunningExceptionResponse responseBody = new VehicleTestRunningExceptionResponse(ex.getMessage(), ex.getTestId(), ex.getStatus(), ex.getStartTime());
        return new ResponseEntity(responseBody, HttpStatus.BAD_REQUEST);
    }

//
//    @NonNull
//    @Override
//    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
//        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getFieldError().getDefaultMessage());
//        return new ResponseEntity<Object>(responseBody, HttpStatus.BAD_REQUEST);
//    }
//
//    @Override
//    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
//        ExceptionResponseModel responseBody = new ExceptionResponseModel("Unexpected characters in input field");
//        return new ResponseEntity<Object>(responseBody, HttpStatus.BAD_REQUEST);
//    }

    @ExceptionHandler(value = {AccessDeniedException.class})
    protected ResponseEntity<Object> accessDenied(RuntimeException ex, WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getMessage());
        return handleExceptionInternal(ex, responseBody, new HttpHeaders(), HttpStatus.FORBIDDEN,
                request);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getFieldError().getDefaultMessage());

        return handleExceptionInternal(ex, responseBody, headers, HttpStatus.BAD_REQUEST, request);
    }

    @ExceptionHandler(value = {V2AnalyticsInternalServerErrorException.class})
    protected ResponseEntity<Object> handleInternalErrors(Exception ex, Object body, HttpHeaders headers, HttpStatusCode statusCode, WebRequest request) {
        return handleExceptionInternal(ex, body, headers, HttpStatus.INTERNAL_SERVER_ERROR, request);
    }

//    @Override
//    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
//        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getFieldError().getDefaultMessage());
//        return new ResponseEntity(responseBody, HttpStatus.BAD_REQUEST);
//    }

    @ExceptionHandler(value = {DateValidationException.class})
    protected ResponseEntity<Object> handleDateValidations(RuntimeException ex) {
        ExceptionModel responseBody = new ExceptionModel(ex.getMessage(), TestStatus.ABORTED.name());
        return new ResponseEntity(responseBody, HttpStatus.BAD_REQUEST);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel("Unexpected characters in input field");
        return new ResponseEntity<Object>(responseBody, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = {ParsingPlanException.class, DataFrequencyPlanException.class, VehicleModelSubscriptionNotFoundException.class,
            OrganisationSubscriptionNotFoundException.class ,SubscriptionException.class})
    public ResponseEntity<Object> handleSubscriptionPlanExceptions(RuntimeException ex, WebRequest request) {
        ExceptionResponseModel responseBody = new ExceptionResponseModel(ex.getMessage());
        return handleExceptionInternal(ex, responseBody, new HttpHeaders(), HttpStatus.BAD_REQUEST, request);
    }

}
