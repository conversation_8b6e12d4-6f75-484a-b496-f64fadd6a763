package com.nichesolv.evahanam.cache.handler;


import com.nichesolv.evahanam.cache.service.VehicleCacheRefreshService;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component("ADVERTISING_NAME_VEHICLE")
@Slf4j
public class AdvertisingNameVehicleHandler implements EventHandler {

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private VehicleCacheRefreshService vehicleCacheRefreshService;

    @Override
    public void handleEvent(String[] keyValueArray, String eventName) {
        if (keyValueArray.length > 1 && eventName.equals("keymiss")) {
            String advertisingName = keyValueArray[1];

            vehicleRepository.findByDeviceAdvertisingName(advertisingName).ifPresentOrElse(vehicle -> {
                vehicleCacheRefreshService.initializeOrUpdateAdvertingNameToEncryptionKeyCache(List.of(vehicle));
            }, () -> {
                log.debug("ADVERTISING_NAME_VEHICLE:: advertising name not found : {}, eventName : {}", advertisingName, eventName);
            });
        }
    }
}
