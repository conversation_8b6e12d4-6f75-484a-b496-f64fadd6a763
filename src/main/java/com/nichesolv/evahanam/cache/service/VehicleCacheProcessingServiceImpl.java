package com.nichesolv.evahanam.cache.service;

import com.nichesolv.evahanam.cache.dto.vehicle.VehicleDto;
import com.nichesolv.evahanam.cache.dto.vehicle.VehiclePartDto;
import com.nichesolv.evahanam.cache.enums.KeyType;
import com.nichesolv.evahanam.common.jpa.ActiveVehicleSubscriptionPlan;
import com.nichesolv.evahanam.common.repository.ActiveVehicleSubscriptionPlanRepository;
import com.nichesolv.evahanam.common.repository.ImageRepository;
import com.nichesolv.evahanam.cache.dto.partModelDto.*;
import com.nichesolv.evahanam.vehicle.jpa.Part;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleRegistrationDetails;
import com.nichesolv.evahanam.vehicle.repository.VehicleRegistrationDetailsRepository;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.jpa.DriveModeSpeed;
import com.nichesolv.evahanam.vehicleModel.jpa.PartModel;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VehicleCacheProcessingServiceImpl implements VehicleCacheProcessingService {

    @Autowired
    ImageRepository imageRepository;

    @Autowired
    VehicleRegistrationDetailsRepository vehicleRegistrationDetailsRepository;

    @Override
    public Set<PartModelDto> getPartModels(Set<PartModel> partModels) {
        return partModels.stream().map((partModel -> {
            Set<PartModelAttributeDto> partAttributes = partModel.getPartModelAttributes().stream().map((attribute) -> {
                return new PartModelAttributeDto(attribute.getId(), attribute.getName(), attribute.getType(), attribute.getValue());
            }).collect(Collectors.toSet());
            return new PartModelDto(partModel.getId(), partModel.getName(), partModel.getPartType(), partModel.getDescription(), (Optional.ofNullable(partModel.getParentPartModelId()).isPresent() ? partModel.getParentPartModelId().getId() : null), (Optional.ofNullable(partModel.getManufacturer()).isPresent() ? partModel.getManufacturer().getId() : null), partAttributes);
        })).collect(Collectors.toSet());
    }

    @Override
    public VehicleModelVariantDto getVehicleModelVariantCombinations(VehicleModelVariantDto vehicleModelDto, List<PartModelDto> combination) {
        VehicleModelVariantDto newVehicleModelDto = new VehicleModelVariantDto();
        newVehicleModelDto.setName(vehicleModelDto.getName());
        newVehicleModelDto.setPartModels(new HashSet<>(combination));
        newVehicleModelDto.setId(vehicleModelDto.getId());
        newVehicleModelDto.setPartType(vehicleModelDto.getPartType());
        newVehicleModelDto.setDescription(vehicleModelDto.getDescription());
        newVehicleModelDto.setParentPartModelId(vehicleModelDto.getParentPartModelId());
        newVehicleModelDto.setPartModelAttributes(vehicleModelDto.getPartModelAttributes());
        newVehicleModelDto.setModelNo(vehicleModelDto.getModelNo());
        newVehicleModelDto.setWeightUom(vehicleModelDto.getWeightUom());
        newVehicleModelDto.setGrossWeight(vehicleModelDto.getGrossWeight());
        newVehicleModelDto.setNetWeight(vehicleModelDto.getNetWeight());
        newVehicleModelDto.setImages(vehicleModelDto.getImages());
        newVehicleModelDto.setDriveModes(vehicleModelDto.getDriveModes());
        return newVehicleModelDto;
    }

    @Override
    public List<VehicleModelVariantDto> getVehicleModelVariants(List<VehicleModel> vehicleModelVariants) {
        List<VehicleModelVariantDto> vehicleModelVariantDtos = new ArrayList<>();
        vehicleModelVariantDtos = vehicleModelVariants.stream().map(
                (e) -> {

                    VehicleModelVariantDto vehicleModelVariantDto = new VehicleModelVariantDto();
                    vehicleModelVariantDto.setId(e.getId());
                    vehicleModelVariantDto.setName(e.getName());
                    vehicleModelVariantDto.setPartType(e.getPartType());
                    vehicleModelVariantDto.setId(e.getId());
                    vehicleModelVariantDto.setParentPartModelId(Optional.ofNullable(e.getParentPartModelId()).isPresent() ? e.getParentPartModelId().getId() : null);
                    vehicleModelVariantDto.setManufacturerId(Optional.ofNullable(e.getManufacturer()).isPresent() ? e.getManufacturer().getId() : null);
                    vehicleModelVariantDto.setDescription(e.getDescription());

                    Set<PartModelAttributeDto> vehicleAttributes = e.getPartModelAttributes().stream().map((attribute) -> {
                        return new PartModelAttributeDto(attribute.getId(), attribute.getName(), attribute.getType(), attribute.getValue());
                    }).collect(Collectors.toSet());

                    vehicleModelVariantDto.setPartModelAttributes(vehicleAttributes);
                    vehicleModelVariantDto.setModelNo(e.getModelNo());
                    vehicleModelVariantDto.setGrossWeight(e.getGrossWeight());
                    vehicleModelVariantDto.setNetWeight(e.getNetWeight());
                    vehicleModelVariantDto.setWeightUom(e.getWeightUom());

                    Set<DriveModeSpeedDto> driveModeSpeeds = this.getDriveModeSpeeds(e.getDriveModes());

                    vehicleModelVariantDto.setDriveModes(driveModeSpeeds);

                    AtomicReference<Long> colorId = new AtomicReference<>();
                    Set<PartModelDto> partModels =
                            e.getPartModels().stream().map((partModel -> {
                                Set<PartModelAttributeDto> partAttributes = partModel.getPartModelAttributes().stream().map((attribute) -> {
                                    return new PartModelAttributeDto(attribute.getId(), attribute.getName(), attribute.getType(), attribute.getValue());
                                }).collect(Collectors.toSet());
                                if (partModel.getPartType().equals(PartType.COLOR)) {
                                    colorId.set(partModel.getId());
                                }
                                return new PartModelDto(partModel.getId(), partModel.getName(), partModel.getPartType(), partModel.getDescription(), (Optional.ofNullable(partModel.getParentPartModelId()).isPresent() ? partModel.getParentPartModelId().getId() : null), (Optional.ofNullable(partModel.getManufacturer()).isPresent() ? partModel.getManufacturer().getId() : null), partAttributes);
                            })).collect(Collectors.toSet());

                    if (Optional.ofNullable(colorId.get()).isPresent() && Optional.ofNullable(e.getColorImages()).isPresent()) {
                        Set<VehicleImageDto> images = e.getColorImages().entrySet().stream().filter(colorModelId -> Objects.equals(colorModelId.getKey().getId(), colorId.get())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)).values().stream().flatMap(Set::stream).map(value -> imageRepository.findById(value).get()).map(value -> new VehicleImageDto(value.getId(), value.getUrl(), value.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant(), value.getTag(), value.getDescription())).collect(Collectors.toSet());
                        vehicleModelVariantDto.setImages(images);
                    }

                    vehicleModelVariantDto.setPartModels(partModels);
                    return vehicleModelVariantDto;
                }
        ).toList();
        return vehicleModelVariantDtos;
    }

    @Autowired
    ActiveVehicleSubscriptionPlanRepository vehicleSubscriptionPlanRepository;

    public Map<String, Long> initalizeOrUpdateVehicleSubscriptions(Vehicle vehicle) {
        Map<String, Long> vehicleSubscriptionsMap = new HashMap<>();
        try {
            if (vehicle == null || vehicle.getImei() == null) {
                log.warn("Skipping null or invalid vehicle during subscription initialization.");
                return null;
            }

            Optional<ActiveVehicleSubscriptionPlan> vehicleSubscriptions = vehicleSubscriptionPlanRepository.findByVehicle(vehicle);

            if (vehicleSubscriptions.isEmpty()) {
                log.info("No active subscription plans found for vehicle with IMEI: {}", vehicle.getImei());
            }


            vehicleSubscriptions.ifPresent(sub -> {
                if (sub.getDataParsingPlan() != null) {
                    vehicleSubscriptionsMap.put(KeyType.SUBSCRIPTIONS_FIELDS.name(), sub.getDataParsingPlan().getId());
                } else {
                    log.warn("Data parsing plan is null for vehicle with IMEI: {}", vehicle.getImei());
                }

                if (sub.getDataFrequencyPlan() != null) {
                    vehicleSubscriptionsMap.put(KeyType.SUBSCRIPTIONS_FREQUENCY.name(), sub.getDataFrequencyPlan().getId());
                } else {
                    log.warn("Data frequency plan is null for vehicle with IMEI: {}", vehicle.getImei());
                }
            });

            if (vehicleSubscriptionsMap.isEmpty()) {
                log.info("No valid subscription data found for vehicle with IMEI: {}", vehicle.getImei());
            }
        } catch (Exception e) {
            log.error("Unexpected error while processing vehicle with IMEI: {}. Error: {}",
                    vehicle != null ? vehicle.getImei() : "unknown", e.getMessage(), e);
        }
        return vehicleSubscriptionsMap;

    }

    @Override
    public VehicleDto getVehicle(Vehicle vehicle) {

        VehicleDto vehicleDto = new VehicleDto();
        vehicleDto.setId(vehicle.getId());
        vehicleDto.setImei(vehicle.getImei());
        vehicleDto.setVehicleModelId(vehicle.getVehicleModel().getId());
        Optional<VehicleRegistrationDetails> vehicleRegistrationDetails = vehicleRegistrationDetailsRepository.findByVehicleId(vehicle.getId());
        vehicleRegistrationDetails.ifPresent(registrationDetails -> vehicleDto.setRegNo(registrationDetails.getRegistrationNumber()));
        vehicleDto.setOwnerId(Optional.ofNullable(vehicle.getOwner()).isPresent() ? vehicle.getOwner().getId() : null);
        vehicleDto.setDealershipId(Optional.ofNullable(vehicle.getDealership()).isPresent() ? vehicle.getDealership().getId() : null);
        vehicleDto.setManufacturerId(Optional.ofNullable(vehicle.getManufacturer()).isPresent() ? vehicle.getManufacturer().getId() : null);
        vehicleDto.setOperationStatus(vehicle.getOperationStatus());
        vehicleDto.setChassisNumber(vehicle.getChassisNumber());
        vehicleDto.setEncryptionKey(vehicle.getEncryptionKey());
        vehicleDto.setDeviceAdvertisingName(vehicle.getDeviceAdvertisingName());
        vehicleDto.setVehicleState(vehicle.getVehicleState());

        Set<Part> parts = vehicle.getVehicleParts();
        Set<VehiclePartDto> vehiclePartDtos = parts.stream().map(e -> {
            VehiclePartDto vehiclePartDto = new VehiclePartDto();
            vehiclePartDto.setId(e.getId());
            vehiclePartDto.setPartModelId(e.getPartModel().getId());
            vehiclePartDto.setPartType(e.getPartType());
            vehiclePartDto.setSerialNumber(e.getSerialNumber());
            vehiclePartDto.setSerialNumber(e.getSerialNumber());
            vehiclePartDto.setManufacturerId(e.getManufacturer().getId());
            return vehiclePartDto;
        }).collect(Collectors.toSet());

        vehicleDto.setParts(vehiclePartDtos);
        vehicleDto.setSubscriptions(initalizeOrUpdateVehicleSubscriptions(vehicle));

        return vehicleDto;
    }

    @Override
    public Set<DriveModeSpeedDto> getDriveModeSpeeds(Set<DriveModeSpeed> driveModeSpeeds) {
        return driveModeSpeeds.stream().map((driveModeSpeed -> {
            DriveModeDto driveMode = new DriveModeDto(driveModeSpeed.getDriveMode().getId(), driveModeSpeed.getDriveMode().getName(), driveModeSpeed.getDriveMode().getOrganisation().getId());
            return new DriveModeSpeedDto(driveModeSpeed.getId(), driveMode, driveModeSpeed.getMin(), driveModeSpeed.getMax(), driveModeSpeed.getUnit(), driveModeSpeed.getRange(), driveModeSpeed.getHexColor());
        })).collect(Collectors.toSet());
    }

    @Override
    public List<List<PartModelDto>> generatePartModelCombinations(List<List<PartModelDto>> partLists) {
        List<List<PartModelDto>> combinations = new ArrayList<>();
        generatePartModelCombinationsRecursive(partLists, 0, new ArrayList<>(), combinations);
        return combinations;
    }

    private void generatePartModelCombinationsRecursive(List<List<PartModelDto>> partLists, int index,
                                                        List<PartModelDto> currentCombination,
                                                        List<List<PartModelDto>> allCombinations) {
        if (index == partLists.size()) {
            allCombinations.add(new ArrayList<>(currentCombination));  // Create a deep copy of the combination
            return;
        }

        for (PartModelDto partModel : partLists.get(index)) {
            currentCombination.add(partModel);
            generatePartModelCombinationsRecursive(partLists, index + 1, currentCombination, allCombinations);
            currentCombination.remove(currentCombination.size() - 1);  // Backtrack
        }
    }

}
