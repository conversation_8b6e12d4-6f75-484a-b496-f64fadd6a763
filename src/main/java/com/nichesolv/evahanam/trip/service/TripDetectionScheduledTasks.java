package com.nichesolv.evahanam.trip.service;

import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.repository.TripRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.function.Predicate;

@Component
@Slf4j
public class TripDetectionScheduledTasks {

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    TripRepository tripRepository;

    @Autowired
    TripSummaryService tripSummaryService;

//    @Transactional
////    @Scheduled(cron = "0 */10 * * * ?", zone = "UTC")
//    public void startTripDetectionCron() {
//        log.info("===================trip start detection cron ran at================= {}", Instant.now());
//        List<Vehicle> tripsInProgressVehicles = tripRepository.findByTripTypeAndSummaryPopulationStatus(TripType.AUTOMATIC, TestRideSummaryPopulationStatus.IN_PROGRESS).stream().map(Trip::getVehicle).toList();
//        Predicate<Vehicle> isVehicleTripInprogress = vehicle -> !tripsInProgressVehicles.contains(vehicle);
//        List<Vehicle> vehicles = vehicleRepository.findAllByOperationStatus(OperationStatus.ACTIVE).filter(isVehicleTripInprogress).toList();
//        log.info("vehicles count {}",vehicles.size());
////        tripSummaryService.startAutomaticTrips(vehicles);
//
//    }
//
////    @Scheduled(cron = "0 */10 * * * ?", zone = "UTC")
//    public void endTripDetectionCron() {
//        log.info("===================trip end detection cron ran at================= {}", Instant.now());
//        List<Trip> inProgressTrips = tripRepository.findByTripTypeAndSummaryPopulationStatus(TripType.AUTOMATIC, TestRideSummaryPopulationStatus.IN_PROGRESS);
//        log.info("count of inProgress trips {}",inProgressTrips.size());
////        tripSummaryService.endAutomaticTrips(inProgressTrips);
//    }
}
