package com.nichesolv.evahanam.trip.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nichesolv.evahanam.common.enums.EventType;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import com.nichesolv.evahanam.common.repository.ActiveVehicleSubscriptionPlanRepository;
import com.nichesolv.evahanam.common.repository.MyCustomOrganisationRepository;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.evApp.jpa.UserVehicleConnection;
import com.nichesolv.evahanam.evApp.repository.UserVehicleConnectionRepository;
import com.nichesolv.evahanam.evApp.service.UserTripService;
import com.nichesolv.evahanam.telemetryData.repository.LocationDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.MotorDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.trip.dto.*;
import com.nichesolv.evahanam.trip.dto.cumulative.ChordDiagramResponseDto;
import com.nichesolv.evahanam.trip.dto.cumulative.CumulativeResponse;
import com.nichesolv.evahanam.trip.dto.cumulative.input.CumulativeInputDto;
import com.nichesolv.evahanam.trip.dto.cumulative.input.FilterDto;
import com.nichesolv.evahanam.trip.dto.cumulative.input.FilterInputDto;
import com.nichesolv.evahanam.trip.dto.cumulative.response.*;
import com.nichesolv.evahanam.trip.dto.mapper.TripDtoMapper;
import com.nichesolv.evahanam.trip.dto.mapper.V2TripDtoMapper;
import com.nichesolv.evahanam.trip.dto.tripsummary.*;
import com.nichesolv.evahanam.trip.dto.v2.LocationDto;
import com.nichesolv.evahanam.trip.dto.v2.V2TestRideInput;
import com.nichesolv.evahanam.trip.enums.*;
import com.nichesolv.evahanam.trip.jpa.*;
import com.nichesolv.evahanam.trip.rabbitmq.dto.*;
import com.nichesolv.evahanam.trip.repository.*;
import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.evahanam.vehicle.dto.TyrePressureDto;
import com.nichesolv.evahanam.vehicle.enums.UpdateSource;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.events.FetchTripDetailsEvent;
import com.nichesolv.evahanam.vehicle.exception.VehicleException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRunningMetricsRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicleModel.repository.VehicleModelRepository;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.exception.*;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;

import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomUserRepository;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.text.DecimalFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Stream;

@Service
@Slf4j
public class TripSummaryServiceImpl implements TripSummaryService {

    @Autowired
    private TripRepository tripRepository;
    @Autowired
    TripDtoMapper tripSummaryMapper;
    @Autowired
    CustomUserRepository userRepository;
    @Autowired
    VehicleTestRepository vehicleTestRepository;
    @Autowired
    TripDetailsRepo tripDetailsRepo;
    @Autowired
    BrakeDataRepository brakeDataRepository;
    @Autowired
    LocationDataRepository locationDataRepository;



    @Value("${rabbitmq.queue.trip-details-input}")
    String TRIP_DETAILS_INPUT_QUEUE;
    @Autowired
    WebClient.Builder client;
    @Value("${location.service.endpoint}")
    String locationServiceAPIUrl;

    @Autowired
    VehicleRepository vehicleRepository;
    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;
    @Value("${v2.analytics.endpoint}")
    String v2AnalyticsEndpoint;
    @Value("${tyre.pressure.endpoint}")
    String tripTyrePressureEndPoint;
    @Autowired
    V2TripDtoMapper tripDtoMapper;
    @Autowired
    AccelerationDecelerationDataRepository accelerationDecelerationDataRepository;
    @Autowired
    StopIntervalRepository stopIntervalRepository;
    @Autowired

    EnergyConsumptionRepository energyConsumptionRepository;
    @Value("${application.base-url}")
    String baseUrl;
    @Autowired
    MyCustomOrganisationRepository organisationRepository;

    @Autowired
    private VehicleDataRepository vehicleDataRepository;

    @Autowired
    MotorDataRepository motorDataRepository;

    @Autowired
    TurnDataRepository turnDataRepository;

    @Autowired
    UserVehicleConnectionRepository vehicleConnectionRepository;

    @Autowired
    MyCustomOrganisationRepository myCustomOrganisationRepository;

    @Autowired
    VehicleModelRepository vehicleModelRepository;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;

    @Autowired
    EventDurationConstantsRepository eventDurationConstantsRepository;

    @Autowired
    ActiveVehicleSubscriptionPlanRepository activeVehicleSubscriptionPlanRepository;


    @Autowired
    UserVehicleConnectionRepository userVehicleConnectionRepository;

    @Autowired
    UserVehicleTripRepository userVehicleTripRepository;

    @Autowired
    UserVehicleTripDetailsRepository userVehicleTripDetailsRepository;


    @Autowired
    VehicleRunningMetricsRepository vehicleRunningMetricsRepository;

    @Autowired
    ApplicationEventPublisher eventPublisher;

    @Autowired
    UserTripService userTripService;


    private final List<String> tripAttributeNames = Arrays.asList("dischargePerCharge", "meanSpeed", "startSoc", "endSoc", "totalGpsDistance", "rideDuration", "stopDuration", "maxGpsSpeed", "maxAccelerationMps2",
            "runningDuration", "maxMotorCurrentA", "totalBrakeDuration", "brakeDistance", "statisticalSpeedMode", "maxSpeed", "brakesPerKm",
            "averageBrakeDistance", "meanBrakeDuration","minAccelerationMps2");

    public TripSummaryServiceImpl(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    @Override
    public TripSummaryDto getTripSummary(Long testId, CustomOrganisation organisation) {
        Trip trip = tripRepository.findByVehicleTestId(testId)
                .orElseThrow(() -> new TripSummaryException(evMessageBundle.getMessage("TRIP_DETAILS_NOT_FOUND", testId)));
        if (trip.getSummaryPopulationStatus() == TestRideSummaryPopulationStatus.COMPLETED)
            return tripSummaryMapper.entityToDto(trip);
        else {
            throw new TripSummaryException(trip.getSummaryPopulationStatus().name());
        }
    }

    public List<Trip> findByImeiAndStatusAndTripType(Vehicle vehicle, TestRideSummaryPopulationStatus status, TripType tripType) {
        log.debug("Finding trips for vehicle: {}, status: {}, tripType: {}", vehicle.getImei(), status, tripType);
        return tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(vehicle.getImei(), null, status, tripType);
    }


    @Override
    public Page<TripProjection> getTripList(String identifier, Pageable page) {
        String imei = vehicleRepository.getVehicleIdentifiers(identifier).get().getVehImei();
        page = PageRequest.of(page.getPageNumber(), page.getPageSize(), Sort.by("vehicleTestEndTime").descending().and(Sort.by("vehicleTestId").descending()));
        return tripRepository.findByImei(imei, page);
    }

    /**
     * This method gives the index of the tripStops list to get the end time for the trip.
     *
     * @param tripStart startTime of the automated trip.
     * @param tripStops list of stopTimes
     * @param lo        lower index of the tripStops list
     * @param hi        higher index of the tripStops list
     * @return The index of the tripStops list, to get the tripStop time for that particular tripStart time
     */
    public int findTripEnd(Instant tripStart, LinkedList<Instant> tripStops, int lo, int hi) {
        if (lo > hi) {
            return lo;
        }
        int mid = (lo + hi) / 2;
        if (tripStart.isBefore(tripStops.get(mid))) {
            return findTripEnd(tripStart, tripStops, 0, mid - 1);
        } else if (tripStart.isAfter(tripStops.get(mid))) {
            return findTripEnd(tripStart, tripStops, mid + 1, hi);
        } else {
            return mid;
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void processAutomatedTrips(@NotNull TripHistoryResponse tripHistoryResponse) {
        log.debug("Processing automated trips");
        String imei = tripHistoryResponse.getImei();
        LocalDateTime now = LocalDateTime.now();

        Vehicle vehicle = vehicleRepository.findByImei(imei).orElseThrow(() -> new VehicleException("Vehicle= " + imei + " does not exist"));
        CustomOrganisation vehicleOwner = (CustomOrganisation) vehicle.getOwner();
        // Find all existing trips. Should not return more than one trip.
        List<Trip> existingTrips = tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(
                vehicle.getImei(), null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC
        );

        // Let's loop over all the start trips.
        List<Instant> tripStarts = tripHistoryResponse.getTripStarts();

        // let's sort the end trips array
        LinkedList<Instant> tripStops = new LinkedList<>(tripHistoryResponse.getTripEnds());
        // The logic would work better on a sorted list of stops and starts.
        Collections.sort(tripStops);
        Collections.sort(tripStarts);

        if (imei == null) {
            log.error("Empty imei received in trip history response, check the trip detection service for errors.");
        } else if (existingTrips.size() > 1) {
            log.error("Number of active trips for imei={} cannot be more than 1. Current active trips={}", imei, existingTrips.size());
        } else {
            // Vehicle exists.

            List<Trip> trips = new ArrayList<>();
            // Get the first trip.
            if (!existingTrips.isEmpty()) {
                Trip existingTrip = existingTrips.get(0);
                existingTrip.setUpdatedOn(Instant.now());
                trips.add(existingTrip);
            }


            // Add all trips to the trips list.
            if (tripStarts != null && tripStarts.size() > 0) {
                for (Instant startTime : tripStarts) {
                    if (existingTrips.isEmpty() || !existingTrips.get(0).getStartTime().equals(startTime)) {

                        Trip newTrip = Trip.builder()
                                .imei(imei)
                                .vehicle(vehicle)
                                .startTime(startTime)
                                .summaryPopulationStatus(TestRideSummaryPopulationStatus.IN_PROGRESS)
                                .tripType(TripType.AUTOMATIC)// Let's mark it in progress.
                                .createdOn(Instant.now())
                                .updatedOn(Instant.now())
                                .build();
                        trips.add(newTrip);
                    }
                }
            }

            Optional<UserVehicleConnection> activeVehicleConnection = vehicleConnectionRepository
                    .findUserByVehicleAndEndOn(vehicle, null).stream().findFirst();
            //getting all the 1 hour recent connections for the vehicle
            List<UserVehicleConnection> recentConnections = vehicleConnectionRepository
                    .findRecentConnections(vehicle.getId(), now.minusHours(1), now);

            //for B2C if recent connection is not then we are adding the active one
            if (recentConnections.isEmpty() && activeVehicleConnection.isPresent() && vehicleOwner.getOrganisationType() == OrganisationType.B2CCUSTOMER) {
                recentConnections.add(activeVehicleConnection.get());
            }
            log.info("active rider id : {}", activeVehicleConnection.map(UserVehicleConnection::getUser).map(CustomUser::getId).orElse(null));
            log.info("no of recentConnections : {}", recentConnections.size());

            log.debug("Number of trips={}", trips.size());

            //disconnecting the active user-vehicle connection if there are no trips and connection is older than 1 hour and user is not a B2CCUSTOMER
            if (trips.isEmpty()) {
                activeVehicleConnection.map(c -> {
                    List<String> organisationTypes = myCustomOrganisationRepository.findOrganisationTypeByUserId(c.getUser().getId());
                    if (c.getStartOn().isBefore(now.minusHours(1)) && !organisationTypes.contains(OrganisationType.B2CCUSTOMER.name())) {
                        c.setEndOn(now);
                        vehicleConnectionRepository.save(c);
                        log.info("disconnecting the user, userId : {} ", c.getUser().getId());
                    }
                    return c;
                });
            }


            // Let's write these trips to the db.
            for (Trip trip : trips) {
                int index = findTripEnd(trip.getStartTime(), tripStops, 0, tripStops.size() - 1);
                log.info("StartTime={}, Index={}", trip.getStartTime(), index);
                if (index < tripStops.size()) {
                    trip.setEndTime(tripStops.get(index));
                    trip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.COMPLETED);
                    tripStops.remove(index);
                }
                try {
                    log.debug("Saving trip");
                    CustomUser user = trip.getUser();
                    if (trip.getSummaryPopulationStatus().equals(TestRideSummaryPopulationStatus.COMPLETED) && trip.getUser() == null) {// null check : in case if user already assigned for a IN_PROGRESS trip (existingTrips)
                        // check for suitable connection from recent connection list
                        for (UserVehicleConnection connection : recentConnections) {
                            if (connection.getStartOn().isBefore(LocalDateTime.ofInstant(trip.getStartTime(), ZoneId.systemDefault()))
                                    || connection.getEndOn().isAfter(LocalDateTime.ofInstant(trip.getEndTime(), ZoneId.systemDefault()))) {
                                user = connection.getUser();
                            }
                        }
                    } else if (trip.getUser() == null) {
                        //setting the active connection for the IN_PROGRESS or FAILED trips and for the COMPLETED trips if there is no recent connection
                        user = activeVehicleConnection.map(UserVehicleConnection::getUser).orElse(null);
                    }
                    //assigning the user to the trip only if user is B2BCUSTOMER or B2CCUSTOMER
                    if (user != null) {
                        List<String> organisationTypes = myCustomOrganisationRepository.findOrganisationTypeByUserId(user.getId());
                        if (!organisationTypes.contains(OrganisationType.B2BCUSTOMER.name()) && !organisationTypes.contains(OrganisationType.B2CCUSTOMER.name())) {
                            log.debug("user is not a B2B CUSTOMER nor B2C CUSTOMER");
                            user = null;
                        }
                    }
                    log.debug("trip and user {} {}", trip.getId(), user);
                    trip.setUser(user);

                    Trip savedTrip = null;
                    Optional<Trip> tripExists = tripRepository.findByStartTimeAndEndTimeAndImeiAndSummaryPopulationStatusAndTripType(trip.getStartTime(), trip.getEndTime(), trip.getImei(), trip.getSummaryPopulationStatus(), trip.getTripType());
                    if (tripExists.isEmpty()) {
                        savedTrip = tripRepository.save(trip);
                        log.info("Trip={} saved with status={}", savedTrip.getId(), savedTrip.getSummaryPopulationStatus());
                    } else {
                        savedTrip = tripExists.get();
                        log.info("Duplicate Trip={} found with same status={}", savedTrip.getId(), savedTrip.getSummaryPopulationStatus());
                    }

                    // Calculate location, distance and save the trip.
                    if (savedTrip.getSummaryPopulationStatus().equals(TestRideSummaryPopulationStatus.COMPLETED)) {
                        //saving the location details of the trip
                        getStartAndEndLocationData(savedTrip);
                    }
                    log.debug("saved the start and end location data");
                    // Trip details.
                    log.info("Calculating total distance, imei={}, trip={}", savedTrip.getImei(), savedTrip.getId());
                    Float totalDistance = this.motorDataRepository.getDistanceTravelled(savedTrip.getImei(), savedTrip.getStartTime(), savedTrip.getEndTime());
                    log.info("Total distance: imei={}, trip={}, distance={}", savedTrip.getImei(), savedTrip.getId(), totalDistance);
                    if (totalDistance != null) {
                        TripIdx idx = createTripIdx(savedTrip);
                        TripDetails details = new TripDetails();
                        idx.setFieldName("totalDistance");
                        details.setTripIdx(idx);
                        details.setVehicle(vehicle);
                        details.setFieldValue(String.valueOf(totalDistance));
                        details.setDataType("FLOAT");
                        details.setVehicle(idx.getTrip().getVehicle());
                        log.debug("Saving trip details.");
                        tripDetailsRepo.save(details);
                    }
                    // Get location details
                    log.debug("Saving location data for trip");
                    // getStartAndEndLocationData(trip);
                } catch (Exception e) {
                    log.error("Trip={}, failed with reason={}", trip, e.getMessage());
                    trip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.FAILED);
                    tripRepository.save(trip);
                }
            }
        }


    }

    @Override
    @Transactional
    public Trip saveTrip(TripDto tripDto) {
        log.info(tripDto.toString());
        Trip trip = new Trip();
        try {
            TripIdx tripIdx = new TripIdx();
            tripIdx.setImei(tripDto.getImei());
            tripIdx.setTimestamp(tripDto.getEndTime());
            Vehicle vehicle = vehicleRepository.findByImei(tripDto.getImei())
                    .orElseThrow(() -> new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));
            if (Optional.ofNullable(tripDto.getUserId()).isPresent()) {
                CustomUser user = userRepository.findById(tripDto.getUserId())
                        .orElseThrow(() -> new UsernameNotFoundException(evMessageBundle.getMessage("USER_NOT_FOUND") + tripDto.getUserId()));
                trip.setUser(user);
            }
            trip.setImei(tripDto.getImei());
            trip.setStartTime(tripDto.getStartTime());
            trip.setEndTime(tripDto.getEndTime());
            if (Optional.ofNullable(tripDto.getTestId()).isPresent()) {
                VehicleTest vehicleTest = vehicleTestRepository.findById(tripDto.getTestId())
                        .orElseThrow(() -> new VehicleTestException(evMessageBundle.getMessage("TEST_NOT_FOUND")));
                trip.setVehicleTest(vehicleTest);
            }
            trip.setVehicle(vehicle);
            trip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.IN_PROGRESS);
            trip.setMfrOrg(vehicle.getManufacturer());
            trip.setOwnerOrg(vehicle.getOwner());
            trip.setTripType(TripType.MANUAL);
            trip.setCreatedOn(Instant.now());
            trip.setUpdatedOn(Instant.now());
            trip = tripRepository.save(trip);
            getStartAndEndLocationData(trip);
            processTripDetails(trip);
        } catch (Exception e) {
            log.info("An Exception occurred {}", e.getMessage());
        }
        return trip;
    }

    public void processTripDetails(Trip trip) {
        if (Optional.ofNullable(trip.getVehicleTest()).isPresent()) {
            V2TestRideInput inputModel = new V2TestRideInput(trip.getImei(), trip.getId(), trip.getStartTime().toEpochMilli(), trip.getEndTime().toEpochMilli(), UUID.randomUUID());
            log.info("Publishing FetchTripDetailsEvent for tripId: {}", trip.getId());
            eventPublisher.publishEvent(new FetchTripDetailsEvent(this, inputModel));
        }
    }

    /**
     * This method will save or updates the automatic trips
     *
     * @param vehicle           vehicle
     * @param runningTime       totalRunningTime in seconds
     * @param stoppageTime      totalStoppageTime in seconds
     * @param status            trip status
     * @param vehicleStatusTime time
     * @param inProgressTrip    InProgress trip for a vehicle
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void saveAutomaticTrips(Vehicle vehicle, Long runningTime, Long stoppageTime, TestRideSummaryPopulationStatus status, Instant vehicleStatusTime, List<Trip> inProgressTrip) {
        log.debug("inside the saveAutomaticTrips");
        log.debug("vehicle {}, runningTime {}, stoppageTime{}, status {}, vehicleStatusTime {}, inProgressTrip size {}", vehicle.getImei(), runningTime, stoppageTime, status.name(), vehicleStatusTime, inProgressTrip.size());
        Trip trip = !inProgressTrip.isEmpty() ? inProgressTrip.get(0) : new Trip(); // considering there will be only one IN_PROGRESS automatic trip for a vehicle
        Instant now = Instant.now();
        try {
            //saving or updating the trip
            if (status.equals(TestRideSummaryPopulationStatus.IN_PROGRESS)) {
                log.info("trip started for vehicle {}", vehicle.getImei());
                Instant tripStartTime = vehicleStatusTime.minusSeconds(runningTime); //subtracting the vehicleStatusTime with minTripDuration, so that we get exact time when its started running
                trip.setImei(vehicle.getImei());
                trip.setVehicle(vehicle);
                trip.setStartTime(tripStartTime);
                trip.setOwnerOrg(vehicle.getOwner());
                trip.setMfrOrg(vehicle.getManufacturer());
                trip.setCreatedOn(now);
                trip.setUpdatedOn(now);
                trip.setTripType(TripType.AUTOMATIC);
                trip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.IN_PROGRESS);
                trip.setUpdateSource(UpdateSource.TRIP_CRON);
                Trip savedTrip = tripRepository.save(trip);
                //saving the odometer reading at trip_start
                saveAutomaticTripDetails(savedTrip, TripDetailsFields.odoAtTripStart);
                //starting  or saving the user_vehicle_trip
                try {
                    saveUserVehicleTrip(savedTrip, null, UserVehicleTripEvents.TRIP_START);
                } catch (Exception e) {
                    log.error("Error occured in saving the user trips ", e.getMessage());
                }

            } else if (status.equals(TestRideSummaryPopulationStatus.COMPLETED)) {
                Instant tripEndTime = vehicleStatusTime.minusSeconds(stoppageTime);//subtracting the vehicleStatusTime with stopDuration, so that we get exact time when its stopped
                trip.setUpdatedOn(now);
                trip.setEndTime(tripEndTime);
                trip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.COMPLETED);

                log.debug("saving the trip location details");
                getStartAndEndLocationData(trip);

                //saving the trip total distance
                saveAutomaticTripDetails(trip, TripDetailsFields.totalDistance);


                log.info("trip {} ended for vehicle {}", trip.getId(), vehicle.getImei());
                tripRepository.save(trip);
                saveGpsMotorDistanceAndTyrePressureThreshold(trip);

                //updating or ending the user_vehicle_trip
                try {
                    saveUserVehicleTrip(trip, null, UserVehicleTripEvents.TRIP_END);
                } catch (Exception e) {
                    log.error("Error occurred while updating the user trip ", e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Error updating the trip with message " + e.getMessage());
            trip.setUpdatedOn(now);
            trip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.FAILED);
            tripRepository.save(trip);
        }
    }

    public void saveGpsMotorDistanceAndTyrePressureThreshold(Trip trip) {
        V2TestRideInput tripInput = new V2TestRideInput();
        tripInput.setTripId(trip.getId());
        tripInput.setStartTime(trip.getStartTime().toEpochMilli());
        tripInput.setEndTime(trip.getEndTime().toEpochMilli());
        tripInput.setImei(trip.getImei());
        tripInput.setCorrelationId(UUID.randomUUID());
        TyrePressureDto tyrePressureDto = getRequestBodySpec(tripTyrePressureEndPoint)
                .body(Mono.just(tripInput), V2TestRideInput.class)
                .accept(MediaType.APPLICATION_JSON).retrieve()
                .onStatus(HttpStatusCode::is4xxClientError, res -> res.bodyToMono(String.class).map(V2AnalyticsBadRequestException::new))
                .onStatus(HttpStatusCode::is5xxServerError, res -> res.bodyToMono(String.class).map(V2AnalyticsInternalServerErrorException::new))
                .bodyToMono(TyrePressureDto.class)
                .doOnError(throwable -> log.info("error inside the tyrePressure analysis : " + throwable))
                .block();

        saveTripDetail(trip, TripDetailsFields.motorDistance, tyrePressureDto.getTotalMotorDistance());
        saveTripDetail(trip, TripDetailsFields.gpsDistance, tyrePressureDto.getTotalGpsDistance());
        saveTripDetail(trip, TripDetailsFields.tyrePressureThreshold, tyrePressureDto.getTyreThreshold());
    }

    public void saveTripDetail(Trip trip, TripDetailsFields tripDetailsFields, String fieldValue) {
        TripIdx idx = createTripIdx(trip);
        TripDetails tripDetails = new TripDetails();
        tripDetails.setVehicle(trip.getVehicle());
        idx.setFieldName(tripDetailsFields.name());
        tripDetails.setTripIdx(idx);
        tripDetails.setDataType("FLOAT");
        tripDetails.setFieldValue(String.valueOf(fieldValue));
        tripDetailsRepo.save(tripDetails);
        log.debug("field name :{} and field value :{}", tripDetails.getTripIdx().getFieldName(), tripDetails.getFieldValue());
    }

    /**
     * This method checks if there is any status updated in last 24 hours by VEHICLE_STATUS_UPDATE_CRON and marks existing trips as ARCHIVED and creates new vehicle-trips and user-vehicle-trips by calling saveDataDelayedVehicleTrips and saveDataDelayedUserTrips methods respectively
     *
     * @param imei                     imei
     * @param currentTime              current_time (the time when CRON runs)
     * @param dataFrequencyPlanDetails - data_frequency_plan_details of the vehicle
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void saveDataDelayedAutomaticTrips(String imei, Instant currentTime, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        Vehicle vehicle = vehicleRepository.findByImei(imei).orElseThrow(() -> new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND_WITH_IMEI", imei)));
        EventDurationConstants tripDurationConstants = eventDurationConstantsRepository.findByOrganisationAndConstantAndType((CustomOrganisation) vehicle.getManufacturer(), EventConstants.STOP_DURATION, EventType.TRIP).orElseThrow(() -> new TripConstantNotFoundException(evMessageBundle.getMessage("TRIP_CONSTANT_NOT_FOUND", EventConstants.STOP_DURATION.name(), vehicle.getManufacturer().getOrganisationProfile().getName())));
        Instant startTime = currentTime.minusSeconds(86400);//minus 24 hr
        Instant endTime = currentTime;
        log.debug("inside saveDataDelayAutomaticTrips with currentTime {} finalStartTime {}", currentTime, startTime);

        List<VehicleStatusTimeWindowProjection> delayedStatusTimeWindows = vehicleStatusRepository.getDataDelayTimeWindowsByImeiAndUpdateSourceAndUpdatedOnBetween(imei, UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON.name(), startTime, endTime, tripDurationConstants.getDuration());

        //remove this
        log.debug("delayedStatusTimeWindows for the imei {}", imei);



        log.debug("number of delayedStatusTimeWindows {}", delayedStatusTimeWindows.size());

        TripsToArchiveAndRecalculatedTimeWindows tripsToArchiveAndRecalculatedTimeWindows = getTripsTobeArchivedAndNewTimeWindows(imei, delayedStatusTimeWindows, tripDurationConstants.getDuration());
        List<Trip> tripsToArchive = tripRepository.findAllById(tripsToArchiveAndRecalculatedTimeWindows.getTripIdsToArchive());
        List<TripRecalculationTimeWindowDto> recalculatedTimeWindows = tripsToArchiveAndRecalculatedTimeWindows.getTimeWindows();
        log.info("number of trips to be archived {}", tripsToArchive.size());

        List<UserVehicleTrip> userVehicleTripsToBeArchived = new ArrayList<>();
        tripsToArchive = tripsToArchive.stream().map(e -> {
            e.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.ARCHIVED);
            e.setUpdateSource(UpdateSource.TRIP_DATA_DELAY_CRON);
            List<UserVehicleTrip> userVehicleTrips = userVehicleTripRepository.findByTrip(e).stream().map(userVehicleTrip -> {
                userVehicleTrip.setStatus(TestRideSummaryPopulationStatus.ARCHIVED);
                return userVehicleTrip;
            }).toList();
            userVehicleTripsToBeArchived.addAll(userVehicleTrips);
            return e;
        }).toList();
        log.debug("number of user_vehicle_trips to be ARCHIVED {}", userVehicleTripsToBeArchived.size());
        tripRepository.saveAll(tripsToArchive);
        userVehicleTripRepository.saveAll(userVehicleTripsToBeArchived);

        List<Trip> recalculatedTrips = new ArrayList<>();
        recalculatedTimeWindows.forEach(e -> {
            try {
                recalculatedTrips.addAll(saveDataDelayedVehicleTrips(imei, e.getStartTime(), e.getEndTime(), dataFrequencyPlanDetails));
            } catch (Exception exception) {
                log.error("Error While saving the data delayed vehicle trips for imei {} startingTimeWindow {} endingTimeWindow {} with message {}", imei, e.getStartTime(), e.getEndTime(), exception.getMessage());
            }
        });


        recalculatedTrips.forEach(e -> {
            //saving the trip location details
            getStartAndEndLocationData(e);

            //saving the totalDistance in trip_details table
            try {
                saveAutomaticTripDetails(e, TripDetailsFields.totalDistance);
                saveGpsMotorDistanceAndTyrePressureThreshold(e);
            } catch (Exception ex) {
                log.error("Error While Saving the trip_details for the trip {} with message {}", e.getId(), ex.getMessage());
            }
        });

        //saving the user_vehicle_trips for the respective trips (vehicle-trips)
        try {
            saveDataDelayedUserTrips(recalculatedTrips);
        } catch (Exception e) {
            log.error("Error while saving the user_vehicle_trips with message {}", e.getMessage());
        }

    }

    /**
     * This method finds all the nearer trips for the data-delayed time windows and returns the re-calculated time-windows for the re-creation of trips
     *
     * @param imei                 - imei
     * @param dataDelayTimeWindows - time windows at which the data-delay has happened and vehicle_status is update by VEHICLE_STATUS_DATA_DELAY_CRON
     * @param thresholdInterval    - STOP_DURATION
     * @return - list of trips to archive and list new time windows for the re-calculation of trips
     */
    @Override
    public TripsToArchiveAndRecalculatedTimeWindows getTripsTobeArchivedAndNewTimeWindows(String imei, List<VehicleStatusTimeWindowProjection> dataDelayTimeWindows, Integer thresholdInterval) {
        log.debug("inside getTripsTobeArchivedAndNewTimeWindows for the imei {}", imei);
        TripsToArchiveAndRecalculatedTimeWindows tripsToArchiveAndRecalculatedTimeWindows = new TripsToArchiveAndRecalculatedTimeWindows();
        Stack<TripRecalculationTimeWindowDto> alteredTimeWindows = new Stack<>();
        Set<Long> tripIdsToArchive = new HashSet<>();
        dataDelayTimeWindows.forEach(e -> {
            Instant finalStartTime = e.getMinTimestamp();
            Instant finalEndTime = e.getMaxTimestamp();

            //checking if the time window is nearer to previous altered time window
            if (!alteredTimeWindows.isEmpty()) {
                TripRecalculationTimeWindowDto recentAlteredTimeWindow = alteredTimeWindows.pop();
                if (finalStartTime.isBefore(recentAlteredTimeWindow.getEndTime()) || finalStartTime.minusSeconds(thresholdInterval).isBefore(recentAlteredTimeWindow.getEndTime())) {
                    finalStartTime = recentAlteredTimeWindow.getStartTime();
                } else {
                    alteredTimeWindows.push(recentAlteredTimeWindow);
                }
            }
            Instant t1 = e.getMinTimestamp().minusSeconds(thresholdInterval); //doing minus 10 min (STOP_DURATION) to minTimestamp -> to find the nearest trips
            Instant t2 = e.getMaxTimestamp().plusSeconds(thresholdInterval); //doing Plus 10 min (STOP_DURATION) to maxTimestamp -> to find the nearest trips
            List<Trip> nearestTrips = tripRepository.getNearestTripsOfDataDelayInterval(imei, TestRideSummaryPopulationStatus.COMPLETED.name(), TripType.AUTOMATIC.name(), t1, t2);
            for (Trip trip : nearestTrips) {
                if (finalStartTime.isAfter(trip.getStartTime())) {
                    finalStartTime = trip.getStartTime();
                }
                if (finalEndTime.isBefore(trip.getEndTime())) {
                    finalEndTime = trip.getEndTime();
                }
            }
            tripIdsToArchive.addAll(nearestTrips.stream().map(Trip::getId).toList());
            alteredTimeWindows.push(new TripRecalculationTimeWindowDto(finalStartTime, finalEndTime));
        });
        tripsToArchiveAndRecalculatedTimeWindows.setTripIdsToArchive(tripIdsToArchive.stream().toList());
        tripsToArchiveAndRecalculatedTimeWindows.setTimeWindows(alteredTimeWindows);
        return tripsToArchiveAndRecalculatedTimeWindows;
    }

    /**
     * This method will re-create the trip for the re-calculated time intervals and saves it in the DB
     *
     * @param imei                     imei
     * @param startTime                start_time
     * @param endTime                  end_time
     * @param dataFrequencyPlanDetails data_frequency_plan_details of that vehicle
     * @return return list of Trips saved in trip table
     */
    @Override
    public List<Trip> saveDataDelayedVehicleTrips(String imei, Instant startTime, Instant endTime, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        Short computationFrequency = dataFrequencyPlanDetails.getComputationFrequency();
        Instant now = Instant.now();
        log.debug("inside saveDataDelayVehicleTrips with imei {} startTime {} endTime {}", imei, startTime, endTime);
        Vehicle vehicle = vehicleRepository.findByImei(imei).orElseThrow(() -> new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND_WITH_INFO", imei)));
        EventDurationConstants minTripDurationConstant = eventDurationConstantsRepository.findByOrganisationAndConstantAndType((CustomOrganisation) vehicle.getManufacturer(), EventConstants.MIN_TRIP_DURATION,EventType.TRIP).orElseThrow(() -> new TripConstantNotFoundException(evMessageBundle.getMessage("TRIP_CONSTANT_NOT_FOUND", EventConstants.MIN_TRIP_DURATION.name(), vehicle.getManufacturer().getId())));
        EventDurationConstants stopDurationConstant = eventDurationConstantsRepository.findByOrganisationAndConstantAndType((CustomOrganisation) vehicle.getManufacturer(), EventConstants.STOP_DURATION,EventType.TRIP).orElseThrow(() -> new TripConstantNotFoundException(evMessageBundle.getMessage("TRIP_CONSTANT_NOT_FOUND", EventConstants.STOP_DURATION.name(), vehicle.getManufacturer().getId())));
        endTime = endTime.plusSeconds(stopDurationConstant.getDuration()); //adding 10 minn to end_time, end_time = end_time + stop_duration , doing this because for re-calulating of the trips i am getting the list of vehicle_status between t1 and t2, here t2 might be the tripEndTime, vehicle will be runinning till t2, so need to add stop_duration
        Long vehicleRunningCount = 0L;
        Long vehicleStoppageCount = 0L;
        Instant previousStatusTime = null;
        Trip inProgressTrip = null;
        Stack<Trip> trips = new Stack<>();

        try (Stream<VehicleStatus> vehicleStatusStream = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(imei, startTime, endTime)) {
            for (VehicleStatus vehicleStatus : vehicleStatusStream.toList()) {
                if (VehicleState.RUNNING.equals(vehicleStatus.getVehicleState())) {
                    vehicleStoppageCount = 0L;
                    Long timeDifference = previousStatusTime != null ? Duration.between(previousStatusTime, vehicleStatus.getVehicleStatusIdx().getTimestamp()).toSeconds() : 0;
                    vehicleRunningCount += timeDifference;
                } else {
                    vehicleRunningCount = 0L;
                    Long timeDifference = previousStatusTime != null ? Duration.between(previousStatusTime, vehicleStatus.getVehicleStatusIdx().getTimestamp()).toSeconds() : 0;
                    vehicleStoppageCount += timeDifference;
                }
                previousStatusTime = vehicleStatus.getVehicleStatusIdx().getTimestamp();
                if (vehicleRunningCount >= minTripDurationConstant.getDuration() && inProgressTrip == null) {
                    //save the trip as in_progress

                    inProgressTrip = new Trip();
                    Instant tripStartTime = vehicleStatus.getVehicleStatusIdx().getTimestamp().minusSeconds(vehicleRunningCount - computationFrequency); //subtracting the vehicleStatusTime with minTripDuration, so that we get exact time when its started running
                    inProgressTrip.setImei(vehicle.getImei());
                    inProgressTrip.setVehicle(vehicle);
                    inProgressTrip.setStartTime(tripStartTime);
                    inProgressTrip.setOwnerOrg(vehicle.getOwner());
                    inProgressTrip.setMfrOrg(vehicle.getManufacturer());
                    inProgressTrip.setCreatedOn(now);
                    inProgressTrip.setUpdatedOn(now);
                    inProgressTrip.setTripType(TripType.AUTOMATIC);
                    inProgressTrip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.IN_PROGRESS);
                    inProgressTrip.setUpdateSource(UpdateSource.TRIP_DATA_DELAY_CRON);

                    //adding into trips
                    trips.push(inProgressTrip);
                } else if (vehicleStoppageCount >= stopDurationConstant.getDuration() && inProgressTrip != null) {
                    //complete the trip
                    inProgressTrip = trips.pop();
                    Instant tripEndTime = vehicleStatus.getVehicleStatusIdx().getTimestamp().minusSeconds(vehicleStoppageCount - computationFrequency);//subtracting the vehicleStatusTime with stopDuration, so that we get exact time when its stopped
                    inProgressTrip.setUpdatedOn(now);
                    inProgressTrip.setEndTime(tripEndTime);
                    inProgressTrip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.COMPLETED);

                    trips.push(inProgressTrip);
                    inProgressTrip = null;
                }
            }
        }
        log.info("saving the data delay trips {}", trips.size());
        //saving the data-delay trips into DB
        tripRepository.saveAll(trips);
        return trips;
    }

    /**
     * This method will create the user_vehicle_trips for the list of vehicle_trips based on the user_vehicle_connection's start_on and end_on
     *
     * @param vehicleTrips list of data delayed vehicle_trips, which is created by saveDataDelayedVehicleTrips method
     */
    @Override
    public void saveDataDelayedUserTrips(List<Trip> vehicleTrips) {
        log.debug("inside saveDataDelayUserTrips with vehicleTrips size {}", vehicleTrips.size());
        ZoneId zoneId = ZoneId.systemDefault();
        List<UserVehicleTrip> userTrips = new ArrayList<>();
        vehicleTrips.forEach(trip -> {
            LocalDateTime tripStartTime = LocalDateTime.ofInstant(trip.getStartTime(), zoneId);
            LocalDateTime tripEndTime = LocalDateTime.ofInstant(trip.getEndTime(), zoneId);
            List<UserVehicleConnection> userVehicleConnections = userVehicleConnectionRepository.findByVehicleAndStartOnOrEndOnBetweenOrderByStartOn(trip.getVehicle().getId(), tripStartTime, tripEndTime);
            userVehicleConnections.forEach(userConnection -> {
                LocalDateTime connectionStartOn = userConnection.getStartOn();
                LocalDateTime connectionEndOn = userConnection.getEndOn();
                Instant userTripStartTime = null;
                Instant userTripEndTime = null;

                //deciding the userTripStartTime
                if (connectionStartOn.isAfter(tripStartTime)) {
                    userTripStartTime = connectionStartOn.atZone(zoneId).toInstant();
                } else if (connectionStartOn.isBefore(tripStartTime) || connectionStartOn.equals(tripStartTime)) {
                    userTripStartTime = tripStartTime.atZone(zoneId).toInstant();
                }

                //deciding the userTripEndTime
                if (connectionEndOn == null || connectionEndOn.isAfter(tripEndTime) || connectionEndOn.equals(tripEndTime)) {
                    userTripEndTime = tripEndTime.atZone(zoneId).toInstant();
                } else if (connectionEndOn.isBefore(tripEndTime)) {
                    userTripEndTime = connectionEndOn.atZone(zoneId).toInstant();
                }

                UserVehicleTrip userVehicleTrip = UserVehicleTrip.builder()
                        .userVehicleConnection(userConnection)
                        .startTime(userTripStartTime)
                        .endTime(userTripEndTime)
                        .status(TestRideSummaryPopulationStatus.COMPLETED)
                        .trip(trip)
                        .build();

                //getting the start location for the user_vehicle_trip
                LocationDataDto startLocationData = getStartAndEndLocation(userTripStartTime, null, trip.getImei()).getStartLocation();
                userVehicleTrip = userTripStartLocationMapper(userVehicleTrip, startLocationData);

                //getting the end location for the user_vehicle_trip
                LocationDataDto endLocationData = getStartAndEndLocation(null, userTripEndTime, trip.getImei()).getEndLocation();
                userVehicleTrip = userTripEndLocationMapper(userVehicleTrip, endLocationData);

                userTrips.add(userVehicleTrip);

            });
        });
        //saving the user_vehicle_trips
        log.debug("saving the user vehicle trips for data delay {}", userTrips.size());
        userVehicleTripRepository.saveAll(userTrips);

        //saving the user_trip_details
        try {
            saveAutomaticUserTripDetails(userTrips, TripDetailsFields.totalDistance);
        } catch (Exception e) {
            log.error("Error while saving automaticUserTripDetails with message {}", e.getMessage());
        }
    }

    private void saveAutomaticTripDetails(Trip trip, TripDetailsFields fieldName) {
        Float totalDistance = locationDataRepository.getTotalDistanceTravelledBetweenTimeAndImei(trip.getStartTime(),trip.getEndTime(),trip.getImei());
        log.debug("saving {} as {}", fieldName, totalDistance);
        TripIdx idx = createTripIdx(trip);
        TripDetails details = new TripDetails();
        details.setVehicle(trip.getVehicle());
        idx.setFieldName(fieldName.name());
        details.setTripIdx(idx);
        details.setDataType("FLOAT");
        details.setVehicle(idx.getTrip().getVehicle());
        details.setFieldValue(String.valueOf(totalDistance));
        tripDetailsRepo.save(details);
    }

    /**
     * This method decides to start the user_trip based on the event
     *
     * @param trip       IN_PROGRESS vehicleTrip
     * @param connection ACTIVE user connection
     * @param event
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void saveUserVehicleTrip(Trip trip, UserVehicleConnection connection, UserVehicleTripEvents event) {
        List<UserVehicleTrip> userTrips = new ArrayList<>();
        TripDetailsFields fieldName = null;
        switch (event) {
            case TRIP_START -> {
                userTrips = startUserTripOnVehicleTripStartMapper(trip);
            }
            case TRIP_END -> {
                userTrips = endUserTripOnVehicleTripEndMapper(trip);
            }
            case USER_CONNECTED -> {
                UserVehicleTrip userTrip = startUserTripOnUserConnectionMapper(connection);
                userTrips = userTrip != null ? List.of(userTrip) : List.of();
            }
            case USER_DISCONNECTED -> {
                UserVehicleTrip userTrip = endUserTripOnUserDisconnectionMapper(connection);
                userTrips = userTrip != null ? List.of(userTrip) : List.of();
            }
        }
        userVehicleTripRepository.saveAll(userTrips);
        log.info(" {} user_vehicle_trips got saved based on the event {}", userTrips.size(), event.name());
        saveAutomaticUserTripDetails(userTrips, TripDetailsFields.totalDistance);
    }

    /**
     * This method starts the user_trip when the vehicle trip starts and there is ACTIVE user_vehicle connection
     *
     * @param vehicleTrip IN_PROGRESS vehicle_trip
     * @return returns list of user_vehicle_trip based on the number of users connected to the vehicle
     */
    List<UserVehicleTrip> startUserTripOnVehicleTripStartMapper(Trip vehicleTrip) {
        List<UserVehicleTrip> userTrips = new ArrayList<>();
        List<UserVehicleConnection> connections = userVehicleConnectionRepository.findUserByVehicleAndEndOn(vehicleTrip.getVehicle(), null);
        log.info("number of active connections for the vehicleTrip {} are {}", vehicleTrip.getId(), connections.size());
        connections.forEach(e -> {
            Instant userTripStartTime = vehicleTrip.getStartTime();
            UserVehicleTrip userVehicleTrip = UserVehicleTrip.
                    builder()
                    .userVehicleConnection(e)
                    .trip(vehicleTrip)
                    .startTime(userTripStartTime)
                    .status(TestRideSummaryPopulationStatus.IN_PROGRESS)
                    .build();

            //get the start location data
            LocationDataDto startLocationData = getStartAndEndLocation(userTripStartTime, null, vehicleTrip.getImei()).getStartLocation();
            userVehicleTrip = userTripStartLocationMapper(userVehicleTrip, startLocationData);

            userTrips.add(userVehicleTrip);
        });
        return userTrips;
    }

    /**
     * This method updates the IN_PROGRESS user_trip to COMPLETED when vehicle trip ends
     *
     * @param vehicleTrip COMPLETD Trip
     * @return returns COMPLETED user_trip
     */
    List<UserVehicleTrip> endUserTripOnVehicleTripEndMapper(Trip vehicleTrip) {
        List<UserVehicleTrip> userTrips = new ArrayList<>();
        List<UserVehicleTrip> inProgressAndCompletedUserTrips = userVehicleTripRepository.findByTripAndStatus(vehicleTrip, TestRideSummaryPopulationStatus.IN_PROGRESS);
        inProgressAndCompletedUserTrips.addAll(userVehicleTripRepository.findByTripAndStatus(vehicleTrip, TestRideSummaryPopulationStatus.COMPLETED));
        inProgressAndCompletedUserTrips.forEach(e -> {
            Instant userTripEndTime = !e.getStatus().equals(TestRideSummaryPopulationStatus.IN_PROGRESS) && e.getEndTime().isBefore(vehicleTrip.getEndTime()) ? e.getEndTime() : vehicleTrip.getEndTime();
            TestRideSummaryPopulationStatus userTripStatus = e.getStartTime().isAfter(vehicleTrip.getEndTime()) ? TestRideSummaryPopulationStatus.FAILED : TestRideSummaryPopulationStatus.COMPLETED;
            e.setEndTime(userTripEndTime);
            e.setStatus(userTripStatus);
            //get the end location for the user trip
            LocationDataDto endLocationData = getStartAndEndLocation(null, vehicleTrip.getEndTime(), vehicleTrip.getImei()).getEndLocation();
            e = userTripEndLocationMapper(e, endLocationData);

            userTrips.add(e);
        });
        return userTrips;
    }

    /**
     * This Method starts the user_trip when user is connected to the vehicle when there is a IN_PROGRESS vehicle_trip
     *
     * @param userVehicleConnection UserVehicleConnection entity
     * @return return IN_PROGRESS user_vehicle_trip
     */
    UserVehicleTrip startUserTripOnUserConnectionMapper(UserVehicleConnection userVehicleConnection) {
        UserVehicleTrip userTrip = null;
        List<Trip> inProgressVehicleTrip = tripRepository.findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(userVehicleConnection.getVehicle().getImei(), null, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC);

        if (inProgressVehicleTrip.size() > 1) {
            log.error("Multiple IN_PROGRESS trips for a vehicle {}", userVehicleConnection.getVehicle().getImei());
        }
        if (!inProgressVehicleTrip.isEmpty()) {
            Instant userTripStartTime = userVehicleConnection.getStartOn().atZone(ZoneId.systemDefault()).toInstant().truncatedTo(ChronoUnit.SECONDS);
            userTrip = new UserVehicleTrip();
            userTrip.setUserVehicleConnection(userVehicleConnection);
            userTrip.setStartTime(userTripStartTime);
            userTrip.setTrip(inProgressVehicleTrip.get(0));//considering there can be only on IN_PROGRESS trip for a vehicle
            userTrip.setStatus(TestRideSummaryPopulationStatus.IN_PROGRESS);

            //get the start location data
            LocationDataDto startLocationData = getStartAndEndLocation(userTripStartTime, null, userVehicleConnection.getVehicle().getImei()).getStartLocation();
            userTrip = userTripStartLocationMapper(userTrip, startLocationData);
        }
        return userTrip;
    }

    /**
     * This method with ends the IN_PROGRESS user_trips when the user disconnects from the vehicle
     *
     * @param userVehicleConnection UserVehicleConnection entity
     * @return returns the updated the IN_PROGRESS user_trip to COMPLETED
     */
    UserVehicleTrip endUserTripOnUserDisconnectionMapper(UserVehicleConnection userVehicleConnection) {
        UserVehicleTrip inProgressUserTrip = null;
        List<UserVehicleTrip> inProgressUserTrips = userVehicleTripRepository.findByUserVehicleConnectionAndStatus(userVehicleConnection, TestRideSummaryPopulationStatus.IN_PROGRESS);//there can be only one IN_PROGRESS user trip at a time
        if (!inProgressUserTrips.isEmpty()) {
            Instant userTripEndTime = userVehicleConnection.getEndOn().atZone(ZoneId.systemDefault()).toInstant().truncatedTo(ChronoUnit.SECONDS);
            inProgressUserTrip = inProgressUserTrips.get(0);
            inProgressUserTrip.setEndTime(userTripEndTime);
            inProgressUserTrip.setStatus(TestRideSummaryPopulationStatus.COMPLETED);

            //getting the end location details
            LocationDataDto endLocationData = getStartAndEndLocation(null, userTripEndTime, inProgressUserTrip.getTrip().getImei()).getEndLocation();
            inProgressUserTrip = userTripEndLocationMapper(inProgressUserTrip, endLocationData);

        }
        return inProgressUserTrip;
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    private void saveAutomaticUserTripDetails(List<UserVehicleTrip> userVehicleTrips, TripDetailsFields fieldName) {
        log.debug("inside saveAutomaticUserTripDetails with userVehicleTrips {} and fieldName {}", userVehicleTrips.size(), fieldName);
        userVehicleTrips.forEach(userVehicleTrip -> {
            if (userVehicleTrip.getEndTime() != null) {
//                Float fieldValue = vehicleRunningMetricsRepository.getTotalDistanceTravelledBetweenTimeAndImei(userVehicleTrip.getStartTime(), userVehicleTrip.getEndTime(), userVehicleTrip.getTrip().getImei());
                Float fieldValue = locationDataRepository.getTotalDistanceTravelledBetweenTimeAndImei(userVehicleTrip.getStartTime(), userVehicleTrip.getEndTime(), userVehicleTrip.getTrip().getImei());
                UserVehicleTripDetails userVehicleTripDetails = new UserVehicleTripDetails();
                UserVehicleTripIdx idx = createUserVehicleTripIdx(userVehicleTrip);
                idx.setFieldName(fieldName.name());
                userVehicleTripDetails.setId(idx);
                userVehicleTripDetails.setDataType("FLOAT");
                userVehicleTripDetails.setFieldValue(String.valueOf(fieldValue));
                userVehicleTripDetailsRepository.save(userVehicleTripDetails);
            }
        });
    }

    /**
     * This method will map LocationDataDto to UserVehicleTrip
     *
     * @param userTrip          UserTrip
     * @param startLocationData LocationDataDto
     * @return UserVehicleTrip
     */
    private UserVehicleTrip userTripStartLocationMapper(UserVehicleTrip userTrip, LocationDataDto startLocationData) {
        if (startLocationData != null) {
            userTrip.setTripStartCity(startLocationData.getCity());
            userTrip.setTripStartState(startLocationData.getState());
            userTrip.setTripStartNeighbourhood(startLocationData.getNeighbourhood());
            userTrip.setTripStartSuburb(startLocationData.getSuburb());
        }
        return userTrip;
    }

    /**
     * This method will map LocationDataDto to UserVehicleTrip
     *
     * @param userTrip        UserTrip
     * @param endLocationData LocationDataDto
     * @return UserVehicleTrip
     */
    private UserVehicleTrip userTripEndLocationMapper(UserVehicleTrip userTrip, LocationDataDto endLocationData) {
        if (endLocationData != null) {
            userTrip.setTripEndCity(endLocationData.getCity());
            userTrip.setTripEndState(endLocationData.getState());
            userTrip.setTripEndNeighbourhood(endLocationData.getNeighbourhood());
            userTrip.setTripEndSuburb(endLocationData.getSuburb());
        }
        return userTrip;
    }


    @Override
    @Transactional
    public void saveGeneratedTripStats(TestRideV2 analyticsData) {
        Long tripId = analyticsData.getTripId();
        log.info("Processing analytics data for trip {}", tripId);

        Trip trip = tripRepository.findById(tripId).orElseThrow(() -> new TripSummaryException(evMessageBundle.getMessage("TRIP_NOT_FOUND", tripId)));

        try {
            // save acceleration, deceleration, stop, and energy data
            accelerationDecelerationDataRepository.saveAll(tripDtoMapper.accelerationDtoToEntity(analyticsData, trip));
            accelerationDecelerationDataRepository.saveAll(tripDtoMapper.decelerationDtoToEntity(analyticsData, trip));
            stopIntervalRepository.saveAll(tripDtoMapper.stopIntervalDtoToEntity(analyticsData, trip));
            energyConsumptionRepository.saveAll(tripDtoMapper.energyConsumptionDtoToEntity(analyticsData, trip));

            // brake stats
            if (analyticsData.getBrakeStat() != null) {
                List<BrakeResponseModel> brakes = analyticsData.getBrakeStat();
                if (!brakes.isEmpty()) {
                    createBreaks(brakes, trip);
                }
            }

            // Turn stats
            if (analyticsData.getTurnStat() != null) {
                List<TurnResponseModel> turns = analyticsData.getTurnStat();
                if (!turns.isEmpty()) {
                    createTurns(turns, trip);
                }
            }

            // Mode wise distance and speed
            List<TripDetails> tripDetails = new ArrayList<>();
            if (analyticsData.getRideSummary() != null) {
                if (analyticsData.getRideSummary().getModeWiseDistance() != null) {
                    tripDetails.addAll(createModeWiseData(
                            analyticsData.getRideSummary().getModeWiseDistance(), trip,"Distance"));
                }
                if (analyticsData.getRideSummary().getModeWiseMeanSpeed() != null) {
                    tripDetails.addAll(createModeWiseData(
                            analyticsData.getRideSummary().getModeWiseMeanSpeed(), trip, "MeanSpeed"));
                }
            }
            log.debug("RideSummary = {}", analyticsData.getRideSummary());
            log.debug("Ranges = {}", analyticsData.getRanges());

            // flatten fields from RideSummary and Ranges
            Map<String, Object> flatFields = new HashMap<>();
            ObjectMapper objectMapper = new ObjectMapper();


            if (analyticsData.getRideSummary() != null) {
                Map<String, Object> rideSummaryMap = objectMapper.convertValue(
                        analyticsData.getRideSummary(), new TypeReference<Map<String, Object>>() {}
                );

                rideSummaryMap.remove("modeWiseDistance");
                rideSummaryMap.remove("modeWiseMeanSpeed");

                flatFields.putAll(rideSummaryMap);
            }

            if (analyticsData.getRanges() != null) {
                flatFields.putAll(objectMapper.convertValue(
                        analyticsData.getRanges(), new TypeReference<Map<String, Object>>() {}));
            }
            log.debug("Flat fields: {}", flatFields);


            for (Map.Entry<String, Object> entry : flatFields.entrySet()) {
                if (entry.getValue() != null) {
                    log.debug("Saving field: {} = {}", entry.getKey(), entry.getValue());

                    TripDetails details = new TripDetails();
                    TripIdx idx = createTripIdx(trip);
                    idx.setFieldName(entry.getKey());
                    details.setTripIdx(idx);
                    details.setVehicle(trip.getVehicle());
                    details.setFieldValue(String.valueOf(entry.getValue()));
                    details.setDataType(findDataType(String.valueOf(entry.getValue())));
                    tripDetails.add(details);
                } else {
                    log.debug("Skipping null field in RideSummary/Ranges: {}", entry.getKey());
                }
            }


            if (!tripDetails.isEmpty()) {
                tripDetailsRepo.saveAll(tripDetails);
            }

            // Mark trip and vehicle test as completed
            trip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.COMPLETED);
            trip.getVehicleTest().setStatus(TestStatus.COMPLETED);

        } catch (Exception e) {
            log.error("Error saving analytics for trip {}: {}", tripId, e.getMessage(), e);
            trip.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.FAILED);
            trip.getVehicleTest().setStatus(TestStatus.ABORTED);
        }
        vehicleTestRepository.save(trip.getVehicleTest());
        tripRepository.save(trip);
    }


    @Async("saveLocationData")
    private void getStartAndEndLocationData(Trip trip) {
        try {
            TripLocationProjection startLocationData = locationDataRepository.getStartLocationDataByTimeStampAndImei(trip.getStartTime(), trip.getImei());
            TripLocationProjection endLocationData = locationDataRepository.getEndLocationDataByTimeStampAndImei(trip.getEndTime(), trip.getImei());
            LocationDataDto startLoc = getLocationDataFromReverseGeoCodingApi(trip, startLocationData);
            LocationDataDto endLoc = getLocationDataFromReverseGeoCodingApi(trip, endLocationData);
            trip.setTripStartCity(startLoc.getCounty());
            trip.setTripEndCity(endLoc.getCounty());
            trip.setTripStartState(startLoc.getState());
            trip.setTripEndState(endLoc.getState());
            trip.setTripStartNeighbourhood(startLoc.getNeighbourhood());
            trip.setTripEndNeighbourhood(endLoc.getNeighbourhood());
            trip.setTripStartSuburb(startLoc.getSuburb());
            trip.setTripEndSuburb(endLoc.getSuburb());
            tripRepository.save(trip);
        } catch (Exception e) {
            log.error("Error while saving the location details for the trip_id {}", trip.getId());
        }
    }

    public LocationDto getStartAndEndLocation(Instant startTime, Instant endTime, String imei) {
        LocationDto location = new LocationDto();
        LocationDataDto startLocation = null;
        LocationDataDto endLocation = null;
        if (startTime != null) {
            TripLocationProjection startLocationData = locationDataRepository.getStartLocationDataByTimeStampAndImei(startTime, imei);
            startLocation = startLocationData != null ? getLocationDataFromReverseGeoCodingApi(null, startLocationData) : null;
        }
        if (endTime != null) {
            TripLocationProjection endLocationData = locationDataRepository.getEndLocationDataByTimeStampAndImei(endTime, imei);
            endLocation = endLocationData != null ? getLocationDataFromReverseGeoCodingApi(null, endLocationData) : null;
        }
        location.setStartLocation(startLocation);
        location.setEndLocation(endLocation);
        return location;
    }

    private LocationDataDto getLocationDataFromReverseGeoCodingApi(Trip trip, TripLocationProjection
            locationProjection) {
        LocationDataDto response;
        log.info("Lat and long is {} {}", locationProjection.getLatitude(), locationProjection.getLongitude());
        try {
            response =
                    WebClient.create().get()
                            .uri(locationServiceAPIUrl + "?lat=" + locationProjection.getLatitude() + "&long=" + locationProjection.getLongitude())
                            .retrieve().bodyToMono(LocationDataDto.class).block();
            log.info("Data is {}", response);
        } catch (Exception e) {
            response = new LocationDataDto();
            log.error("An error occurred {}", e.getMessage());
        }
        return response;
    }

    @Override
    public FilterResponseDto getFilterData(FilterDto filterDto, Long organisationId) {
        Optional<CustomOrganisation> organisation = organisationRepository.findById(organisationId);
        Long orgId = organisationId;
        if (organisation.isPresent() && organisation.get().getOrganisationType().equals(OrganisationType.ADMINISTRATOR)) {
            orgId = null;
        }
        FilterResponseDto responseDto = new FilterResponseDto();
        Instant start = epochMillisToInstant(filterDto.getStartTime(), "Start Time");
        Instant end = epochMillisToInstant(filterDto.getEndTime(), "End Time");
        responseDto.setStartTime(start);
        responseDto.setEndTime(end);
        List<RiderAndBatteryManufacturer> responseList;
        if (Optional.ofNullable(orgId).isPresent()) {
            responseList = tripRepository.getImeiRiderBatteryManufacturerByTimestamp(start, end, orgId, filterDto.getVehicleModelId());
        } else {
            responseList = tripRepository.getImeiRiderBatteryManufacturerByTimestampForAdmin(start, end, filterDto.getVehicleModelId());
        }
        responseDto.setImeiList(responseList.stream().map(RiderAndBatteryManufacturer::getImei).distinct().toList());
        responseDto.setRiders(responseList.stream().map(e -> new RiderData(e.getId(), e.getRiderName())).distinct().toList());
        responseDto.setBatteryManufacturers(responseList.stream().map(RiderAndBatteryManufacturer::getBatteryManufacturer).distinct().toList());
        return responseDto;
    }

    public TripCountByFilterResponse getTripCountByFilter(FilterInputDto filterInputDto) {
        Instant start = epochMillisToInstant(filterInputDto.getStartTime(), "Start Time");
        Instant end = epochMillisToInstant(filterInputDto.getEndTime(), "End Time");
        List<Long> tripIds = tripRepository.getTripIdsByFilter(start, end,
                filterInputDto.getImei(), filterInputDto.getRiders(), filterInputDto.getBatteryManufacturers());
        TripCountByFilterResponse response = new TripCountByFilterResponse();
        int count = tripIds.size();
        if (count == 0) {
            response.setMessage(evMessageBundle.getMessage("TRIP_NOT_FOUND_FOR_FILTER"));
        }
        response.setTripIds(tripIds);
        response.setCount(count);
        return response;
    }

    @Override
    public TripListAllDto getVehicleTripsBetween(String identifier, TripType tripType, Long from, Long to, Pageable pageable) {
        TripListAllDto result = new TripListAllDto();
        Vehicle vehicle = vehicleRepository.findVehicleByIdentifier(identifier).orElseThrow(() -> new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));
        Pageable alteredPageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize());
        Page<Trip> trips;
        if (tripType != null) {
            trips = tripRepository.findValidTripsWithDistanceAndDuration(vehicle.getId(), TestRideSummaryPopulationStatus.COMPLETED.name(), Collections.singletonList(tripType.name()), Instant.ofEpochMilli(from), Instant.ofEpochMilli(to), alteredPageable);
        } else {
            List<String> tripTypes = Arrays.asList(TripType.AUTOMATIC.name(), TripType.TEST_RIDE.name());
            trips = tripRepository.findValidTripsWithDistanceAndDuration(vehicle.getId(), TestRideSummaryPopulationStatus.COMPLETED.name(), tripTypes, Instant.ofEpochMilli(from), Instant.ofEpochMilli(to), alteredPageable);
        }

        result.setData(tripListAllMapper(trips.toList()));
        result.setTotalPages(trips.getTotalPages());
        result.setTotalElements(trips.getTotalElements());

        // Fetch latest trip for this vehicle
        Trip latestTrip = tripRepository.findLatestValidTripByVehicle(vehicle.getId() , TestRideSummaryPopulationStatus.COMPLETED.name() , TripDetailsFields.totalDistance.name());
        if (latestTrip != null) {
            TripListAllDataDto latestTripDto = tripListAllMapper(Collections.singletonList(latestTrip))
                    .stream()
                    .findFirst()
                    .orElse(null);
            result.setLatestTrip(latestTripDto);
        }
        return result;
    }

    private List<TripListAllDataDto> tripListAllMapper(List<Trip> trips) {
        DecimalFormat decimalFormatter = new DecimalFormat("#.##");
        List<TripListAllDataDto> result = new ArrayList<>();
        trips.forEach(e -> {
            TripListAllDataDto tripListAllDataDto = new TripListAllDataDto();
            BeanUtils.copyProperties(e, tripListAllDataDto);
            tripListAllDataDto.setStartTime(e.getStartTime().toEpochMilli());
            tripListAllDataDto.setEndTime(e.getEndTime().toEpochMilli());
            TripDetails tripDetails = tripDetailsRepo.findByTripIdxTripAndTripIdxFieldNameAndTripIdxTimestamp(e, "totalDistance", e.getStartTime());
            Float tripDistance = tripDetails != null ? Float.valueOf(decimalFormatter.format(Float.valueOf(tripDetails.getFieldValue()))) : 0.0f;
            tripListAllDataDto.setDistance(tripDistance);
            Long tripDuration = Duration.between(e.getStartTime(), e.getEndTime()).toSeconds();
            tripListAllDataDto.setDuration(tripDuration);
            tripListAllDataDto.setTestId(
                    Optional.ofNullable(e.getVehicleTest())
                            .map(VehicleTest::getId)
                            .orElse(null)
            );
            tripListAllDataDto.setTestStatus(
                    Optional.ofNullable(e.getVehicleTest())
                            .map(VehicleTest::getStatus)
                            .orElse(null)
            );

            tripListAllDataDto.setStartAddress(userTripService.getAddressFromNeighbourhoodAndCityAndState(e.getTripStartNeighbourhood(), e.getTripStartCity(), e.getTripStartState()));
            tripListAllDataDto.setEndAddress(userTripService.getAddressFromNeighbourhoodAndCityAndState(e.getTripEndNeighbourhood(), e.getTripEndCity(), e.getTripEndState()));
            result.add(tripListAllDataDto);
        });
        return result;
    }

    @Override
    public TestRideV2 getV2AnalyticsData(V2TestRideInput v2TestRideInput) {
        log.debug("Inside getV2AnalyticsData {}", v2TestRideInput);
        v2TestRideInput.setCorrelationId(UUID.randomUUID());
        TestRideV2 response = getRequestBodySpec(v2AnalyticsEndpoint)
                .body(Mono.just(v2TestRideInput), V2TestRideInput.class)
                .accept(MediaType.APPLICATION_JSON).retrieve()
                .onStatus(HttpStatusCode::is4xxClientError, res -> res.bodyToMono(String.class).map(V2AnalyticsBadRequestException::new))
                .onStatus(HttpStatusCode::is5xxServerError, res -> res.bodyToMono(String.class).map(V2AnalyticsInternalServerErrorException::new))
                .bodyToMono(TestRideV2.class)
                .doOnError(throwable -> log.info("error inside the getV2Analytics : " + throwable))
                .block();

        // Validate response
        if (response == null || response.getTripId() == null) {
            log.error("Received invalid response from ev-servables for tripId: {}. Response: {}",
                    v2TestRideInput.getTripId(), response);
            throw new V2AnalyticsInternalServerErrorException(
                    evMessageBundle.getMessage("V2_ANALYTICS_INVALID_RESPONSE", v2TestRideInput.getTripId()));
        }
        log.debug("Successfully received analytics data for tripId: {}", response.getTripId());
        return response;
    }

    private Instant epochMillisToInstant(Long epochMillis, String fieldName) {
        Instant instant;
        try {
            instant = Instant.ofEpochMilli(epochMillis);
        } catch (Exception e) {
            throw new TripSummaryException(fieldName + " must not be empty");
        }
        return instant;
    }

    @Transactional
    private void createBreaks(List<BrakeResponseModel> data1, Trip trip) {
        List<BrakeData> brakeDataList = new ArrayList<>();
        for (BrakeResponseModel brakeResponseModel : data1) {
            BrakeData brakeData = new BrakeData();
            brakeData.setTrip(trip);
            brakeData.setStartTime(Instant.ofEpochMilli(brakeResponseModel.getStartTime()));
            brakeData.setEndTime(Instant.ofEpochMilli(brakeResponseModel.getEndTime()));
            brakeData.setBrakeDistance(brakeResponseModel.getDistance());
            brakeData.setBrakeDuration(brakeResponseModel.getDuration());
            brakeData.setBrakeIntensity(brakeResponseModel.getIntensity());
            brakeDataList.add(brakeData);
        }
        brakeDataRepository.saveAll(brakeDataList);
    }

    @Transactional
    private void createTurns(List<TurnResponseModel> data, Trip trip) {
        log.info("inside createTurns {}", data);
        List<TurnData> turns = new ArrayList<>();
        data.forEach(e -> {
            TurnType turnType = e.getTurnType().equalsIgnoreCase("right") ? TurnType.RIGHT : TurnType.LEFT;
            TurnData turnData = new TurnData();
            turnData.setTrip(trip);
            turnData.setTimestamp(Instant.ofEpochMilli(e.getTimestamp()));
            turnData.setTurnType(turnType);
            turnData.setLeanAngle(e.getLeanAngle());
            turns.add(turnData);
        });
        log.info("turn data size", turns.size());
        turnDataRepository.saveAll(turns);
    }

    private List<TripDetails> createModeWiseData(Map<String, Double> data, Trip trip, String suffix) {
        List<TripDetails> tripDetails = new ArrayList<>();
        for (Map.Entry<String, Double> entry : data.entrySet()) {
            TripDetails details = new TripDetails();
            TripIdx idx = createTripIdx(trip);
            idx.setFieldName(entry.getKey().toLowerCase() + suffix);
            details.setTripIdx(idx);
            details.setVehicle(trip.getVehicle());
            details.setFieldValue(String.valueOf(entry.getValue()));
            details.setDataType(findDataType(String.valueOf(entry.getValue())));
            tripDetails.add(details);
        }
        return tripDetails;
    }


    private TripIdx createTripIdx(Trip trip) {
        TripIdx idx = new TripIdx();
        idx.setImei(trip.getImei());
        idx.setTrip(trip);
        idx.setTimestamp(trip.getStartTime());
        return idx;
    }

    private UserVehicleTripIdx createUserVehicleTripIdx(UserVehicleTrip userVehicleTrip) {
        UserVehicleTripIdx idx = new UserVehicleTripIdx();
        idx.setImei(userVehicleTrip.getTrip().getImei());
        idx.setUserVehicleTrip(userVehicleTrip);
        idx.setTimestamp(userVehicleTrip.getStartTime());
        return idx;
    }

    private String findDataType(String value) {
        String dataType;
        if (isInteger(value)) {
            dataType = "INTEGER";
        } else if (isFloat(value)) {
            dataType = "FLOAT";
        } else if (isDouble(value)) {
            dataType = "DOUBLE";
        } else if (isBoolean(value)) {
            dataType = "BOOLEAN";
        } else {
            dataType = "STRING";
        }
        return dataType;
    }

    public static boolean isInteger(String value) {
        boolean isInteger;
        try {
            Integer.parseInt(value);
            isInteger = true;

        } catch (Exception e) {
            isInteger = false;
        }
        return isInteger;
    }

    public static boolean isFloat(String value) {
        boolean isFloat;
        try {
            Float.parseFloat(value);
            isFloat = true;

        } catch (Exception e) {
            isFloat = false;
        }
        return isFloat;
    }

    public static boolean isDouble(String value) {
        boolean isDouble;
        try {
            Double.parseDouble(value);
            isDouble = true;

        } catch (Exception e) {
            isDouble = false;
        }
        return isDouble;
    }

    public static boolean isBoolean(String value) {
        return value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false");
    }

    @Override
    public CumulativeResponse   getCumulativeData(CumulativeInputDto cumulativeInputDto, Long organisationId) throws
            JsonProcessingException {
        try {
            Map<String, Object> cumulativeTestRideResponse = new HashMap<>();
            Instant start = epochMillisToInstant(cumulativeInputDto.getStartTime(), "Start Time");
            Instant end = epochMillisToInstant(cumulativeInputDto.getEndTime(), "End Time");
            if (Optional.ofNullable(organisationId).isPresent()) {
                cumulativeTestRideResponse = generateCumulativeRideResponses(tripRepository.findWeightedAverageData(cumulativeInputDto.getImei(), cumulativeInputDto.getRiders(), cumulativeInputDto.getBatteryManufacturers(), start, end, organisationId, tripAttributeNames));
            } else {
                cumulativeTestRideResponse = generateCumulativeRideResponses(tripRepository.findWeightedAverageDataForAdmin(cumulativeInputDto.getImei(), cumulativeInputDto.getRiders(), cumulativeInputDto.getBatteryManufacturers(), start, end, tripAttributeNames));
            }
            log.info("Cumulative Test Ride Response size: {}", cumulativeTestRideResponse.size());
            Predicate<SpeedModeData> notAverageSpeedModeData = fieldName -> !fieldName.getFieldName().equals("average");
            Predicate<SpeedModeData> isAverageSpeedModeData = fieldName -> fieldName.getFieldName().equals("average");
            List<SpeedModeData> speedModeData = getModeWiseFrequentAndMaxSpeed(cumulativeInputDto.getImei(), cumulativeInputDto.getRiders(), cumulativeInputDto.getBatteryManufacturers(), start, end);
            log.info("Speed Mode Data Response size: {}", speedModeData.size());
            cumulativeTestRideResponse.put("speedModeData", speedModeData.stream().filter(notAverageSpeedModeData).toList());
            cumulativeTestRideResponse.put("overAllSpeedModeData", speedModeData.stream().filter(isAverageSpeedModeData).toList().get(0));
            cumulativeTestRideResponse.put("chordData", tripRepository.getChordDiagramData(cumulativeInputDto.getImei(), cumulativeInputDto.getBatteryManufacturers(), cumulativeInputDto.getRiders(), start, end));
            cumulativeTestRideResponse.put("totalBrakes", brakeDataRepository.getTotalNoOfBreaks(cumulativeInputDto.getImei(), cumulativeInputDto.getRiders(), cumulativeInputDto.getBatteryManufacturers(), start, end));
            log.info("Cumulative Test Ride Response: {}", cumulativeTestRideResponse);
            return mapToDto(cumulativeTestRideResponse);
        } catch (JsonProcessingException e) {
            log.error("Error while processing ", e);
            throw new RuntimeException(e);
        }
    }

    private Float generateWeightedAvg(TestRideDetailsResponse data) {
        return Float.parseFloat(data.getFieldValue()) / data.getCount();
    }

    private Map<String, Object> generateCumulativeRideResponses(List<TestRideDetailsResponse> responses) {
        Map<String, Object> map = new HashMap<>();
        Float startSoc = 0f;
        Float endSoc = 0f;
        for (TestRideDetailsResponse response : responses) {
            String fieldName = response.getFieldName();
            switch (fieldName) {
                case "meanSpeed":
                case "dischargePerCharge":
                case "statisticalSpeedMode":
                case "averageBrakeDistance":
                case "meanBrakeDuration":
                case "maxSpeed":
                    if (fieldName.equals("meanSpeed")) {
                        fieldName = "averageSpeed";
                    } else if (fieldName.equals("dischargePerCharge")) {
                        fieldName = "distancePerCharge";
                    }
                    map.put(fieldName, generateWeightedAvg(response));
                    break;
                case "startSoc":
                case "endSoc":
                    if (fieldName.equals("startSoc")) {
                        startSoc = Float.parseFloat(response.getFieldValue());
                    } else {
                        endSoc = Float.parseFloat(response.getFieldValue());
                    }
                    break;
                default:
                    map.put(fieldName, response.getFieldValue());
                    break;
            }
            map.put("totalDischarge", startSoc - endSoc);
        }
        return map;
    }

    public List<SpeedModeData> getModeWiseFrequentAndMaxSpeed
            (List<String> imei, List<String> riders, List<String> batteryManufacturers, Instant start, Instant end) {
        List<MaxSpeedMode> data = tripRepository.getModeWiseMaxSpeed(imei, riders, batteryManufacturers, start, end);
        List<SpeedModeData> speedModeData = new ArrayList<>();
        Float averageMaxSpeed = 0f;
        Float averageFrequentSpeed = 0f;
        Long count = 0L;
        for (MaxSpeedMode mode : data) {
            FrequentSpeedMode frequentSpeed = tripRepository.getModeWiseFrequentSpeed(mode.getFieldName(), imei, riders, batteryManufacturers, start, end);
            averageMaxSpeed += mode.getTotalDistance();
            averageFrequentSpeed += frequentSpeed.getFrequentSpeed();
            speedModeData.add(new SpeedModeData(mode.getFieldName().split("Distance")[0].toUpperCase(), mode.getTotalDistance(), frequentSpeed.getFrequentSpeed()));
            count++;
        }
        float distance = Float.parseFloat(String.format("%.2f", averageMaxSpeed / count));
        float frequentSpeed = Float.parseFloat(String.format("%.2f", averageFrequentSpeed / count));
        speedModeData.add(new SpeedModeData("average", Float.isNaN(distance) ? 0f : distance, Float.isNaN(frequentSpeed) ? 0f : frequentSpeed));
        return speedModeData;
    }

    public CumulativeResponse mapToDto(Map<String, Object> map) throws JsonProcessingException {
        CumulativeResponse response = new CumulativeResponse();
        response.setTotalDistance(formatToFloatTwoDecimals(map.getOrDefault("totalGpsDistance", "0")));
        response.setDistancePerCharge(formatToFloatTwoDecimals(Float.parseFloat(map.getOrDefault("distancePerCharge", "0").toString()) * 100)); //range * 100 to keep Range unit = km/charge
        response.setStopDuration(Float.parseFloat(map.getOrDefault("stopDuration", "0").toString()));
        response.setRideDuration(Float.parseFloat(map.getOrDefault("rideDuration", "0").toString()));
        response.setRunningDuration(Float.parseFloat(map.getOrDefault("runningDuration", "0").toString()));
        response.setAverageSpeed(formatToFloatTwoDecimals(map.getOrDefault("averageSpeed", "0")));
        response.setTotalDischarge(formatToFloatTwoDecimals(map.getOrDefault("totalDischarge", "0")));
        response.setTopSpeed(formatToFloatTwoDecimals(map.getOrDefault("maxGpsSpeed", "0")));
        response.setMaxAcceleration(formatToFloatTwoDecimals(map.getOrDefault("maxAccelerationMps2", "0")));
        response.setMaxCurrent(formatToFloatTwoDecimals(map.getOrDefault("maxMotorCurrentA", "0")));
        response.setBrakingTime(formatToFloatTwoDecimals(map.getOrDefault("totalBrakeDuration", "0")));
        response.setBrakingDistance(formatToFloatTwoDecimals(map.getOrDefault("brakeDistance", "0")));
        response.setStatisticalSpeedMode(formatToFloatTwoDecimals(map.getOrDefault("statisticalSpeedMode", "0")));
        response.setNumberOfBrakesPerKm(formatToFloatTwoDecimals(map.getOrDefault("brakesPerKm", "0")));
        response.setTotalBrakes(formatToFloatTwoDecimals(map.getOrDefault("totalBrakes", "0")));
        response.setAverageBrakingDuration(formatToFloatTwoDecimals(map.getOrDefault("meanBrakeDuration", "0")));
        response.setAverageBrakingDistance(formatToFloatTwoDecimals(map.getOrDefault("averageBrakeDistance", "0")));
        response.setMaxDeceleration(formatToFloatTwoDecimals(map.getOrDefault("minAccelerationMps2", "0")));
        Float overallDistance = 0f;
        List<SpeedModeData> speedModeData = (List<SpeedModeData>) map.get("speedModeData");
        for (SpeedModeData data : speedModeData) {
            overallDistance += data.getDistance();
        }
        response.setSpeedModeData(speedModeData);
        SpeedModeData overAllSpeedModeData = (SpeedModeData) map.getOrDefault("overAllSpeedModeData", new SpeedModeData());
        response.setOverAllSpeedModeData(new OverAllSpeedModeData(overallDistance, overAllSpeedModeData.getFrequentSpeed()));
        response.setChordData((List<ChordDiagramResponseDto>) map.get("chordData"));
        return response;
    }


    private Float formatToFloatTwoDecimals(Object value) {
        try {
            return Float.parseFloat(String.format("%.2f", Float.parseFloat(value.toString())));
        } catch (Exception e) {
            return 0f;
        }
    }


    public WebClient.RequestBodySpec getRequestBodySpec(String url) {
        return client.baseUrl(baseUrl).build().post()
                .uri(url).headers(httpHeaders -> {
                    httpHeaders.set("Content-type", "application/json");
                    httpHeaders.set("Accept", "*/*");
                });
    }
}