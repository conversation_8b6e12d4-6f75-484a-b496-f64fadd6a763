package com.nichesolv.evahanam.trip.jpa;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Entity
@Data
@Table(name = "trip_details",
        indexes = {
                @Index(name = "trip_details_idx", columnList = "imei, fieldName"),
                @Index(name = "trip_details_imei_idx", columnList = "imei"),
                @Index(name = "trip_details_ts_idx", columnList = "timestamp"),
                @Index(name = "trip_details_veh_id_idx", columnList = "vehicle_id"),
                @Index(name = "trip_details_trip_idx", columnList = "trip_id"),
        }
)
public class TripDetails {
    @EmbeddedId
    TripIdx tripIdx;

    @ManyToOne
    @JoinColumn(name = "vehicle_id", foreignKey = @ForeignKey(name = "fk_vehicle_id"))
    Vehicle vehicle;

    String fieldValue;
    String dataType;

}
