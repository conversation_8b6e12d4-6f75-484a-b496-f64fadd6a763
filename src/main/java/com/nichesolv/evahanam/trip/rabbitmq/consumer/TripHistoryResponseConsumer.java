package com.nichesolv.evahanam.trip.rabbitmq.consumer;

import com.nichesolv.evahanam.trip.rabbitmq.dto.TripHistoryResponse;
import com.nichesolv.evahanam.trip.service.TripSummaryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TripHistoryResponseConsumer {

    private static final Logger log = LoggerFactory.getLogger(TripHistoryResponse.class);

    @Autowired
    private TripSummaryService tripSummaryService;

    //@RabbitListener(queues = {"${rabbitmq.queue.trip-history-response:trip-history-response}"}, replyContentType = MessageProperties.CONTENT_TYPE_JSON)
    public void consume(TripHistoryResponse tripHistoryResponse) {
        log.info("Message received from trip detection service -> {}", tripHistoryResponse);
        tripSummaryService.processAutomatedTrips(tripHistoryResponse);
    }
}
