package com.nichesolv.evahanam.trip.rabbitmq.consumer;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.nichesolv.evahanam.trip.dto.TestRideV2;
import com.nichesolv.evahanam.trip.rabbitmq.dto.TripResponseModel;
import com.nichesolv.evahanam.trip.service.TripSummaryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TripDetailsConsumer {
    private static final Logger LOGGER = LoggerFactory.getLogger(TripDetailsConsumer.class);

    @Autowired
    TripSummaryService tripSummaryService;

//    @RabbitListener(queues = {"${rabbitmq.queue.trip-details-output}"}, replyContentType = MessageProperties.CONTENT_TYPE_JSON)
    public void consume(TestRideV2 message) throws JsonProcessingException {

        LOGGER.debug(String.format("Received message -> %s", message));
        tripSummaryService.saveGeneratedTripStats(message);

    }
}
