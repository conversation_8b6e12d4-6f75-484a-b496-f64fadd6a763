package com.nichesolv.evahanam.trip.listener;

import com.nichesolv.evahanam.common.enums.FeatureName;
import com.nichesolv.evahanam.common.events.VehicleCronEvent;
import com.nichesolv.evahanam.trip.service.TripSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class DelayedTripGenerationListener implements ApplicationListener<VehicleCronEvent> {


    @Autowired
    TripSummaryService tripSummaryService;

    @Override
    @Async("cronEventListenerExecutor")
    public void onApplicationEvent(VehicleCronEvent event) {
        if (event == null || event.getFeatureName() == null || event.getTime() == null|| event.getImei() == null || event.getDataFrequencyPlanDetails() == null) {
            log.warn("Received null field in VehicleCronEvent, skipping delayed trip generation processing.");
            return;
        }
        try {
            if(event.getFeatureName() == FeatureName.TRIP_UPDATION){
                log.debug("Received VehicleCronEvent for TRIP_UPDATION with imei: {} at time: {}", event.getImei(), event.getTime());
                tripSummaryService.saveDataDelayedAutomaticTrips(event.getImei(), event.getTime(), event.getDataFrequencyPlanDetails());
            }
        } catch (Exception e) {
            log.error("Error in processing delayed trip generation for vehicle {}: {}", event.getImei(), e.getMessage(), e);
        }


    }
}
