package com.nichesolv.evahanam.trip.dto.mapper;

import com.nichesolv.evahanam.common.repository.CommentRepository;
import com.nichesolv.evahanam.telemetryData.repository.LocationDataRepository;
import com.nichesolv.evahanam.trip.dto.tripsummary.*;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleModel.jpa.DriveMode;
import com.nichesolv.evahanam.vehicleModel.repository.DriveModeRepository;
import com.nichesolv.evahanam.vehicleTests.dto.TestRideFeedbackFormDto;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.enums.TimePosition;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTestRideForm;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleRideFormRepository;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRideFormVehicleParametersRepository;
import com.nichesolv.evahanam.trip.enums.GrafanaRouteMapFields;
import com.nichesolv.evahanam.trip.jpa.EnergyConsumption;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.repository.BrakeDataRepository;
import com.nichesolv.evahanam.trip.repository.EnergyConsumptionRepository;
import com.nichesolv.evahanam.trip.repository.TripDetailsRepo;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class TripDtoMapper implements BaseTripDtoMapper<Trip, TripSummaryDto> {
    @Autowired
    TripDetailsRepo detailsRepo;


    @Autowired
    VehicleRideFormRepository vehicleRideFormRepository;


    @Autowired
    CommentRepository commentRepository;
    @Autowired
    private BrakeDataRepository brakeDataRepository;
    @Autowired
    EnergyConsumptionRepository energyConsumptionRepository;
    @Autowired
    LocationDataRepository locationDataRepository;

    @Autowired
    VehicleTestRideFormVehicleParametersRepository vehicleTestRideFormVehicleParametersRepository;

    @Autowired
    DriveModeRepository driveModeRepository;

    private static final HashMap<GrafanaRouteMapFields,String> grafanaRouteMapFields=new HashMap<>();
    static  {
        grafanaRouteMapFields.put(GrafanaRouteMapFields.loc,"Route");
        grafanaRouteMapFields.put(GrafanaRouteMapFields.locSpeed,"Speed");
        grafanaRouteMapFields.put(GrafanaRouteMapFields.locAltitude,"Altitude");
        grafanaRouteMapFields.put(GrafanaRouteMapFields.locDriveMode,"Drive Modes");
        grafanaRouteMapFields.put(GrafanaRouteMapFields.locAiTemp,"AI Temperature");
        grafanaRouteMapFields.put(GrafanaRouteMapFields.locMcs,"MCS Temperature");
        grafanaRouteMapFields.put(GrafanaRouteMapFields.locMotor,"Motor Temperature");
        grafanaRouteMapFields.put(GrafanaRouteMapFields.locBattery,"Battery Temperature");
        grafanaRouteMapFields.put(GrafanaRouteMapFields.locSoc,"Battery SOC");
    }

    @Override
    public TripSummaryDto entityToDto(Trip trip) {
        TripSummaryDto tripSummaryDto = new TripSummaryDto();
        log.warn("outside try");
        try {
            Map<String, String> response = detailsRepo.findByTripId(trip.getId()).stream()
                    .collect(
                            Collectors.toMap(TripSummaryResponse::getFieldName,
                                    TripSummaryResponse::getFieldValue
                            ));
            log.info(response.toString());
            log.info(response.get("leftTurns"));
            log.warn("inside the try block");
            Float brakeTime = brakeDataRepository.findBreakDurationByTripId(trip.getId());
            brakeTime = (Optional.ofNullable(brakeTime).isEmpty()) ? 0 : brakeTime;
            PageRequest request = PageRequest.of(0, 1);
            Optional<VehicleTestRideForm> rideForm = vehicleRideFormRepository.findByVehicleTestId(trip.getVehicleTest().getId());
            TestRideFeedbackFormDto formDto = rideFormEntityToDto(rideForm.stream());
            Float startSoc = Float.parseFloat(response.getOrDefault("startSoc", "0"));
            Float endSoc = Float.parseFloat(response.getOrDefault("endSoc", "0"));
            tripSummaryDto.setRecordedTime(trip.getStartTime());
            tripSummaryDto.setTotalMotorDistance(Float.parseFloat(response.getOrDefault("totalMotorDistance", "0")));
            tripSummaryDto.setTotalGpsDistance(Float.parseFloat(response.getOrDefault("totalGpsDistance", "0")));
            tripSummaryDto.setDistancePerCharge(Float.parseFloat(response.getOrDefault("dischargePerCharge", "0"))* 100); // converting range to km
            tripSummaryDto.setTotalBrakes(brakeDataRepository.findCountById(trip.getId()));
            tripSummaryDto.setBrakesPerKm(Float.parseFloat(response.getOrDefault("brakesPerKm", "0")));
            tripSummaryDto.setAverageDistancePerBrake(Float.parseFloat(response.getOrDefault("averageBrakeDistance", "0")));
            tripSummaryDto.setAverageDurationPerBrake(Float.parseFloat(response.getOrDefault("meanBrakeDuration", "0")));
            tripSummaryDto.setAverageSpeed(Float.parseFloat(response.getOrDefault("meanSpeed", "0")));
            tripSummaryDto.setTripDuration(Integer.parseInt(response.getOrDefault("rideDuration", "0")));
            tripSummaryDto.setTopSpeed(Float.parseFloat(response.getOrDefault("maxSpeed", "0")));
            tripSummaryDto.setLeftTurns(Integer.parseInt(response.getOrDefault("leftTurns","0")));
            tripSummaryDto.setRightTurns(Integer.parseInt(response.getOrDefault("rightTurns","0")));
            tripSummaryDto.setTotalDischarge(startSoc - endSoc);
            tripSummaryDto.setStartingVoltage(Float.parseFloat(response.getOrDefault("startVoltage", "0")));
            tripSummaryDto.setEndingVoltage(Float.parseFloat(response.getOrDefault("endVoltage", "0")));
            tripSummaryDto.setMaxAccelerationMps2(Float.parseFloat(response.getOrDefault("maxAccelerationMps2", "0")));
            tripSummaryDto.setMinAccelerationMps2(Float.parseFloat(response.getOrDefault("minAccelerationMps2", "0")));
            tripSummaryDto.setMaxMotorSpeed(Float.parseFloat(response.getOrDefault("maxMotorSpeed", "0")));
            tripSummaryDto.setAvgMotorSpeed(Float.parseFloat(response.getOrDefault("avgMotorSpeed", "0")));
            tripSummaryDto.setMaxGpsSpeed(Float.parseFloat(response.getOrDefault("maxGpsSpeed", "0")));
            tripSummaryDto.setAvgGpsSpeed(Float.parseFloat(response.getOrDefault("avgGpsSpeed", "0")));
            tripSummaryDto.setMaxMotorCurrentA(Float.parseFloat(response.getOrDefault("maxMotorCurrentA", "0")));
            tripSummaryDto.setMinMotorCurrentA(Float.parseFloat(response.getOrDefault("minMotorCurrentA", "0")));
            tripSummaryDto.setMaxBatteryCurrentA(Float.parseFloat(response.getOrDefault("maxBatteryCurrentA", "0")));
            tripSummaryDto.setMinBatteryCurrentA(Float.parseFloat(response.getOrDefault("minBatteryCurrentA", "0")));
            tripSummaryDto.setMaxMotorVoltage(Float.parseFloat(response.getOrDefault("maxMotorVoltage", "0")));
            tripSummaryDto.setMinMotorVoltage(Float.parseFloat(response.getOrDefault("minMotorVoltage", "0")));
            tripSummaryDto.setMaxBatteryVoltage(Float.parseFloat(response.getOrDefault("maxBatteryVoltage", "0")));
            tripSummaryDto.setMinBatteryVoltage(Float.parseFloat(response.getOrDefault("minBatteryVoltage", "0")));
            tripSummaryDto.setMaxAltitude(Float.parseFloat(response.getOrDefault("maxAltitude", "0")));
            tripSummaryDto.setMinAltitude(Float.parseFloat(response.getOrDefault("minAltitude", "0")));
            tripSummaryDto.setMaxLeanAngle(Float.parseFloat(response.getOrDefault("maxLeanAngle", "0")));
            tripSummaryDto.setAvgLeanAngle(Float.parseFloat(response.getOrDefault("avgLeanAngle", "0")));
            tripSummaryDto.setBrakingTime(brakeTime);
            tripSummaryDto.setLoad(response.getOrDefault("load", null));
            tripSummaryDto.setBrakingDistance(Float.parseFloat(response.getOrDefault("brakeDistance", null)));
            tripSummaryDto.setStopDuration(Integer.parseInt(response.getOrDefault("stopDuration", "0")));
            tripSummaryDto.setRunningDuration(Integer.parseInt(response.getOrDefault("runningDuration", "0")));
            tripSummaryDto.setMoreThanOneHourStop(Float.parseFloat(response.getOrDefault("moreThanOneHourStop", null)));
            tripSummaryDto.setStartSoc(startSoc);
            tripSummaryDto.setEndSoc(endSoc);
            tripSummaryDto.setEnergyPerKm(getEnergyConsumptionPerKm(trip));
            tripSummaryDto.setRideForm(formDto);
            tripSummaryDto.setTestStartTime(trip.getVehicleTest().getStartTime().toEpochMilli());
            tripSummaryDto.setTestEndTime(trip.getVehicleTest().getEndTime().toEpochMilli());
            tripSummaryDto.setTestId(trip.getVehicleTest().getId());


            BatteryPerformanceDto batteryPerformanceDto = BatteryPerformanceDto.builder()
                    .distance(tripSummaryDto.getTotalGpsDistance())
                    .discharge(tripSummaryDto.getTotalDischarge())
                    .endSoc(tripSummaryDto.getEndSoc())
                    .endVoltage(tripSummaryDto.getEndingVoltage())
                    .startVoltage(tripSummaryDto.getStartingVoltage())
                    .endTime(tripSummaryDto.getTestEndTime())
                    .startTime(tripSummaryDto.getTestStartTime())
                    .startSoc(tripSummaryDto.getStartSoc())
                    .build();
            tripSummaryDto.setBatteryPerformance(Arrays.asList(batteryPerformanceDto));
            CustomOrganisation customOrganisation= (CustomOrganisation) trip.getVehicle().getManufacturer();
            Set<DriveMode> organisationDriveModes=driveModeRepository.findByOrganisation(customOrganisation);
            List<TripModeStats> tripModeStats = new ArrayList<>();

            organisationDriveModes.forEach(driveMode-> {
                if(!driveMode.getName().equals("NULL_DRIVE_SELECTION")) {
                    String fieldName = driveMode.getName().toLowerCase().concat("MeanSpeed");
                    log.debug("field name {}", fieldName);
                    if (Optional.ofNullable(response.get(fieldName)).isPresent()) {
                        log.debug("field value {}", response.get(fieldName));
                        tripModeStats.add(new TripModeStats(driveMode.getName(), Float.parseFloat(response.getOrDefault(fieldName, "0")), Float.parseFloat(response.getOrDefault(driveMode.getName().toLowerCase().concat("Distance"), null))));
                    }
                }
            });
            tripSummaryDto.setTripOverallModeStats(getOverallSpeedAndDistance(tripModeStats));
            tripSummaryDto.setTripModeStats(tripModeStats);
            tripSummaryDto.setStatisticalModeOfSpeed(Float.parseFloat(response.getOrDefault("statisticalSpeedMode", "0")));
            log.info("trip summary dto {}", response.get("statisticalSpeedMode"));
            log.warn("just before the setting grafana list");
            tripSummaryDto.setRouteMapList(getRouteMapFields(trip.getVehicle(),trip.getImei(),trip.getVehicleTest().getStartTime(),trip.getVehicleTest().getEndTime()));
            log.warn("end of  the try block");
        } catch (Exception e) {
            log.error("An error has been thrown {}", e.getMessage());
        }
        return tripSummaryDto;
    }

    @Override
    public Trip DtoToEntity(TripSummaryDto dto) {
        return null;
    }

    private TripOverallModeStats getOverallSpeedAndDistance(List<TripModeStats> tripModeStats) {
        log.info("Inside getOverallSpeedAndDistance {}", tripModeStats);
        Float totalDistance = 0f;
        Float totalSpeed = 0f;
        Integer count = 0;
        for (TripModeStats tripMode : tripModeStats) {
            count++;
            totalDistance += tripMode.getDistance();
            totalSpeed += tripMode.getSpeed();
        }
        float overAllSpeed= Float.parseFloat(String.format("%.2f", totalSpeed / count));
        float overAllDistance= Float.parseFloat(String.format("%.2f", totalDistance));
        return new TripOverallModeStats( Float.isNaN(overAllSpeed)?0.0f:overAllSpeed, Float.isNaN(overAllDistance)?0.0f:overAllDistance);
    }

    public TestRideFeedbackFormDto rideFormEntityToDto(Stream<VehicleTestRideForm> stream) {
        return  stream.map(e -> new TestRideFeedbackFormDto(e.getImei(), Optional.of(e.getStartTime().toEpochMilli()),
                Optional.of(e.getEndTime().toEpochMilli()),
                TestTypeName.TEST_RIDE,
                e.getRiderName(),
                Optional.ofNullable(e.getRiderWeight()).orElse(0f),
                e.getPillionRiderName(),
                Optional.ofNullable(e.getPillionRiderWeight()).orElse(0f),
                Optional.ofNullable(e.getTotalWeight()).orElse(0f),
                e.getRideStartPlaceName(),
                e.getRideEndPlaceName(),
                e.getRoadCondition().toArray(new String[0]),
                e.getTraffic().toArray(new String[0]),
                e.getClimateCondition().toArray(new String[0]),
                e.getFlyoverCount(),
                e.getDistance(),
                e.getBatteryManufacturer(),
                e.getRideStartVoltage(),
                e.getRideEndVoltage(),
                Optional.ofNullable(e.getComment()).isPresent() ?
                        commentRepository.findById(e.getComment().getId()).get().getComment() : null,
                e.getVehicleTest().getId(),
                e.getGoal(),
                e.getObservationParameter().toArray(new String[0]),
                e.getIssue().toArray(new String[0]),
                getTestRideFormVehicleParameters(e,TimePosition.BEFORE),
                getTestRideFormVehicleParameters(e,TimePosition.AFTER),
                e.getBatteryModel(),
                e.getBatteryCapacity()
        )).findAny().orElse(null);
    }

    public Map<String,Object> getTestRideFormVehicleParameters(VehicleTestRideForm vehicleTestRideForm,TimePosition timePosition){
        Map<String,Object> testRideFormVehicleParameters = new HashMap<>();
        vehicleTestRideFormVehicleParametersRepository.findByVehicleTestRideFormIdxVehicleTestRideFormAndVehicleTestRideFormIdxTimePosition(vehicleTestRideForm,timePosition).forEach(e->{
            testRideFormVehicleParameters.put(e.getVehicleTestRideFormIdx().getFieldName(),e.getFieldValue());
        });
        return testRideFormVehicleParameters;
    }


    private Float getEnergyConsumptionPerKm(Trip trip) {
        Float energyConsumedPerKm = 0f;
        List<EnergyConsumption> energyConsumptions = energyConsumptionRepository.findByTripAndStartTimeGreaterThanEqualAndEndTimeLessThanEqual(trip, trip.getStartTime(), trip.getEndTime());
        for (EnergyConsumption energyConsumption : energyConsumptions) {
            energyConsumedPerKm += energyConsumption.getEnergyPerKm();
        }
        return energyConsumptions.isEmpty()?energyConsumedPerKm : energyConsumedPerKm/energyConsumptions.size();
    }

    private List<GrafanaRouteMapFieldsDto> getRouteMapFields(Vehicle vehicle, String imei, Instant startTime, Instant endTime) {
        List<GrafanaRouteMapFieldsDto> fields = new ArrayList<>();
        Long mfrOrgId = vehicle.getManufacturer().getId();

        // Cache to avoid repeated validations per group
        Map<String, Boolean> validationCache = new HashMap<>();

        for (GrafanaRouteMapFields field : grafanaRouteMapFields.keySet()) {
            try {
                String groupKey = getValidationGroupKey(field);

                boolean dataExists = validationCache.computeIfAbsent(groupKey, key ->
                        getGrafanaRouteMapQueryResult(mfrOrgId, imei, startTime, endTime, field)
                );

                if (dataExists) {
                    fields.add(new GrafanaRouteMapFieldsDto(grafanaRouteMapFields.get(field), field.name()));
                }
            } catch (Exception e) {
                log.error("Validation failed for field {}: {}", field, e.getMessage(), e);
            }
        }
        return fields;
    }

    private String getValidationGroupKey(GrafanaRouteMapFields field) {
        return switch (field) {
            case locAiTemp, locMcs, locMotor -> "temperature";
            case locDriveMode -> "driveMode";
            case locBattery -> "batteryTemp";
            case locSoc -> "soc";
            case loc, locSpeed, locAltitude -> "locSpeedAlt";
        };
    }

    private boolean getGrafanaRouteMapQueryResult(Long mfrOrgId, String imei, Instant startTime, Instant endTime, GrafanaRouteMapFields field) {
        return switch (field) {
            case locAiTemp, locMcs, locMotor ->
                    locationDataRepository.validateTemperaturesQuery(imei, startTime, endTime, mfrOrgId);
            case locDriveMode -> locationDataRepository.validateGeoDriveModeQuery(imei, startTime, endTime, mfrOrgId);
            case locBattery ->
                    locationDataRepository.validateBatteryTemperatureQuery(imei, startTime, endTime, mfrOrgId);
            case locSoc -> locationDataRepository.validateBatterySocQuery(imei, startTime, endTime, mfrOrgId);
            case loc, locSpeed, locAltitude ->
                    locationDataRepository.validateLocSpeedAndAltitude(imei, startTime, endTime, mfrOrgId);
        };
    }

}
