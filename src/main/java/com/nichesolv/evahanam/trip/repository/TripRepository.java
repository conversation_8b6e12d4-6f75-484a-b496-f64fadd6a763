package com.nichesolv.evahanam.trip.repository;
import com.nichesolv.evahanam.trip.jpa.VehicleTripSummaryProjection;
import com.nichesolv.evahanam.vehicle.enums.UpdateSource;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.trip.dto.cumulative.ChordDiagramResponseDto;
import com.nichesolv.evahanam.trip.dto.cumulative.response.FrequentSpeedMode;
import com.nichesolv.evahanam.trip.dto.cumulative.response.MaxSpeedMode;
import com.nichesolv.evahanam.trip.dto.cumulative.response.RiderAndBatteryManufacturer;
import com.nichesolv.evahanam.trip.dto.cumulative.response.TestRideDetailsResponse;
import com.nichesolv.evahanam.trip.dto.tripsummary.TripProjection;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.trip.jpa.Trip;
import jakarta.persistence.QueryHint;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static org.hibernate.jpa.HibernateHints.HINT_FETCH_SIZE;

public interface TripRepository extends JpaRepository<Trip, Long> {

    @Query(value = "select * from trip where id=?1", nativeQuery = true)
    Trip findByIdAndStartTimeGreaterThanEqualAndEndTimeLessThanEqual(Long tripId);

    @Query(value = "select t.test_id as testId, t.start_time as startTime, t.end_time as endTime, vt.rider_name as rider, t.trip_start_city as from ," +
            "t.trip_end_city as to , " +
            "(select field_value from trip_details where field_name='maxSpeed' and trip_id=t.id) as topSpeed ," +
            "(select field_value from trip_details where field_name='totalGpsDistance' and trip_id=t.id) as distance," +
            " (select field_value from trip_details where field_name='rideDuration' and trip_id=t.id) as duration, " +
            " (select field_value from trip_details where field_name='meanSpeed' and trip_id=t.id) as avgSpeed , " +
            " v.end_time as vehicleTestEndTime, v.id as vehicleTestId from trip t,vehicle_test_ride_form vt , vehicle_test v where t.imei=?1 and t.summary_population_status= 'COMPLETED' and v.status = 'COMPLETED' " +
            " and v.id = vt.vehicle_test_id and v.end_time is not null and vt.vehicle_test_id=t.test_id ", countQuery = "select count(*) from trip t , vehicle_test v , vehicle_test_ride_form vt where v.id = vt.vehicle_test_id and " +
                    " vt.vehicle_test_id=t.test_id and " +
                    " t.imei = ?1 and t.summary_population_status = 'COMPLETED' and v.status = 'COMPLETED' " +
                    " and v.end_time is not null ", nativeQuery = true)
    Page<TripProjection> findByImei(String imei, Pageable page);



    Optional<Trip> findByVehicleTestId(Long id);

    @Query(value = "select distinct(t.imei) as imei, vt.rider_name as riderName, vt.battery_manufacturer as batteryManufacturer " +
            "            from trip t " +
            "            join vehicle_test_ride_form vt on t.test_id=vt.vehicle_test_id " +
            "            join vehicle v on v.imei=t.imei " +
            "            join vehicle_model vm on v.vehicle_model_id=vm.id " +
            "            where t.start_time between ?1 and  ?2 " +
            "            and t.summary_population_status='COMPLETED' " +
            "            and v.mfr_org_id= ?3 " +
            "            and vm.id= ?4 ", nativeQuery = true)
    List<RiderAndBatteryManufacturer> getImeiRiderBatteryManufacturerByTimestamp(Instant from, Instant to, Long organisationId,Long vehicleModelId);

    @Query(value = "select distinct(t.imei) as imei, vt.rider_name as riderName, vt.battery_manufacturer as batteryManufacturer " +
            "            from trip t " +
            "            join vehicle_test_ride_form vt on t.test_id=vt.vehicle_test_id " +
            "            join vehicle v on v.imei=t.imei " +
            "            join vehicle_model vm on v.vehicle_model_id=vm.id " +
            "            where t.start_time between ?1 and  ?2 " +
            "            and t.summary_population_status='COMPLETED' " +
            "            and vm.id=?3 ", nativeQuery = true)
    List<RiderAndBatteryManufacturer> getImeiRiderBatteryManufacturerByTimestampForAdmin(Instant from, Instant to,Long vehicleModelId);


    @Query(value = "select u.first_name from trip , users u, vehicle_test_ride_form vt " +
            "where trip.start_time>=?1 and trip.end_time<=?2 " +
            "and vt.vehicle_test_id=trip.test_id and u.id=vt.user_id " +
            "and trip.imei in ?3 " +
            "and trip.summary_population_status='COMPLETED'", nativeQuery = true)
    List<String> findTripMetaDataByTimeStampAndImei(Instant start, Instant end, List<String> imei);

    @Query(value = "select trip.imei as imei, vt.battery_manufacturer as batteryManufacturer, vt.rider_name as riderName from trip, " +
            "vehicle_test_ride_form vt, users u where vt.vehicle_test_id=trip.test_id " +
            "and trip.start_time >= ?1 and trip.end_time <=?2 " +
            "and trip.summary_population_status='COMPLETED' " +
            "and trip.imei in ?3 and u.first_name in ?4 " +
            "and u.id=vt.user_id", nativeQuery = true)
    List<RiderAndBatteryManufacturer> findTripMetaDataByRiders(Instant start, Instant end, List<String> imei, List<String> riders);

    @Query(value = "select sum(cast(td.field_value AS DOUBLE PRECISION)) as fieldValue, count(td.field_value) as count, td.field_name as fieldName " +
            "                                  from trip_details td " +
            "                                  join trip t on td.trip_id=t.id " +
            "                                  join vehicle_test_ride_form vt on t.test_id=vt.vehicle_test_id " +
            "                                  join vehicle v on v.imei=t.imei " +
            "                                  join vehicle_model vm on v.vehicle_model_id=vm.id " +
            "                                  where v.mfr_org_id= ?6 " +
            "                                  and t.start_time >= ?4 and t.end_time <=  ?5 " +
            "                                  and td.field_name in ?7 " +
            "                                  and t.summary_population_status='COMPLETED' " +
            "                                  and vt.rider_name in ?2 " +
            "                                  and t.imei  in ?1 " +
            "                                  and vt.battery_manufacturer in ?3 " +
            "                                  group by field_name ", nativeQuery = true)
    List<TestRideDetailsResponse> findWeightedAverageData(List<String> imei, List<String> riders, List<String> batteryManufacturers, Instant start, Instant to, Long orgId,List<String> filedNames);

    @Query(value = "select distinct(t.id) " +
            "                        from trip t " +
            "                        join vehicle_test_ride_form vt on t.test_id=vt.vehicle_test_id " +
            "                        join vehicle v on v.imei=t.imei " +
            "                        where t.start_time >= ?1 and  t.end_time <= ?2 " +
            "                        and t.summary_population_status='COMPLETED' " +
            "                        and t.imei in ?3 " +
            "                        and vt.rider_name in ?4 " +
            "                        and vt.battery_manufacturer in ?5 ",nativeQuery = true)
    List<Long> getTripIdsByFilter(Instant startTime, Instant endTime, List<String> imei, List<String> riders, List<String> batteryManufacturers);

    @Query(value = "SELECT MAX(td.field_value) AS maxSpeed, td.field_name as fieldName, (SELECT field_value FROM trip_details GROUP BY field_value ORDER BY COUNT(*) DESC LIMIT 1) AS frequentSpeed FROM trip t, trip_details td, vehicle_test_ride_form vt, users u where td.field_name =?1 and td.trip_id=t.id and vt.vehicle_test_id=t.test_id and t.imei in ?2 and u.id=vt.user_id and u.id in ?3 and vt.battery_manufacturer in ?4 and t.start_time >=?5 and t.end_time <=?6 GROUP BY field_name, td.trip_id order by maxSpeed desc limit 1", nativeQuery = true)
    MaxSpeedMode getModeWiseFrequentAndMaxSpeed(String mode, List<String> imei, List<Long> riders, List<String> batteryManufacturers, Instant start, Instant end);

    @Query(value = "select trip_start_city as startCity, trip_end_city as endCity, count(*) as noOfTrip " +
            "                                  from trip t " +
            "                                  join vehicle_test_ride_form vt on t.test_id=vt.vehicle_test_id " +
            "                                  join vehicle v on v.imei=t.imei " +
            "                                  where t.start_time >= ?4 and  t.end_time <= ?5 " +
            "                                  and vt.rider_name in ?3 " +
            "                                  and t.imei  in ?1 " +
            "                                  and vt.battery_manufacturer in ?2 " +
            "                                  group by trip_start_city, trip_end_city ", nativeQuery = true)
    List<ChordDiagramResponseDto> getChordDiagramData(List<String> imei, List<String> batteryManufacturers, List<String> riders, Instant start, Instant end);

    @Query(value = "select field_name as fieldName, sum(cast(td.field_value AS DOUBLE PRECISION)) as totalDistance " +
            "                                  from trip_details td " +
            "                                  join trip t on td.trip_id=t.id " +
            "                                  join vehicle_test_ride_form vt on t.test_id=vt.vehicle_test_id " +
            "                                  join vehicle v on v.imei=t.imei " +
            "                                  where t.start_time >= ?4 and  t.end_time <= ?5 " +
            "                                  and td.field_name in ('ecoDistance', 'powerDistance', 'cityDistance') " +
            "                                  and vt.rider_name in ?2 " +
            "                                  and t.imei  in ?1 " +
            "                                  and vt.battery_manufacturer in ?3 " +
            "                                  group by field_name ",nativeQuery = true)
    List<MaxSpeedMode> getModeWiseMaxSpeed(List<String> imei, List<String> riders, List<String> batteryManufacturers, Instant start, Instant end);


    @Query(value = "select field_name as fieldName, field_value as frequentSpeed, COUNT(field_value) AS frequency " +
            "                                  from trip_details td " +
            "                                  join trip t on td.trip_id=t.id " +
            "                                  join vehicle_test_ride_form vt on t.test_id=vt.vehicle_test_id " +
            "                                  join vehicle v on v.imei=t.imei " +
            "                                  where t.start_time >= ?5 and t.end_time <= ?6 " +
            "                                  and td.field_name = ?1 " +
            "                                  and vt.rider_name in ?3 " +
            "                                  and t.imei  in ?2 " +
            "                                  and vt.battery_manufacturer in ?4 " +
            "                                  GROUP BY field_value, field_name ORDER BY frequency DESC limit 1 ",nativeQuery = true)
    FrequentSpeedMode getModeWiseFrequentSpeed(String mode, List<String> imei, List<String> riders, List<String> batteryManufacturers, Instant start, Instant end);


//    List<Trip> findByTripTypeAndSummaryPopulationStatus(TripType tripType,TestRideSummaryPopulationStatus testRideSummaryPopulationStatus);

    @Query(value = "select sum(cast(td.field_value AS DOUBLE PRECISION)) as fieldValue, count(td.field_value) as count, td.field_name as fieldName " +
            "                                  from trip_details td " +
            "                                  join trip t on td.trip_id=t.id " +
            "                                  join vehicle_test_ride_form vt on t.test_id=vt.vehicle_test_id " +
            "                                  join vehicle v on v.imei=t.imei " +
            "                                  join vehicle_model vm on v.vehicle_model_id=vm.id " +
            "                                  where t.start_time >= ?4 and t.end_time <=  ?5 " +
            "                                  and td.field_name in ?6 " +
            "                                  and t.summary_population_status='COMPLETED' " +
            "                                  and vt.rider_name in ?2 " +
            "                                  and t.imei  in ?1 " +
            "                                  and vt.battery_manufacturer in ?3 " +
            "                                  group by field_name ", nativeQuery = true)
    List<TestRideDetailsResponse> findWeightedAverageDataForAdmin(List<String> imei, List<String> riders, List<String> batteryManufacturers, Instant start, Instant to, List<String> fieldNames);

    List<Trip> findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripType(String imei, VehicleTest vehicleTest, TestRideSummaryPopulationStatus status, TripType tripType);

    List<Trip> findByImeiAndVehicleTestAndSummaryPopulationStatusAndTripTypeAndUpdateSource(String imei, VehicleTest vehicleTest, TestRideSummaryPopulationStatus status, TripType tripType, UpdateSource updateSource);

    Page<Trip> findByVehicleAndSummaryPopulationStatusAndTripTypeAndStartTimeBetween(Vehicle vehicle, TestRideSummaryPopulationStatus testRideSummaryPopulationStatus,TripType tripType ,Instant from, Instant to, Pageable pageable);

    Optional<Trip> findByStartTimeAndEndTimeAndImeiAndSummaryPopulationStatusAndTripType( Instant startTime,Instant endTime,String imei, TestRideSummaryPopulationStatus status,TripType tripType);

    List<Trip> findByImeiAndSummaryPopulationStatusAndTripTypeAndUpdateSourceAndEndTimeBetweenOrderByStartTime(String imei,TestRideSummaryPopulationStatus testRideSummaryPopulationStatus, TripType tripType, UpdateSource updateSource,Instant from,Instant to);

    @Query(value = "select * from trip where imei=?1 and summary_population_status= ?2 " +
            "and trip_type= ?3 " +
            "and ((start_time between ?4 and ?5 ) " +
            "or (end_time between ?4 and ?5 )" +
            "or (?4 between start_time and end_time ) "+
            "or (?5 between start_time and end_time ) "+
            " ) order by start_time "
            ,nativeQuery = true)
    List<Trip> getNearestTripsOfDataDelayInterval(String imei, String summaryPopulationStatus, String tripType, Instant t1, Instant t2);

    @Query(value = " SELECT * FROM trip WHERE imei = ?1 AND end_time - start_time >= interval '5 minutes' AND summary_population_status = ?2 ORDER BY start_time DESC LIMIT 1 ", nativeQuery = true)
    Optional<Trip> findLatestTripForVehicleWhereDurationGreaterThanFiveMinutes(String imei, String tripSummaryPopulationStatus);

    @Query(value = " SELECT * from trip WHERE trip_type = ?1 and summary_population_status = ?2 AND user_id = ?3 " , nativeQuery = true)
    List<Trip> findTripByTripTypeAndSummaryPopulationStatusAndUser(String tripType , String tripSummaryPopulationStatus , Long userId);

    @Query(value = "SELECT COUNT(*) FROM trip WHERE trip_type = ?1 AND summary_population_status = ?2 AND user_id = ?3 AND start_time >= ?4 AND end_time <= ?5", nativeQuery = true)
    Long countTripsByTripTypeAndSummaryPopulationStatusAndUserAndBetweenTimestamp(String tripType, String status, Long userId, Instant startTime, Instant endTime);

    @Query(value = "SELECT * from trip WHERE trip_type = ?1 AND summary_population_status = ?2 AND user_id = ?3 AND vehicle_id = ?4 ",nativeQuery = true)
    List<Trip> findTripByTripTypeAndSummaryPopulationStatusAndUserAndImei(String tripType, String tripSummaryPopulationStatus, Long userId, Long vehicleId);

    @Query(value = "select count(*) from trip where summary_population_status = ?1 and start_time >= ?2 and end_time <= ?3 and trip_type in ?4 and vehicle_id in ?5 ", nativeQuery = true)
    Long countTripBySummaryPopulationStatusAndTripTypeInAndVehicleListIn(String tripSummaryPopulationStatus, Instant startTime, Instant endTime, List<String> tripTypeList, List<Long> vehicleId);

    @QueryHints(value = {
            @QueryHint(name = HINT_FETCH_SIZE, value = "500"),
    })
    Stream<Trip> findByTripTypeAndSummaryPopulationStatus(TripType tripType,
                                                          TestRideSummaryPopulationStatus status);

    Long countByVehicleAndSummaryPopulationStatusAndTripTypeIn(Vehicle vehicle, TestRideSummaryPopulationStatus testRideSummaryPopulationStatus, Collection<TripType> tripTypes);

    @Query(value = "SELECT SUM(CAST(td.field_value AS DOUBLE PRECISION)) " +
            "FROM trip_details td " +
            "JOIN trip t ON td.trip_id = t.id " +
            "WHERE t.imei = :imei " +
            "AND t.trip_type IN (:tripType) " +
            "AND t.summary_population_status = :summaryPopulationStatus " +
            "AND td.field_name = :tripField",
            nativeQuery = true)
    Double findTotalDistanceByImei(@Param("imei") String imei,
                                   @Param("tripType") List<String> tripType,
                                   @Param("summaryPopulationStatus") String summaryPopulationStatus,
                                   @Param("tripField") String tripField);


    @Query(value = """
            SELECT t.* FROM trip t
            JOIN trip_details d ON d.trip_id = t.id
            WHERE t.vehicle_id = :vehicleId
              AND t.summary_population_status = :status
              AND d.field_name = :fieldName
              AND CAST(d.field_value AS FLOAT) >= 0.1
              AND EXTRACT(EPOCH FROM (t.end_time - t.start_time)) >= 60
            ORDER BY t.start_time DESC
            LIMIT 1
            """, nativeQuery = true)
    Trip findLatestValidTripByVehicle(Long vehicleId, String status , String fieldName);

    @Query(value = """
    SELECT t.* FROM trip t
    JOIN trip_details d ON d.trip_id = t.id
    WHERE t.vehicle_id = :vehicleId
      AND t.summary_population_status = :status
      AND t.trip_type IN (:tripTypes)
      AND t.start_time BETWEEN :from AND :to
      AND d.field_name = 'totalDistance'
      AND CAST(d.field_value AS float) >= 0.1
      AND EXTRACT(EPOCH FROM (t.end_time - t.start_time)) >= 60
    ORDER BY t.start_time DESC
    """, nativeQuery = true)
    Page<Trip> findValidTripsWithDistanceAndDuration(Long vehicleId, String status, List<String> tripTypes, Instant from, Instant to, Pageable pageable);

    @Query(value = """
            SELECT v.id AS vehicleId,
            v.imei AS imei,
            v.chassis_number AS chassisNumber,
            v.latitude AS latitude,
            v.longitude AS longitude,
            vm.model_no AS modelNo,
            COUNT(t.vehicle_id) AS tripCount
            FROM evdata.trip t
            JOIN evdata.vehicle v ON t.vehicle_id = v.id
            JOIN evdata.vehicle_model vm ON v.vehicle_model_id = vm.id
            WHERE t.start_time BETWEEN ?1 AND ?2
            AND t.imei IN (?3)
            AND t.trip_type IN (?4)
            AND t.summary_population_status = ?5
            GROUP BY v.id, v.imei, v.chassis_number, v.latitude, v.longitude, vm.model_no
            ORDER BY tripCount DESC
            """,
            nativeQuery = true)
    Page<VehicleTripSummaryProjection> findVehicleTripSummaries(
            Instant startDate,
            Instant endDate,
            List<String> imeis,
            List<String> tripTypes,
            String summaryStatus,
            Pageable pageable);
}

