package com.nichesolv.evahanam.trip.repository;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.trip.jpa.VehicleEventMonitor;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.stream.Stream;

import static org.hibernate.jpa.HibernateHints.HINT_FETCH_SIZE;

@Repository
public interface VehicleEventMonitorRepository extends JpaRepository<VehicleEventMonitor,Long> {


    Optional<VehicleEventMonitor> findByVehicle(Vehicle vehicle);

    /***
     * Find all active running vehicles which have run for more than 1 minutes and running time is a multiple of 60 and stream back the data
     * @param updatedOn
     * @return
     */
    @QueryHints(value = {
            @QueryHint(name = HINT_FETCH_SIZE, value = "5")
    })
    @Query(value = "select * from vehicle_event_monitor where running_time_last_updated_on>now()-interval'15 sec' and running_time%60=0",nativeQuery = true)
    Stream<VehicleEventMonitor> findActiveRunningVehicles();
}
