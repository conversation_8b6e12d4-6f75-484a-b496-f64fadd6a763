package com.nichesolv.evahanam.trip.repository;

import com.nichesolv.evahanam.evApp.jpa.UserVehicleConnection;
import com.nichesolv.evahanam.trip.dto.UserTripHistoryProjection;
import com.nichesolv.evahanam.trip.dto.UserVehicleTripProjection;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.jpa.UserVehicleTrip;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

@Repository
public interface UserVehicleTripRepository extends JpaRepository<UserVehicleTrip, Long> {

    List<UserVehicleTrip> findByTrip(Trip trip);

    List<UserVehicleTrip> findByTripAndStatus(Trip trip, TestRideSummaryPopulationStatus status);

    List<UserVehicleTrip> findByUserVehicleConnectionAndStatus(UserVehicleConnection userVehicleConnection, TestRideSummaryPopulationStatus status);

    @Query(value = " SELECT " +
            "        t.id AS tripId, " +
            "        vgd.registration_number AS regNo, " +
            "        t.start_time AS startTime, " +
            "        t.end_time AS endTime, " +
            "        t.trip_start_neighbourhood AS tripStartNeighbourhood, " +
            "        t.trip_start_city AS tripStartCity, " +
            "        t.trip_start_state AS tripStartState, " +
            "        t.trip_end_neighbourhood AS tripEndNeighbourhood, " +
            "        t.trip_end_city AS tripEndCity, " +
            "        t.trip_end_state AS tripEndState, " +
            "        vci.image_id AS imageIds " +
            "    FROM " +
            "    user_vehicle_connections uvc " +
            "    JOIN " +
            "        user_vehicle_trip t ON t.connection_id =  uvc.id " +
            "    JOIN " +
            "        vehicle v ON uvc.vehicle_id = v.id " +
            "    JOIN " +
            "        vehicle_registration_details vgd ON vgd.vehicle_id = v.id " +
            "    JOIN " +
            "        vehicle_model vm ON v.vehicle_model_id = vm.id " +
            "    INNER JOIN " +
            "        vehicle_color_images vci ON vm.id = vci.vehicle_model_id " +
            "    WHERE " +
            "        uvc.user_id = ?1 " +
            "        AND v.mfr_org_id = ?2 " +
            "        AND t.status = 'COMPLETED' " +
            "        AND vci.color_model_id = v.color_model_id", nativeQuery = true)
    List<UserTripHistoryProjection> findUserTripHistory(Long userId, Long orgId, Pageable pageable);

    @Query(
            value = "SELECT uvt.start_time as startTime, uvt.end_time as endTime , uvtd.field_name as fieldName, uvtd.field_value as fieldValue " +
                    "FROM user_vehicle_connections uvc " +
                    "JOIN user_vehicle_trip uvt ON uvc.id = uvt.connection_id " +
                    "JOIN user_vehicle_trip_details uvtd ON uvtd.user_vehicle_trip_id = uvt.id " +
                    "WHERE uvt.status = ?1 " +
                    "AND uvc.user_id = ?2 " +
                    "AND uvtd.field_name = ?3 AND uvt.start_time >= ?4 AND uvt.end_time <= ?5",
            nativeQuery = true
    )
    List<UserVehicleTripProjection> findUserVehicleTripHistoryDetails(String tripSummaryPopulationStatus, Long userId, String fieldName, Instant startTime, Instant endTime);

    @Query(
            value = "SELECT uvt.id as id , uvt.start_time as startTime, uvt.end_time as endTime, uvtd.field_name as fieldName, uvtd.field_value as fieldValue , " +
                    "uvt.trip_start_city as tripStartCity , uvt.trip_start_neighbourhood as tripStartNeighbourhood , " +
                    "uvt.trip_start_state as tripStartState , uvt.trip_end_neighbourhood as tripEndNeighbourhood , " +
                    "uvt.trip_end_city as tripEndCity , uvt.trip_end_state as tripEndState FROM user_vehicle_connections uvc " +
                    "JOIN user_vehicle_trip uvt ON uvc.id = uvt.connection_id " +
                    "JOIN user_vehicle_trip_details uvtd ON uvtd.user_vehicle_trip_id = uvt.id " +
                    "WHERE uvt.status = ?1 " +
                    "AND uvc.user_id = ?2 " +
                    "AND uvtd.field_name = ?3 and uvc.vehicle_id = ?4 ",
            nativeQuery = true
    )
    List<UserVehicleTripProjection> findUserVehicleTripHistoryDetailsByImei(String tripSummaryPopulationStatus, Long userId, String fieldName, Long vehicleId , Pageable pageable);

    @Query(
            value = "SELECT uvt.* from user_vehicle_connections uvc JOIN user_vehicle_trip uvt on uvc.id = uvt.connection_id " +
                    " where uvt.status = ?1 AND uvc.vehicle_id ?2 and uvc.user_id = ?3 ",nativeQuery = true
    )
    List<UserVehicleTrip> findUserVehicleTrip(String tripSummaryPopulationStatus, Long vehicleId, Long userId);
}
