package com.nichesolv.evahanam.loader;

import com.nichesolv.evahanam.vehicle.dto.AddPartRequestDto;
import com.nichesolv.evahanam.vehicle.dto.PartDto;
import com.nichesolv.evahanam.vehicle.jpa.Part;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.PartRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import com.nichesolv.evahanam.vehicle.service.PartService;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.jpa.PartModel;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.evahanam.vehicleModel.repository.PartModelRepository;
import com.nichesolv.evahanam.vehicleModel.repository.VehicleModelRepository;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.usermgmt.user.model.common.AddressImpl;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfile;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfileImpl;
import com.nichesolv.usermgmt.user.repository.common.AddressRepository;
import com.nichesolv.usermgmt.user.repository.organisation.OrganisationProfileRepository;
import com.nichesolv.usermgmt.user.repository.user.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZoneId;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Slf4j
//@Component
public class GsmDataLoader implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private PartRepository partRepository;

    @Autowired
    private VehicleModelRepository vehicleModelRepository;

    @Autowired
    private CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    private PartModelRepository partModelRepository;

    @Autowired
    private OrganisationProfileRepository organisationProfileRepository;

    @Autowired
    private AddressRepository addressRepository;

    @Autowired
    IVehicleService vehicleService;

    @Autowired
    private PartService partService;

    @Autowired
    UserRepository userRepository;

    private static final int pageSize = 50;

    @Override
    @Transactional
    public void onApplicationEvent(ApplicationReadyEvent event) {
        CustomOrganisation gsmOrganisation = findOrCreateOrganisation();
        PartModel partModel = findOrCreatePartModel(gsmOrganisation);
        log.info("after creation of partModel");
        associatePartModelWithVehicles(partModel);
        log.info("after association of part model to vehicle model");
        createAndAssignGsmPartsToVehicles(partModel, gsmOrganisation);
        log.info("done");
    }

    private CustomOrganisation findOrCreateOrganisation() {
        Optional<CustomOrganisation> customOrganisation = Optional.ofNullable(customOrganisationRepository.findByName("ajjas"));
        CustomOrganisation gsmOrganisation;
        if (customOrganisation.isEmpty()) {
            OrganisationProfile gsmOrganisationProfile = createOrganisationProfile();
            gsmOrganisation = createCustomOrganisation(gsmOrganisationProfile);
            customOrganisationRepository.save(gsmOrganisation);
        }
        else{
            gsmOrganisation = customOrganisation.get();
        }
        return gsmOrganisation;
    }

    private OrganisationProfile createOrganisationProfile() {
        OrganisationProfile gsmOrganisationProfile = new OrganisationProfileImpl();
        gsmOrganisationProfile.setName("ajjas");
        gsmOrganisationProfile.setEmail("<EMAIL>");
        gsmOrganisationProfile.setPhoneNumber("9741141000");
        gsmOrganisationProfile.setDescription("TCU manufacturer");
        AddressImpl address = new AddressImpl();
        address.setAddressLine1("JP Nagar");
        address.setAddressLine2("JP Nagar");
        address.setCity("Bengaluru");
        address.setCountry("India");
        address.setPostalCode("560078");
        addressRepository.save(address);
        gsmOrganisationProfile.setAddress(address);
        organisationProfileRepository.save(gsmOrganisationProfile);
        return gsmOrganisationProfile;
    }

    private CustomOrganisation createCustomOrganisation(OrganisationProfile gsmOrganisationProfile) {
        CustomOrganisation gsmOrganisation = new CustomOrganisation();
        gsmOrganisation.setUrlSlug("ajjas");
        gsmOrganisation.setOrganisationProfile(gsmOrganisationProfile);
        gsmOrganisation.setOrganisationType(OrganisationType.MANUFACTURER);
        gsmOrganisation.setIsCommercial(true);
        gsmOrganisation.setIsLoginEnabled(true);
        return gsmOrganisation;
    }

    private PartModel findOrCreatePartModel(CustomOrganisation gsmOrganisation) {
        Optional<PartModel> partModelOpt = partModelRepository.findByName("ajjas-1");
        PartModel partModel;
        if (partModelOpt.isEmpty()) {
            partModel = new PartModel();
            partModel.setPartType(PartType.GSM);
            partModel.setManufacturer(gsmOrganisation);
            partModel.setName("ajjas-1");
            partModel.setDescription("ajjas-1 model of gsm");
            partModelRepository.save(partModel);
        } else {
            partModel = partModelOpt.get();
        }
        return partModel;
    }

    private void associatePartModelWithVehicles(PartModel partModel) {
        List<VehicleModel> vehicleModelList = vehicleModelRepository.findAll();
        log.info("Found {} VehicleModels", vehicleModelList.size());
        log.info("Starting association of PartModel with VehicleModels");
        for (VehicleModel vehicleModel : vehicleModelList) {
            Set<PartModel> partModels = vehicleModel.getPartModels();
            if (partModels == null) {
                log.warn("partModels is null for VehicleModel: {}", vehicleModel.getId());
                partModels = new HashSet<>();
                partModels.add(partModel);  // Add partModel to the newly initialized set
                vehicleModel.setPartModels(partModels);  // Set the updated set to the vehicle model
                log.info("Initialized partModels and added PartModel, new size: {}", partModels.size());
            } else if (!partModels.contains(partModel)) {
                log.info("Associating PartModel with VehicleModel: {}", vehicleModel.getId());
                partModels.add(partModel);  // Add partModel if it's not already present
                vehicleModel.setPartModels(partModels);  // Update the vehicle model with the modified set
            }
            log.info("partModel set contains partModel already");
        }
        log.info("Saving updated VehicleModels to the repository");
        vehicleModelRepository.saveAll(vehicleModelList);
    }

    private void createAndAssignGsmPartsToVehicles(PartModel partModel, CustomOrganisation gsmOrganisation) {
        Pageable pageable = PageRequest.of(0, pageSize);
        Page<Vehicle> vehiclePage;
        do {
            vehiclePage = vehicleRepository.findAll(pageable);
            log.info("total size {}", vehiclePage.getTotalElements());
            for (Vehicle vehicle : vehiclePage.getContent()) {
                // Check if the Part already exists in the repository
                Optional<Part> existingPartOpt = partRepository.findBySerialNumberAndManufacturerAndPartType(
                        vehicle.getImei(), gsmOrganisation, PartType.GSM);
                Part part;
                if (existingPartOpt.isEmpty()) {
                    PartDto partDto = new PartDto();
                    partDto.setPartType(PartType.GSM);
                    partDto.setPartModelName("ajjas-1");
                    partDto.setSerialNumber(vehicle.getImei());
                    partDto.setManufacturerName("ajjas");
                    partDto.setManufacturedDate(vehicle.getMfrDate().atStartOfDay(ZoneId.of("UTC")).toInstant());
                    partService.addPart(partDto);
                    part = partRepository.findBySerialNumberAndManufacturerAndPartType(vehicle.getImei(), gsmOrganisation, PartType.GSM).get();
                } else {
                    part = existingPartOpt.get();
                }
                if(vehicle.getVehicleParts() == null || !vehicle.getVehicleParts().contains(part)){
                    AddPartRequestDto addPartRequestDto = new AddPartRequestDto(vehicle.getImei() , part.getSerialNumber(),null,PartType.GSM,"ajjas");
                    vehicleService.addPartToVehicle(addPartRequestDto , userRepository.findByEmailIgnoreCase("<EMAIL>").get());
                }
            }
            pageable = pageable.next();
        } while (vehiclePage.hasNext());
    }

//    private Part createNewPart(PartModel partModel, CustomOrganisation gsmOrganisation, String serialNumber) {
//        Part part = new Part();
//        part.setPartType(PartType.GSM);
//        part.setPartModel(partModel);
//        part.setSerialNumber(serialNumber);
//        part.setManufacturer(gsmOrganisation);
//        part.setManufacturedDate(Instant.now());
//        return part;
//    }
//
//    private void updateVehicleParts(Vehicle vehicle, Part part) {
//        Set<Part> vehicleParts = vehicle.getVehicleParts();
//        if (vehicleParts == null) {
//            vehicleParts = new HashSet<>();
//            vehicle.setVehicleParts(vehicleParts);
//        }
//        if (!vehicleParts.contains(part)) {
//            vehicleParts.add(part);
//        }
//    }


}
