package com.nichesolv.evahanam.loader;

import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.jpa.TripDetails;
import com.nichesolv.evahanam.trip.repository.TripDetailsRepo;
import com.nichesolv.evahanam.trip.repository.TripRepository;
import com.nichesolv.evahanam.trip.service.TripSummaryServiceImpl;
import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class TyrePressureLatestTripDataLoader implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    TripRepository tripRepository;

    @Autowired
    TripSummaryServiceImpl tripSummaryService;

    @Autowired
    TripDetailsRepo tripDetailsRepo;

    @Transactional
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        CustomOrganisation customOrganisation = customOrganisationRepository.findByName("NDS Eco Motors");
        List<Vehicle> vehicleList = vehicleRepository.findAllByManufacturerAndOperationStatus(customOrganisation, OperationStatus.ACTIVE);
        log.debug("size {}", vehicleList.size());
        for (Vehicle vehicle : vehicleList) {
            Optional<Trip> trip = tripRepository.findLatestTripForVehicleWhereDurationGreaterThanFiveMinutes(vehicle.getImei(), TestRideSummaryPopulationStatus.COMPLETED.name());
            if (trip.isPresent()) {
                log.debug("trip id {}", trip.get().getId());
                TripDetails tripDetailMotorDistance = tripDetailsRepo.findByTripIdxTripAndTripIdxFieldNameAndTripIdxTimestamp(trip.get(), "motorDistance", trip.get().getStartTime());
                TripDetails tripDetailGpsDistance = tripDetailsRepo.findByTripIdxTripAndTripIdxFieldNameAndTripIdxTimestamp(trip.get(), "gpsDistance", trip.get().getStartTime());
                TripDetails tripDetailTyrePressureThreshold = tripDetailsRepo.findByTripIdxTripAndTripIdxFieldNameAndTripIdxTimestamp(trip.get(), "tyrePressureThreshold", trip.get().getStartTime());
                if (tripDetailMotorDistance == null || tripDetailGpsDistance == null || tripDetailTyrePressureThreshold == null) {
                    log.debug(" one of the field names are missing ");
                    tripSummaryService.saveGpsMotorDistanceAndTyrePressureThreshold(trip.get());
                } else {
                    log.debug("All trip details already present. Skipping trip id: {}", trip.get().getId());
                }
            } else {
                log.debug("There does not exist a trip where duration is greater than 5 minutes");
            }
        }
    }

}
