package com.nichesolv.evahanam.util;

import com.nichesolv.evahanam.common.dto.UserAuthTokenContext;
import com.nichesolv.evahanam.evApp.exception.OrganisationNotFoundException;
import com.nichesolv.evahanam.evApp.exception.UserProfileNotFoundException;
import com.nichesolv.evahanam.evApp.jpa.VehicleRider;
import com.nichesolv.evahanam.evApp.repository.VehicleRiderRepository;
import com.nichesolv.evahanam.vehicle.exception.VehicleException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicle.service.VehicleService;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.nds.repository.CustomUserRepository;
import com.nichesolv.nds.util.MessageBundle;
import com.nichesolv.usermgmt.user.model.authorization.Role;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class HttpRequestOriginUtil {

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;
    @Autowired
    CustomUserRepository userRepository;
    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    UserAuthTokenContext userAuthTokenContext;

    @Autowired
    VehicleRiderRepository vehicleRiderRepository;

    @Autowired
    MessageBundle messageBundle;

    public CustomOrganisation getUserOrganisation(HttpServletRequest request){

        String sub = SecurityContextHolder.getContext().getAuthentication().getName();
        Long currentOrgId= userAuthTokenContext.getCurrentOrgId();
        log.info("currentOrgId in authToken {} ",currentOrgId);

        CustomUser user = userRepository.findByEmailIgnoreCase(sub).orElseThrow(() -> new UserProfileNotFoundException("User does not exists"));
        String finalSlug = getOrganisationUrlSlug(request);
        List<CustomOrganisation> organisation = customOrganisationRepository.findByUser(user).stream().filter(e -> e.getUrlSlug().equals(finalSlug) || e.getOrganisationType().equals(OrganisationType.ADMINISTRATOR)).toList();
        CustomOrganisation selectedOrganisation=(CustomOrganisation) customOrganisationRepository.findById(currentOrgId).orElseThrow(()->new OrganisationNotFoundException(messageBundle.getMessage("ORG_NOT_FOUND_WITH_ORG_INFO",currentOrgId)));
        log.debug("Issue is ++++" + user.getEmail() + "     -==== " + finalSlug + "test");
        if (organisation.isEmpty()) {
            throw new AccessDeniedException("Access Denied");
        }
        log.info("selected org id {}",selectedOrganisation.getId());
        return selectedOrganisation;
    }

    private String getOrganisationUrlSlug(HttpServletRequest request) {
        String slug = request.getHeader("origin");
        if (Optional.ofNullable(slug).isPresent()) {
            slug = slug.split("//")[1].split("\\.")[0];
        } else {
            slug = request.getServerName().split("//")[0].split("\\.")[0];
        }
        log.debug("The Url slug is " + slug);
        return slug;
    }

    public void checkVehicleBelongToOrganisation(String identifier, Long id, HttpServletRequest request) throws AccessDeniedException {
        try {
            String email = SecurityContextHolder.getContext().getAuthentication().getName();
            log.debug("email :{}", email);
            CustomUser user = userRepository.findByEmailIgnoreCase(email).orElseThrow(() -> new UsernameNotFoundException("User does not exists"));
            List<CustomOrganisation> customOrganisationList = customOrganisationRepository.findByUser(user).stream().toList();
            if (customOrganisationList.isEmpty()) {
                throw new AccessDeniedException("Access Denied");
            }
            List<CustomOrganisation> adminOrg = customOrganisationList.stream().filter(e -> e.getOrganisationType().equals(OrganisationType.ADMINISTRATOR)).toList();
            if (adminOrg.isEmpty()) {
                Vehicle vehicle = vehicleRepository.findVehicleByIdentifier(identifier).orElseThrow(() -> new VehicleException(messageBundle.getMessage("VEHICLE_NOT_FOUND")));
                CustomOrganisation customOrganisation = customOrganisationList.get(0);
                boolean isBelongToThisOrganisation = false;
                if (Optional.ofNullable(id).isPresent()) {
                    isBelongToThisOrganisation = vehicleRepository.existsByIdAndManufacturerOrDealership(id, (CustomOrganisation) vehicle.getManufacturer(), (CustomOrganisation) vehicle.getManufacturer());
                } else if (Optional.ofNullable(identifier).isPresent()) {
                    Long dealerId = vehicle.getManufacturer().getId();
                    if (vehicle.getDealership() != null) {
                        dealerId = vehicle.getDealership().getId();
                    }
                    isBelongToThisOrganisation = vehicleRepository.existsByVehicleIdentifierAndManufacturerOrDealershipOrOwner(identifier, vehicle.getManufacturer().getId(), dealerId, vehicle.getOwner().getId());
                }
                else {
                    throw new IllegalArgumentException("provide imei or id");
                }
                List<CustomOrganisation> b2cOrg = customOrganisationList.stream().filter(e -> e.getOrganisationType().equals(OrganisationType.B2CCUSTOMER)).toList();
                boolean existsRiderAndVehicle = vehicleRiderRepository.existsByRiderAndVehicle(user, vehicle);
                if (!existsRiderAndVehicle && (!isBelongToThisOrganisation)) {
                    throw new AccessDeniedException("Access denied");
                }
            }
        } catch (Exception e) {
            log.error("Error check :" + identifier, e);
            throw e;

        }
    }
}
