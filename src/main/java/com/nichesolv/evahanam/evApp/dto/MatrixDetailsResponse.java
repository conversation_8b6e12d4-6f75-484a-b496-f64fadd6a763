package com.nichesolv.evahanam.evApp.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MatrixDetailsResponse {
    private List<MatrixDetailsDto> running = new ArrayList<>();
    private List<MatrixDetailsDto> trips = new ArrayList<>();
    private List<MatrixDetailsDto> alerts = new ArrayList<>();
    private List<MatrixDetailsDto> alarms = new ArrayList<>();
}
