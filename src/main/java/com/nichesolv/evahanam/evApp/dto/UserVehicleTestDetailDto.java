package com.nichesolv.evahanam.evApp.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserVehicleTestDetailDto extends UserTripDto {
    Double rideTime; // for rider role and with vehicle connected
    Double rideDistance; // for rider role and with vehicle connected
    int testRecords; // for rider role and with vehicle connected
    Boolean isManual;
    Long tripId;
    Long testId;
    Long testRideCount; // only for rider role and with vehicle connected
    Long totalAlarmCount; // for executive role
    Long totalAlertCount; // for executive role
    Long totalTripCount; // total vehicle trip count for executive role and user_vehicle_trip count for the rider role
    Long totalRunning; // executive role
    Long totalVehicles; // executive role


}
