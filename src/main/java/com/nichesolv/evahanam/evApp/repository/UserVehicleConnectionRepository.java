package com.nichesolv.evahanam.evApp.repository;

import com.nichesolv.evahanam.evApp.dto.IUserVehicleLastConnectionDetailsDto;
import com.nichesolv.evahanam.evApp.dto.UserVehicleConnectionProjection;
import com.nichesolv.evahanam.evApp.jpa.UserVehicleConnection;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.nds.model.user.CustomUser;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface UserVehicleConnectionRepository extends JpaRepository<UserVehicleConnection, Long> {

    List<UserVehicleConnection> findByUserAndEndOn(CustomUser customUser, Instant date);

    Optional<UserVehicleConnection> findByUserAndVehicle(CustomUser user, Vehicle vehicle);

    List<UserVehicleConnection> findUserByVehicleAndEndOn(Vehicle vehicle, Instant date);

    @Query(value = "select uv.imei, uv.start_on as startOn, uv.end_on as endOn, uv.model_name as modelName, uv.reg_no as regNo, uv.odometer as distanceCovered, uv.soc , uv.image_id as imageId \n" +
            "             from (select DISTINCT ON(imei) imei, name as model_name, registration_number as reg_no,soc,odometer, end_on, start_on, image_id, organisation_type from vehicle \n" +
            "            left join organisations on owner_org_id=organisations.id  \n" +
            "            left join vehicle_registration_details on vehicle.id=vehicle_registration_details.vehicle_id \n" +
            "            left join vehicle_model on vehicle_model_id=vehicle_model.id \n" +
            "            left join part_model on part_model.id = vehicle_model.id\t   \n" +
            "            left join user_vehicle_connections on user_vehicle_connections.vehicle_id=vehicle.id \n" +
            "            left join vehicle_color_images on vehicle_model.id=vehicle_color_images.vehicle_model_id \n" +
            "            where  vehicle.mfr_org_id=?6 and  user_vehicle_connections.user_id = ?1 and vehicle_color_images.color_model_id = vehicle.color_model_id  order by imei,start_on desc) as uv \n" +
            "            where (uv.imei ilike %?2% OR uv.reg_no ilike %?3% OR uv.model_name ilike %?4% ) and uv.organisation_type=?5 ", nativeQuery = true)
    List<IUserVehicleLastConnectionDetailsDto> getUserVehiclesLastConnectionDetailsByUserProfileIdAndImeiOrRegNoOrModelName(Long userId, String imei, String regNo, String modelName, String orgType, Long orgId, Pageable pageable);

    @Query(value = "select SUM(uvc.distance_covered) from user_vehicle_connections uvc, vehicle v WHERE uvc.user_id =?1  and v.id=uvc.vehicle_id and v.mfr_org_id=?2", nativeQuery = true)
    Float getUserB2BDistanceCovered(Long userId, Long orgId);

    @Query(value = "select * from user_vehicle_connections where " +
            "vehicle_id = ?1 " +
            "and end_on is not null " +
            "and (start_on between ?3 and ?4 " +
            "or end_on >= ?3 ) " +
            "order by start_on desc ", nativeQuery = true)
    List<UserVehicleConnection> findRecentConnections(Long vehicleId, LocalDateTime lowerTimeLimit, LocalDateTime upperTimeLimit);

    @Query(value = "select * from user_vehicle_connections where vehicle_id = ?1 and (start_on between ?2 and ?3 " +
            "or end_on between ?2 and ?3 or (start_on <= ?3 and end_on is null )) order by start_on ",nativeQuery = true)
    List<UserVehicleConnection> findByVehicleAndStartOnOrEndOnBetweenOrderByStartOn(Long vehicleId,LocalDateTime from,LocalDateTime to);

    @Query(value = "select uvc.user_id as userId, uvc.vehicle_id as vehicleId, v.imei as imei, uvc.start_on as startOn, uvc.end_on as endOn from user_vehicle_connections as uvc, vehicle as v where uvc.user_id=?1 and v.id=uvc.vehicle_id and v.mfr_org_id = ?8 and ((uvc.start_on between ?2 and ?3) or (uvc.end_on between ?4 and ?5) or (uvc.end_on is null) or (uvc.start_on < ?6 and uvc.end_on > ?7)) order by uvc.start_on asc", nativeQuery = true)
    List<UserVehicleConnectionProjection> findByStartOnOREndOnAndUser(Long user, LocalDateTime startTime1, LocalDateTime endTime1, LocalDateTime startTime2, LocalDateTime endTime2, LocalDateTime startTime3, LocalDateTime endTime3, Long orgId);
    @Query(value = "select distinct(vehicle_id) from user_vehicle_connections where user_id = ?1", nativeQuery = true)
    List<Long> getDistinctVehicleIdListByUser(Long userId);
}
