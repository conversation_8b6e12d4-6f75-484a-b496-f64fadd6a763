package com.nichesolv.evahanam.evApp.service;

import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.evApp.dto.EmergencyEventRequestDto;
import com.nichesolv.evahanam.evApp.dto.EmergencyEventResponseDto;
import com.nichesolv.evahanam.evApp.dto.EmergencyTypeDto;
import com.nichesolv.evahanam.evApp.dto.EmergencyTypeRequestDto;
import com.nichesolv.evahanam.evApp.enums.EmergencyStatus;
import com.nichesolv.evahanam.evApp.exception.EmergencyEventException;
import com.nichesolv.evahanam.evApp.exception.EmergencyTypeException;
import com.nichesolv.evahanam.evApp.exception.OrganisationNotFoundException;
import com.nichesolv.evahanam.evApp.jpa.EmergencyEvent;
import com.nichesolv.evahanam.evApp.jpa.EmergencyType;
import com.nichesolv.evahanam.evApp.repository.EmergencyEventRepository;
import com.nichesolv.evahanam.evApp.repository.EmergencyTypeRepository;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleLocationData;
import com.nichesolv.evahanam.telemetryData.repository.LocationDataRepository;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.service.VehicleService;
import com.nichesolv.evahanam.vehicleTests.dto.EmergencyEventDto;
import com.nichesolv.evahanam.vehicleTests.exception.VehicleTestException;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmergencyServiceImpl implements EmergencyService{

    @Autowired
    EmergencyTypeRepository emergencyTypeRepository;

    @Autowired
    EmergencyEventRepository emergencyEventRepository;

    @Autowired
    VehicleService vehicleService;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    LocationDataRepository locationDataRepository;


    @Override
    public List<String> getEmergencyTypes(String identifier) {
        try {
            Vehicle vehicle = vehicleService.getVehicleByAnyId(identifier);
            return emergencyTypeRepository.findByOrganisation((CustomOrganisation) vehicle.getManufacturer())
                    .stream()
                    .map(EmergencyType::getDescription)
                    .collect(Collectors.toList());
        } catch (Exception exception) {
            log.error("Unexpected error getting emergency types for vehicle identifier: {} ", identifier, exception);
            throw exception;
        }
    }

    @Override
    public EmergencyTypeDto getEmergencyTypeId(Long emergencyTypeId) {
        EmergencyType emergencyType = emergencyTypeRepository.findById(emergencyTypeId)
                .orElseThrow(() -> new EmergencyTypeException(evMessageBundle.getMessage("EMERGENCY_TYPE_NOT_PRESENT", emergencyTypeId)));
        return new EmergencyTypeDto
                (emergencyType.getId()
                        , emergencyType.getDescription()
                        , emergencyType.getOrganisation().getOrganisationProfile().getName()
                        , emergencyType.getOrganisation().getId());
    }


    @Override
    @Transactional
    public void saveEmergencyType(EmergencyTypeRequestDto emergencyTypeRequestDto) {
        try {
            CustomOrganisation organisation = (CustomOrganisation) customOrganisationRepository.findById(emergencyTypeRequestDto.getOrganisationId()).
                    orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
            emergencyTypeRepository.saveAll(emergencyTypeRequestDto.getEmergencyTypes().stream().
                    map(e -> new EmergencyType(e, organisation)).
                    collect(Collectors.toList()));
        } catch (Exception exception) {
            log.error("Unexpected error creating emergencyType", exception);
            throw exception;
        }
    }

    @Override
    @Transactional
    public EmergencyEventResponseDto createEmergencyEvent(CustomUser user, EmergencyEventRequestDto request) {
        try {
            Instant now = Instant.now();
            EmergencyEvent event = new EmergencyEvent();
            Vehicle vehicle = vehicleService.getVehicleByAnyId(request.getImei());
            event.setVehicle(vehicle);

            EmergencyType emergencyType = emergencyTypeRepository.findByDescriptionAndOrganisation(
                            request.getEmergencyType(), (CustomOrganisation) vehicle.getManufacturer())
                    .orElseThrow(() -> new EmergencyTypeException(
                            evMessageBundle.getMessage("EMERGENCY_TYPE_NOT_FOUND",
                                    request.getEmergencyType(), vehicle.getManufacturer().getOrganisationProfile().getName())));

            event.setEmergencyType(emergencyType);
            event.setStatus(EmergencyStatus.REGISTERED);
            VehicleLocationData latestLocationData = locationDataRepository.findLatestLocation(vehicle.getImei());
            if (latestLocationData != null && latestLocationData.getGeoPointZM() != null) {
                event.setVehicleLastLocation(latestLocationData.getGeoPointZM());
            }

            Point userLocation = createPointZM(request.getUserLongitude(), request.getUserLatitude(), request.getUserAltitude(), (float) now.toEpochMilli());
            event.setUserLocation(userLocation);
            event.setText(request.getAdditionalInfo());
            event.setUser(user);
            event.setMfrOrg((CustomOrganisation) vehicle.getManufacturer());
            event.setRegisteredOn(now);
            EmergencyEvent savedEvent = emergencyEventRepository.save(event);
            return new EmergencyEventResponseDto(
                    savedEvent.getId(),
                    savedEvent.getStatus().toString(),
                    savedEvent.getEmergencyType().getDescription(),
                    savedEvent.getRegisteredOn().toEpochMilli()
            );
        } catch (Exception exception) {
            log.error("Unexpected error creating emergency event", exception);
            throw exception;
        }
    }

    @Override
    public EmergencyEventDto getEmergencyEventById(Long emergencyEventId) {
        EmergencyEvent emergencyEvent = emergencyEventRepository.findById(emergencyEventId)
                .orElseThrow(() -> new EmergencyEventException(evMessageBundle.getMessage("EMERGENCY_EVENT_NOT_FOUND")));

        EmergencyEventDto emergencyEventDto = new EmergencyEventDto();
        emergencyEventDto.setEmergencyEventId(emergencyEvent.getId());
        emergencyEventDto.setVehicleId(emergencyEvent.getVehicle().getId());
        emergencyEventDto.setText(emergencyEvent.getText());
        emergencyEventDto.setEmergencyType(emergencyEvent.getEmergencyType().getDescription());
        emergencyEventDto.setEmergencyStatus(emergencyEvent.getStatus().toString());
        emergencyEventDto.setRegisteredOn(emergencyEvent.getRegisteredOn());
        emergencyEventDto.setMfrOrgId(emergencyEvent.getMfrOrg().getId());
        emergencyEventDto.setMfrOrgName(emergencyEvent.getMfrOrg().getOrganisationProfile().getName());
        emergencyEventDto.setFixedOn(emergencyEvent.getFixedOn());
        emergencyEventDto.setUserId(emergencyEvent.getUser().getId());
        emergencyEventDto.setUserName(emergencyEvent.getUser().getUsername());
        return emergencyEventDto;
    }


    public Point createPointZM(Float lon, Float lat, Float alt, Float measure) {
        CoordinateXYZM coordinateXYZM = new CoordinateXYZM(lon, lat, alt, measure);
        GeometryFactory factory = new GeometryFactory(new PrecisionModel(), 4326);
        return factory.createPoint(coordinateXYZM);
    }

}