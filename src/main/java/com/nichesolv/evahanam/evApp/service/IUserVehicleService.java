package com.nichesolv.evahanam.evApp.service;

import com.nichesolv.evahanam.evApp.dto.*;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.exception.UserNotFoundException;
import com.nichesolv.nds.model.user.CustomUser;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface IUserVehicleService {
    public List<UserVehicleLastConnectionDetailsDto> findUserVehiclesLastConnectionDetails(CustomUser user, String key, Pageable pageable, OrganisationType organisationType, Long orgId) throws UserNotFoundException;

    UserVehicleInsightsStaticsDto findUserVehiclesInsightStatistics(CustomUser user, Optional<Long> startTime, Optional<Long> endTime, OrganisationType organisationType, Long orgId);

    UserVehicleInsightsStaticsDetailsDto findUserVehiclesInsightStatisticsDetails(CustomUser user, Optional<Long> startTime, Optional<Long> endTime, String dataType, String period, OrganisationType organisationType, Long orgId);

    UserVehicleTestDetailDto getUserVehicleTest(CustomUser user , String identifier , Long startTime , Long endTime , String role , Long orgId);

    List<UserVehicleTestDetailDto> getUserVehicleTripHistory(String identifier, CustomUser user, Pageable pageable);

    MatrixDetailsResponse getMatrixDetails(Long orgId, Long startDate, Long endDate, Pageable pageable);
}
