package com.nichesolv.evahanam.evApp.service;

import com.nichesolv.evahanam.common.enums.PermissionStatus;
import com.nichesolv.evahanam.common.enums.RelationType;
import com.nichesolv.evahanam.common.enums.VerificationStatus;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.evApp.dto.UserVehicleDto;
import com.nichesolv.evahanam.evApp.dto.requests.AcceptInvite;
import com.nichesolv.evahanam.evApp.dto.requests.VehicleRider;
import com.nichesolv.evahanam.evApp.exception.VehicleRiderAssociationAlreadyExistException;
import com.nichesolv.evahanam.evApp.jpa.UserVehicleConnection;
import com.nichesolv.evahanam.evApp.repository.UserVehicleConnectionRepository;
import com.nichesolv.evahanam.evApp.repository.VehicleRiderRepository;
import com.nichesolv.evahanam.vehicle.exception.VehicleNotFoundException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleRegistrationDetails;
import com.nichesolv.evahanam.vehicle.repository.VehicleRegistrationDetailsRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;

import com.nichesolv.nds.controller.organisation.OrganisationControllerImpl;
import com.nichesolv.nds.dto.organisation.CustomOrganisationDto;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.dto.user.activity.UserActivityRequestBody;
import com.nichesolv.nds.dto.user.registration.B2cUserRegistrationDto;
import com.nichesolv.nds.enums.ActivityType;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.nds.repository.CustomUserRepository;
import com.nichesolv.nds.service.user.UserServiceImpl;
import com.nichesolv.nds.service.user.activity.UserActivityService;
import com.nichesolv.usermgmt.user.dto.organisation.AddressDto;
import com.nichesolv.usermgmt.user.dto.security.GenericMessage;
import com.nichesolv.usermgmt.user.exception.UserNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Slf4j
public class VehicleRiderServiceImpl implements VehicleRiderService {

    @Autowired
    VehicleRegistrationDetailsRepository vehicleRegistrationDetailsRepository;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    CustomUserRepository customUserRepository;

    @Autowired
    UserServiceImpl userService;

    @Autowired
    VehicleRiderRepository vehicleRiderRepository;

    @Autowired
    UserVehicleConnectionRepository userVehicleConnectionRepository;

    @Autowired
    OrganisationControllerImpl organisationController;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    UserActivityService userActivityService;
    private static final Logger LOGGER = LoggerFactory.getLogger(VehicleRiderServiceImpl.class);


    @Override
    @Transactional
    public void updateVehicleRider(CustomUser owner, VehicleRider vehicleRiderBody,
                                   HttpServletRequest request) throws UserNotFoundException {
        Vehicle vehicle = null;
        CustomUser rider;
        try {

            if (owner.getPhoneNumber().equals(vehicleRiderBody.getRiderPhoneNumber())) {
                throw new IllegalArgumentException(evMessageBundle.getMessage("PHONE_NUMBER_MATCH_WITH_OWNER"));
            }

            vehicle = vehicleRegistrationDetailsRepository.findDetailsByRegistrationNumber(vehicleRiderBody.getRegNo())
                    .map(VehicleRegistrationDetails::getVehicle)
                    .or(() -> vehicleRepository.findByImei(vehicleRiderBody.getRegNo()))
                    .orElseThrow(() -> new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));

            if (Optional.ofNullable(vehicle.getUser()).filter(user -> user.getId().equals(owner.getId())).isEmpty()) {
                throw new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND"));
            }
            boolean checkOrgType = customOrganisationRepository.checkOrganisationType(vehicle.getOwner().getId(), OrganisationType.B2CCUSTOMER.toString());
            if (!checkOrgType) {
                throw new VehicleNotFoundException(evMessageBundle.getMessage("B2C_VEHICLE_NOT_FOUND"));
            }

            rider = this.createOrGetRiderByPhoneNumber(vehicleRiderBody, request, vehicle);

            Optional<com.nichesolv.evahanam.evApp.jpa.VehicleRider> vehicleRiderOptional = vehicleRiderRepository.findByOwnerAndRiderAndVehicle(owner, rider, vehicle);

            if (vehicleRiderOptional.isEmpty()) {
                com.nichesolv.evahanam.evApp.jpa.VehicleRider vehicleRider = new com.nichesolv.evahanam.evApp.jpa.VehicleRider();

                vehicleRider.setConnected(false);
                vehicleRider.setOwner(owner);
                vehicleRider.setRider(rider);
                vehicleRider.setVehicle(vehicle);
                vehicleRider.setRiderAlias(vehicleRiderBody.getRiderName());
                vehicleRider.setRelationType(vehicleRiderBody.getRelationType());
                vehicleRider.setVerificationStatus(VerificationStatus.PENDING);
                vehicleRider.setPermissionStatus(PermissionStatus.GRANTED);

                vehicleRider = vehicleRiderRepository.save(vehicleRider);

                this.saveUserKeyActivity(owner, ActivityType.RIDER_INVITE_SENT, vehicleRider.getId(), (CustomOrganisation) vehicle.getOwner());
                this.saveUserKeyActivity(owner, ActivityType.RIDER_PERMISSION_GRANTED, vehicleRider.getId(), (CustomOrganisation) vehicle.getOwner());

            } else {
                com.nichesolv.evahanam.evApp.jpa.VehicleRider currentVehicleRider = vehicleRiderOptional.get();
                if (Optional.ofNullable(vehicleRiderBody.getNewRiderPhoneNumber()).isEmpty()) {
                    if (currentVehicleRider.getPermissionStatus() == PermissionStatus.REMOVED && vehicleRiderBody.getPermissionStatus() == PermissionStatus.GRANTED) {
                        currentVehicleRider.setVerificationStatus(VerificationStatus.PENDING);
                    }
                    if (vehicleRiderBody.getPermissionStatus() == PermissionStatus.REVOKED || vehicleRiderBody.getPermissionStatus() == PermissionStatus.REMOVED) {
                        currentVehicleRider.setConnected(false);
                    }

                    currentVehicleRider.setPermissionStatus(vehicleRiderBody.getPermissionStatus());
                    currentVehicleRider.setRiderAlias(vehicleRiderBody.getRiderName());
                    currentVehicleRider.setRelationType(vehicleRiderBody.getRelationType());

                    ActivityType activityType = ActivityType.RIDER_PERMISSION_GRANTED;

                    if (vehicleRiderBody.getPermissionStatus().equals(PermissionStatus.REVOKED)) {
                        activityType = ActivityType.RIDER_PERMISSION_REVOKED;
                    } else if (vehicleRiderBody.getPermissionStatus().equals(PermissionStatus.REMOVED)) {
                        activityType = ActivityType.RIDER_PERMISSION_REMOVED;
                    }
                    vehicleRiderRepository.save(currentVehicleRider);

                    this.saveUserKeyActivity(owner, activityType, currentVehicleRider.getId(), (CustomOrganisation) vehicle.getOwner());

                } else {

                    currentVehicleRider.setPermissionStatus(PermissionStatus.REVOKED);
                    this.saveUserKeyActivity(owner, ActivityType.RIDER_PERMISSION_REVOKED, currentVehicleRider.getId(), (CustomOrganisation) vehicle.getOwner());

                    com.nichesolv.evahanam.evApp.jpa.VehicleRider newVehicleRider = new com.nichesolv.evahanam.evApp.jpa.VehicleRider();

                    vehicleRiderBody.setRiderPhoneNumber(vehicleRiderBody.getNewRiderPhoneNumber());

                    CustomUser newRider = this.createOrGetRiderByPhoneNumber(vehicleRiderBody, request, vehicle);

                    Optional<com.nichesolv.evahanam.evApp.jpa.VehicleRider> newVehicleRiderOptional = vehicleRiderRepository.findByOwnerAndRiderAndVehicle(owner, newRider, vehicle);

                    if (newVehicleRiderOptional.isEmpty()) {
                        newVehicleRider.setConnected(false);
                        newVehicleRider.setOwner(owner);
                        newVehicleRider.setRider(newRider);
                        newVehicleRider.setVehicle(vehicle);
                        newVehicleRider.setRiderAlias(vehicleRiderBody.getRiderName());
                        newVehicleRider.setRelationType(vehicleRiderBody.getRelationType());
                        newVehicleRider.setPermissionStatus(PermissionStatus.GRANTED);
                        newVehicleRider.setVerificationStatus(VerificationStatus.PENDING);

                        vehicleRiderRepository.save(currentVehicleRider);
                        newVehicleRider = vehicleRiderRepository.save(newVehicleRider);

                        this.saveUserKeyActivity(owner, ActivityType.RIDER_INVITE_SENT, newVehicleRider.getId(), (CustomOrganisation) vehicle.getOwner());
                        this.saveUserKeyActivity(owner, ActivityType.RIDER_PERMISSION_GRANTED, newVehicleRider.getId(), (CustomOrganisation) vehicle.getOwner());

                    } else {
                        throw new VehicleRiderAssociationAlreadyExistException(evMessageBundle.getMessage("VEHICLE_RIDER_ALREADY_EXIST"));
                    }

                }
            }

        }catch (Exception e){
            LOGGER.error(String.format("updateVehicleRider Error: %s", Arrays.toString(e.getStackTrace())));
        }

    }

    private void saveUserKeyActivity(CustomUser user, ActivityType type, Long vehicleRiderId, CustomOrganisation org) {

        UserActivityRequestBody userActivityRequestBody = new UserActivityRequestBody();

        userActivityRequestBody.setActivityType(type);
        userActivityRequestBody.setValue(vehicleRiderId.toString());
        userActivityRequestBody.setOrganisationId(org.getId());

        userActivityService.saveUserKeyActivity(user, userActivityRequestBody);
    }

    private CustomUser createOrGetRiderByPhoneNumber(VehicleRider vehicleRiderBody, HttpServletRequest request, Vehicle vehicle) throws UserNotFoundException {
        CustomUser rider;
        Optional<CustomUser> riderOptional = customUserRepository.findByPhoneNumber(vehicleRiderBody.getRiderPhoneNumber());
        if (riderOptional.isEmpty()) {
            B2cUserRegistrationDto b2cUserRegistrationDto = new B2cUserRegistrationDto(vehicleRiderBody.getRiderName(), "", null, vehicleRiderBody.getRiderPhoneNumber());
            CustomOrganisationDto customOrganisationDto = getCustomOrganisationDto(b2cUserRegistrationDto, vehicle);
            customOrganisationDto.setUserDetails(b2cUserRegistrationDto);
            log.info(customOrganisationDto.toString());
            GenericMessage message = organisationController.saveB2COrganisation(customOrganisationDto, request).getBody();
            log.info(message.getMessage() + "\n" + message.getError());
            rider = userService.findByPhoneNumber(b2cUserRegistrationDto.getPhoneNumber());

        } else {
            rider = riderOptional.get();
        }
        return rider;
    }

    private static CustomOrganisationDto getCustomOrganisationDto(B2cUserRegistrationDto userRegistrationDTO, Vehicle vehicle) {
        CustomOrganisation organisation = (CustomOrganisation) vehicle.getManufacturer();
        String urlSlug = organisation.getUrlSlug();
        CustomOrganisationDto customOrganisationDto = new CustomOrganisationDto();
        customOrganisationDto.setName(String.join("/",
                Objects.requireNonNullElse(userRegistrationDTO.getFirstName(), ""),
                Objects.requireNonNullElse(userRegistrationDTO.getLastName(), ""),
                Objects.requireNonNullElse(userRegistrationDTO.getPhoneNumber(), ""),
                Objects.requireNonNullElse(userRegistrationDTO.getEmail(), "")));
        customOrganisationDto.setEmail(userRegistrationDTO.getEmail());
        customOrganisationDto.setPhoneNumber(userRegistrationDTO.getPhoneNumber());
        customOrganisationDto.setDescription("B2C");
        customOrganisationDto.setOrganisationType(OrganisationType.B2CCUSTOMER);
        customOrganisationDto.setUrlSlug(urlSlug);
        customOrganisationDto.setType(new ArrayList<>());
        customOrganisationDto.setLinkedOrganisationId(new ArrayList<>());
        AddressDto address = new AddressDto();
        address.setAddressLine1(" ");
        address.setAddressLine2(" ");
        address.setCity(" ");
        address.setAdminArea(" ");
        address.setPostalCode(" ");
        address.setCountry(" ");
        customOrganisationDto.setAddress(address);
        return customOrganisationDto;
    }

    @Override
    public List<UserVehicleDto> getUserVehicleAndAccessedVehicles(CustomUser owner, Long orgId) {

        Optional<Vehicle> b2cVehicle = Optional.empty();
        List<UserVehicleDto> userAccessedVehicles = new ArrayList<>();

        List<Vehicle> vehicles = vehicleRepository.findByUser(owner);

        //considering only one b2c vehicle for owner
        b2cVehicle = vehicles.stream().max(Comparator.comparing(Vehicle::getCreatedOn));
        List<UserVehicleConnection> connections = userVehicleConnectionRepository.findByUserAndEndOn(owner, null);
        Optional<UserVehicleConnection> connection = connections.stream().filter(e ->
                customOrganisationRepository.checkOrganisationType(e.getVehicle().getOwner().getId(), OrganisationType.B2CCUSTOMER.toString()) && e.getVehicle().getManufacturer().getId().equals(orgId)
        ).findAny();

        if (connection.isPresent() && b2cVehicle.isPresent() && connection.get().getVehicle().equals(b2cVehicle.get())) {
            UserVehicleDto ownerVehicle = new UserVehicleDto();

            ownerVehicle.setImei(b2cVehicle.get().getImei());
            Optional<VehicleRegistrationDetails> vehicleRegistrationDetails = vehicleRegistrationDetailsRepository.findByVehicleId(b2cVehicle.get().getId());
            ownerVehicle.setRegNo(vehicleRegistrationDetails.isPresent() ? vehicleRegistrationDetails.get().getRegistrationNumber() : b2cVehicle.get().getImei());
            ownerVehicle.setOwnerName(owner.getFirstName());
            ownerVehicle.setProfileUrl(owner.getProfilePicUrl());
            ownerVehicle.setRiderPhoneNumber(owner.getPhoneNumber());
            ownerVehicle.setOwner(true);
            ownerVehicle.setConnected(connection.get().getEndOn() == null);
            ownerVehicle.setPermissionStatus(PermissionStatus.GRANTED);
            ownerVehicle.setVerificationStatus(VerificationStatus.VERIFIED);
            ownerVehicle.setRelationType(RelationType.OTHERS);

            userAccessedVehicles.add(ownerVehicle);

        }

        List<com.nichesolv.evahanam.evApp.jpa.VehicleRider> ownerAccessedVehicles = vehicleRiderRepository.findByRider(owner);
        ownerAccessedVehicles.stream().filter(e -> e.getVehicle().getManufacturer().getId().equals(orgId)).forEach(e -> {
            UserVehicleDto ownerAccessedVehicle = new UserVehicleDto();

            ownerAccessedVehicle.setImei(e.getVehicle().getImei());
            Optional<VehicleRegistrationDetails> vehicleRegistrationDetails = vehicleRegistrationDetailsRepository.findByVehicleId(e.getVehicle().getId());
            ownerAccessedVehicle.setRegNo(vehicleRegistrationDetails.isPresent() ? vehicleRegistrationDetails.get().getRegistrationNumber() : e.getVehicle().getImei());
            ownerAccessedVehicle.setOwnerName(e.getOwner().getFirstName());
            ownerAccessedVehicle.setRelationType(e.getRelationType());
            ownerAccessedVehicle.setProfileUrl(e.getOwner().getProfilePicUrl());
            ownerAccessedVehicle.setRiderPhoneNumber(e.getOwner().getPhoneNumber());
            ownerAccessedVehicle.setPermissionStatus(e.getPermissionStatus());
            ownerAccessedVehicle.setVerificationStatus(e.getVerificationStatus());
            ownerAccessedVehicle.setOwner(false);
            ownerAccessedVehicle.setConnected(e.isConnected());

            if (!e.getPermissionStatus().equals(PermissionStatus.REMOVED) && !e.getVerificationStatus().equals(VerificationStatus.REJECTED)) {
                userAccessedVehicles.add(ownerAccessedVehicle);
            }
        });
        return userAccessedVehicles;
    }

    @Override
    public List<UserVehicleDto> getUserRiders(CustomUser owner) {

        List<com.nichesolv.evahanam.evApp.jpa.VehicleRider> ownerVehicleRiders = vehicleRiderRepository.findByOwner(owner);

        return ownerVehicleRiders.stream().map(e -> {
            UserVehicleDto ownerRiderVehicle = new UserVehicleDto();

            ownerRiderVehicle.setImei(e.getVehicle().getImei());
            Optional<VehicleRegistrationDetails> vehicleRegistrationDetails = vehicleRegistrationDetailsRepository.findByVehicleId(e.getVehicle().getId());
            ownerRiderVehicle.setRegNo(vehicleRegistrationDetails.isPresent() ? vehicleRegistrationDetails.get().getRegistrationNumber() : e.getVehicle().getImei());
            ownerRiderVehicle.setOwnerName(e.getRiderAlias());
            ownerRiderVehicle.setProfileUrl(e.getRider().getProfilePicUrl());
            ownerRiderVehicle.setRelationType(e.getRelationType());
            ownerRiderVehicle.setRiderPhoneNumber(e.getRider().getPhoneNumber());
            ownerRiderVehicle.setPermissionStatus(e.getPermissionStatus());
            ownerRiderVehicle.setVerificationStatus(e.getVerificationStatus());
            ownerRiderVehicle.setOwner(false);
            ownerRiderVehicle.setConnected(e.isConnected());
            return ownerRiderVehicle;

        }).filter(e -> !e.getPermissionStatus().equals(PermissionStatus.REMOVED)).toList();
    }

    @Override
    @Transactional
    public void acceptAccessedVehicleInvitation(CustomUser rider, AcceptInvite riderAcceptInviteBody) {

        Vehicle vehicle = vehicleRegistrationDetailsRepository.findDetailsByRegistrationNumber(riderAcceptInviteBody.getRegNo())
                .map(VehicleRegistrationDetails::getVehicle)
                .or(() -> vehicleRepository.findByImei(riderAcceptInviteBody.getRegNo()))
                .orElseThrow(() -> new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));

        com.nichesolv.evahanam.evApp.jpa.VehicleRider vehicleRider = vehicleRiderRepository.findByRiderAndVehicle(rider, vehicle).orElseThrow(() -> new VehicleNotFoundException("RIDER_ACCESSED_VEHICLE_NOT_FOUND"));

        if (riderAcceptInviteBody.getVerificationStatus().equals(VerificationStatus.PENDING)) {
            throw new IllegalArgumentException(evMessageBundle.getMessage("VERIFICATION_STATUS_INVALID_WITH_INFO", riderAcceptInviteBody.getVerificationStatus().name()));
        } else {
            vehicleRider.setVerificationStatus(riderAcceptInviteBody.getVerificationStatus());
            ActivityType activityType = null;
            if (riderAcceptInviteBody.getVerificationStatus().equals(VerificationStatus.VERIFIED)) {
                activityType = ActivityType.RIDER_INVITE_VERIFIED;
            } else if (riderAcceptInviteBody.getVerificationStatus().equals(VerificationStatus.REJECTED)) {
                activityType = ActivityType.RIDER_INVITE_REJECTED;

            }
            if (Optional.ofNullable(activityType).isPresent()) {
                this.saveUserKeyActivity(rider, activityType, vehicleRider.getId(), (CustomOrganisation) vehicle.getOwner());

            }

        }

    }
}