package com.nichesolv.evahanam.evApp.service;

import com.nichesolv.evahanam.cache.service.VehicleCacheRetrievalService;
import com.nichesolv.evahanam.common.jpa.Image;
import com.nichesolv.evahanam.common.repository.ImageRepository;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.evApp.dto.requests.UserProfile;
import com.nichesolv.evahanam.evApp.dto.UserInfoDto;
import com.nichesolv.evahanam.evApp.dto.UserNearByPoiDto;
import com.nichesolv.evahanam.evApp.dto.UserNearByVehiclesDto;
import com.nichesolv.evahanam.evApp.dto.UserProfileDetailsDto;
import com.nichesolv.evahanam.evApp.exception.InvalidVehicleLocationException;
import com.nichesolv.evahanam.evApp.jpa.UserVehicleConnection;
import com.nichesolv.evahanam.evApp.jpa.VehicleRider;
import com.nichesolv.evahanam.evApp.repository.UserVehicleConnectionRepository;
import com.nichesolv.evahanam.evApp.repository.VehicleRiderRepository;
import com.nichesolv.evahanam.telemetryData.dto.AiVinDto;
import com.nichesolv.evahanam.telemetryData.dto.AvailableVehicleLocationData;
import com.nichesolv.evahanam.telemetryData.dto.VehicleLocationData;
import com.nichesolv.evahanam.telemetryData.repository.TelemetryBatteryRepository;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.vehicle.dto.ImageDto;
import com.nichesolv.evahanam.vehicle.exception.VehicleNotFoundException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleLatestData;
import com.nichesolv.evahanam.vehicle.jpa.VehicleRegistrationDetails;
import com.nichesolv.evahanam.vehicle.repository.VehicleLatestDataRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRegistrationDetailsRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleModel.dto.PartModelAttributeDto;
import com.nichesolv.evahanam.vehicleModel.enums.DistanceUnit;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.jpa.ColorModel;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.exception.UserNotFoundException;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.nds.repository.CustomUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@Slf4j
public class UserProfileService implements IUserProfileService {

    @Autowired
    CustomUserRepository customUserRepository;

    @Autowired
    UserVehicleConnectionRepository userVehicleConnectionRepository;

    @Autowired
    VehicleRepository vehicleRepository;
    @Autowired
    VehicleDataRepository vehicleDataRepository;

    @Autowired
    ImageRepository imageRepository;

    @Autowired
    VehicleRegistrationDetailsRepository vehicleRegistrationDetailsRepository;

    @Autowired
    IVehicleLocationService iVehicleLocationService;

    @Autowired
    TelemetryBatteryRepository telemetryBatteryRepository;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    UserOrgPriorityService userOrgPriorityService;

    @Autowired
    VehicleCacheRetrievalService vehicleCacheRetrievalService;
    @Autowired
    VehicleLatestDataRepository vehicleLatestDataRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    VehicleRiderRepository vehicleRiderRepository;

    @Override
    public UserInfoDto findByUser(Long userId, OrganisationType organisationType, Long orgId) throws UserNotFoundException {
        CustomUser customUser = customUserRepository.findById(userId).orElseThrow(() -> new UserNotFoundException(evMessageBundle.getMessage("USER_NOT_FOUND")));

        return getUserInfoDto(customUser, organisationType, orgId);
    }



    private UserInfoDto getUserInfoDto(CustomUser customUser, OrganisationType organisationType, Long orgId) {

        String connectedVehicleImei = null;
        Float totalDistance = 0f;
        // if organisation is b2c, taking total distance from user's vehicle's odometer
        List<UserVehicleConnection> connections = userVehicleConnectionRepository.findByUserAndEndOn(customUser, null);

        if (organisationType.equals(OrganisationType.B2CCUSTOMER)) {
            Optional<VehicleRider> connectedVehicleRider = vehicleRiderRepository.findByRiderAndIsConnected(customUser, true);

            if (connectedVehicleRider.isPresent()) {

                totalDistance = Optional.ofNullable(connectedVehicleRider.get().getVehicle().getTotalDistanceTraveled()).orElse(0f);
                connectedVehicleImei = connectedVehicleRider.get().getVehicle().getImei();
            } else {
                Optional<UserVehicleConnection> b2cConnection = connections.stream().filter(e ->
                        customOrganisationRepository.checkOrganisationType(e.getVehicle().getOwner().getId(), OrganisationType.B2CCUSTOMER.toString())
                ).findAny();
                if (b2cConnection.isPresent()) {
                    totalDistance = Optional.ofNullable(b2cConnection.get().getVehicle().getTotalDistanceTraveled()).orElse(0f);
                    connectedVehicleImei = b2cConnection.get().getVehicle().getImei();
                }
            }


        }
        // if organisation is b2c, taking total distance from user's connections history
        else {
            Optional<UserVehicleConnection> b2bConnection = connections.stream().filter(e ->
                    !customOrganisationRepository.checkOrganisationType(e.getVehicle().getOwner().getId(), OrganisationType.B2CCUSTOMER.toString())
            ).findAny();
            if (b2bConnection.isPresent()) {
                connectedVehicleImei = b2bConnection.get().getVehicle().getImei();
            }
            totalDistance = Optional.ofNullable(userVehicleConnectionRepository.getUserB2BDistanceCovered(customUser.getId(), orgId)).orElse(0f);
        }

        Float electricityCostPerKm = 0.24f;
        Float fuelCostPerKm = 2.5f;

        Integer equivalentCost = Math.round(fuelCostPerKm * totalDistance);
        Integer spentCost = Math.round(electricityCostPerKm * totalDistance);

        return new UserInfoDto(customUser.getFirstName(), customUser.getLastName(), Math.round(totalDistance), spentCost, equivalentCost, connectedVehicleImei);
    }

    @Override
    public UserNearByPoiDto findUserNearByPoi(CustomUser user, String latStr, String longStr, Float range, OrganisationType organisationType, Long orgId) throws UserNotFoundException {
        Float southBound, westBound, northBound, eastBound, userLat, userLong;
        Float vehicleVisibilityRange = 10f;
        Float vehicleVisibilityRangeMin = 0f;
        Float vehicleVisibilityRangeMax = 50f;

        if (Optional.ofNullable(range).isPresent() && range <= 50 && range > 0) {
            vehicleVisibilityRange = range;
        } else if (Optional.ofNullable(range).isPresent()) {
            throw new IllegalArgumentException(evMessageBundle.getMessage("VEHICLE_VISIBILITY_RANGE_EXCEEDED", vehicleVisibilityRangeMin, vehicleVisibilityRangeMax));
        }

        if (!longStr.isBlank() && !latStr.isBlank()) {
            try {
                userLat = Float.parseFloat(latStr);
                userLong = Float.parseFloat(longStr);
            } catch (NumberFormatException e) {
                throw new InvalidVehicleLocationException(evMessageBundle.getMessage("VEHICLE_LOCATION_FORMAT_INCORRECT"));
            }

        } else {
            throw new IllegalArgumentException(evMessageBundle.getMessage("LAT_OR_LONG_NULL"));
        }
        List<VehicleLocationData> vehicleLocationList = new ArrayList<>();
        CustomOrganisation customOrganisation = userOrgPriorityService.pickUserOrgPrioritizingB2BAndB2C(user, organisationType);
        List<UserVehicleConnection> connections = userVehicleConnectionRepository.findByUserAndEndOn(user, null);

        // if user's organisation is B2C Customer, taking user's vehicle's location data
        if (organisationType.equals(OrganisationType.B2CCUSTOMER)) {
            Optional<VehicleRider> connectedVehicleRider = vehicleRiderRepository.findByRiderAndIsConnected(user, true);
            if (connectedVehicleRider.isPresent()) {
                vehicleLocationList = Optional.ofNullable(connectedVehicleRider.get().getVehicle()).map(e -> new VehicleLocationData(e.getLatitude(), e.getLongitude(), e.getImei(), e.getLocationUpdatedAt())).filter(e -> e.getLatitude() != null && e.getLongitude() != null && e.getTimestamp() != null).stream().toList();

            } else {

                Optional<UserVehicleConnection> b2cConnection = connections.stream().filter(e ->
                        customOrganisationRepository.checkOrganisationType(e.getVehicle().getOwner().getId(), OrganisationType.B2CCUSTOMER.toString())
                ).findAny();

                if (b2cConnection.isPresent()) {
                    vehicleLocationList = Optional.ofNullable(b2cConnection.get().getVehicle()).map(e -> new VehicleLocationData(e.getLatitude(), e.getLongitude(), e.getImei(), e.getLocationUpdatedAt())).filter(e -> e.getLatitude() != null && e.getLongitude() != null && e.getTimestamp() != null).stream().toList();

                }
            }

        } else {
            southBound = iVehicleLocationService.calculateDerivedPosition(userLat, userLong, vehicleVisibilityRange, -180f).getLatitude();
            northBound = iVehicleLocationService.calculateDerivedPosition(userLat, userLong, vehicleVisibilityRange, 0f).getLatitude();
            westBound = iVehicleLocationService.calculateDerivedPosition(userLat, userLong, vehicleVisibilityRange, -90f).getLongitude();
            eastBound = iVehicleLocationService.calculateDerivedPosition(userLat, userLong, vehicleVisibilityRange, 90f).getLongitude();
            List<AvailableVehicleLocationData> availableVehiclesLocation = new ArrayList<>();
            // if user's organisation is Manufacturer, taking locations all vehicles which are associated to that organisation
            if (!customOrganisation.getOrganisationType().equals(OrganisationType.B2CCUSTOMER)) {
                availableVehiclesLocation = vehicleRepository.getLocationDataByLatitudeBetweenAndLongitudeBetweenByOwner(customOrganisation.getId(), southBound, northBound, westBound, eastBound, orgId);
            }
            vehicleLocationList = availableVehiclesLocation.stream().map(e -> new VehicleLocationData(e.getLatitude(), e.getLongitude(), e.getImei(), e.getTimestamp())).toList();
        }

        return getNearByPoiDtoUsingLocationData(vehicleLocationList.stream(), userLat, userLong);
    }

    @Override
    public UserProfileDetailsDto findUserProfileDetails(String userName) throws UserNotFoundException {
        CustomUser customUser = customUserRepository.findByEmailIgnoreCase(userName).orElseThrow(() -> new UserNotFoundException(evMessageBundle.getMessage("USER_NOT_FOUND")));
        UserProfileDetailsDto userProfileDetailsDto = new UserProfileDetailsDto(customUser.getFirstName(), customUser.getLastName(), customUser.getProfilePicUrl(), customUser.getDateOfBirth(), customUser.getGender(), customUser.getEmail(), customUser.getPhoneNumber(), customUser.getShortAddress(), customUser.getLongAddress(), customUser.getBloodGroup(), customUser.getEmergencyNumber(), customUser.getWeightInKg());
        return userProfileDetailsDto;
    }

    @Override
    @Transactional
    public void updateUserProfileDetails(String userName, UserProfile updateUserProfileDetailsBody) throws UserNotFoundException {
        CustomUser customUser = customUserRepository.findByEmailIgnoreCase(userName).orElseThrow(() -> new UserNotFoundException(evMessageBundle.getMessage("USER_NOT_FOUND")));

        customUser.setFirstName(updateUserProfileDetailsBody.getFirstName());
        customUser.setLastName(updateUserProfileDetailsBody.getLastName());
        customUser.setLongAddress(updateUserProfileDetailsBody.getAddress());
        customUser.setGender(updateUserProfileDetailsBody.getSex());
        customUser.setDateOfBirth(updateUserProfileDetailsBody.getDob());
        customUser.setBloodGroup(updateUserProfileDetailsBody.getBloodGroup());
        customUser.setWeightInKg(updateUserProfileDetailsBody.getWeightInKg());
        customUser.setEmergencyNumber(updateUserProfileDetailsBody.getEmergencyNumber());
        customUserRepository.save(customUser);
    }

    @Override
    @Transactional
    public void updateUserStatus(String userName) throws UserNotFoundException {
        CustomUser customUser = customUserRepository.findByEmailIgnoreCase(userName).orElseThrow(() -> new UserNotFoundException(evMessageBundle.getMessage("USER_NOT_FOUND")));
        customUser.setEnabled(false);
        customUserRepository.save(customUser);
    }


    private UserNearByPoiDto getNearByPoiDtoUsingLocationData(Stream<VehicleLocationData> locationDataList, Float userLat, Float userLong) {

        List<UserNearByVehiclesDto> userNearByVehiclesDtoList = locationDataList.map(e -> {

            Vehicle vehicle = vehicleRepository.findByImei(e.getImei().toString()).orElseThrow(() -> new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));
            Float vehicleLatitude = null;
            Float vehicleLongitude = null;
            Double distance = 0d;
            DistanceUnit distanceUnit = DistanceUnit.KM;
            List<ImageDto> imageList;
            if (Optional.ofNullable(e.getLatitude()).isPresent() && Optional.ofNullable(e.getLongitude()).isPresent()) {
                vehicleLatitude = Float.parseFloat(e.getLatitude().toString());
                vehicleLongitude = Float.parseFloat(e.getLongitude().toString());
                distance = iVehicleLocationService.getDistanceFromLocation(vehicleLatitude, vehicleLongitude, userLat, userLong);
            }
            Optional<VehicleRegistrationDetails> vehicleRegistrationDetails = vehicleRegistrationDetailsRepository.findByVehicleId(vehicle.getId());

            imageList = Optional.ofNullable(vehicle.getColorModel()).isPresent() ? getImagesFromVehicleColorImagesMap(vehicle.getVehicleModel().getColorImages(), vehicle.getColorModel().getId()) : new ArrayList<>();
            if (distance < 1) {
                distance = distance * 1000;
                distanceUnit = DistanceUnit.METER;
            }
            UserNearByVehiclesDto userNearByVehiclesDto = null;
            String regNo = null;

            if (vehicleRegistrationDetails.isPresent()) {
                regNo = vehicleRegistrationDetails.get().getRegistrationNumber();
            } else {
                regNo = vehicle.getImei();
            }

            userNearByVehiclesDto = new UserNearByVehiclesDto(vehicle.getImei(), vehicleLatitude, vehicleLongitude, regNo, getSocFromVehicle(vehicle), (int) Math.round(distance), distanceUnit, imageList, e.getTimestamp().toEpochMilli());

            return userNearByVehiclesDto;
        }).filter(n -> n != null).collect(Collectors.toList());
        return new UserNearByPoiDto(userNearByVehiclesDtoList, Collections.emptyList());
    }


    private List<ImageDto> getImagesFromVehicleColorImagesMap(Map<ColorModel, Set<Long>> map, Long vehicleColorModelId) {
        List<ImageDto> imageList = new ArrayList<>();
        for (Map.Entry<ColorModel, Set<Long>> entry : map.entrySet())
            if (entry.getKey().getId() == vehicleColorModelId) {
                Set<Long> values = entry.getValue();
                for (Long e : values) {
                    Optional<Image> image = imageRepository.findById(e);
                    imageList.add(new ImageDto(image.get().getTag(), image.get().getUrl()));
                }
            }
        return imageList;
    }

    private Integer getSocFromVehicle(Vehicle vehicle) {
        Float soc = null;
        Optional<VehicleLatestData> vehicleLatestData = vehicleLatestDataRepository.findByImei(vehicle.getImei());
        soc = vehicleLatestData.isPresent() ? vehicleLatestData.get().getSoc() : null;
        if (Optional.ofNullable(soc).isEmpty()) {
            List<AiVinDto> analogInputDataList = vehicleDataRepository.findFirst100ByTelemetryIdxImeiAndTelemetryIdxTimestampLessThanOrderByTelemetryIdxTimestampDesc(vehicle.getImei(), Instant.now());
            Integer length = 0;
            Float socInFloat = 0.0f;

            Optional<PartModelAttributeDto> aiVinMaxAttributeDto = Optional.empty();
            Optional<PartModelAttributeDto> aiVinMinAttributeDto = Optional.empty();

            aiVinMaxAttributeDto = Optional.ofNullable(vehicleCacheRetrievalService.getAttribute("aiVinMax", PartType.BATTERY.name(), vehicle));
            aiVinMinAttributeDto = Optional.ofNullable(vehicleCacheRetrievalService.getAttribute("aiVinMin", PartType.BATTERY.name(), vehicle));

            for (AiVinDto analogInputData : analogInputDataList) {
                Optional<Float> data = Optional.ofNullable(analogInputData.getAiVoltageInput());
                if (data.isPresent()) {
                    socInFloat = socInFloat + data.get();
                    length++;
                }
            }
            if (length > 0) {
                socInFloat = socInFloat / length;
            }
            soc = getSocFromAvgBatteryVoltage(socInFloat, aiVinMinAttributeDto, aiVinMaxAttributeDto);
        }

        return Optional.ofNullable(soc).isPresent() ? soc.intValue() : null;
    }


    private Float getSocFromAvgBatteryVoltage(Float avgBatteryVolt, Optional<PartModelAttributeDto> aiVinMinAttribute, Optional<PartModelAttributeDto> aiVinMaxAttribute) {

        Float minVolt = 64.0f;
        Float maxVolt = 84.0f;
        if (aiVinMinAttribute.isPresent() && aiVinMinAttribute.get().getType().toLowerCase().equals("float")) {
            minVolt = Float.parseFloat(aiVinMinAttribute.get().getValue());
        }
        if (aiVinMaxAttribute.isPresent() && aiVinMaxAttribute.get().getType().toLowerCase().equals("float")) {
            maxVolt = Float.parseFloat(aiVinMaxAttribute.get().getValue());
        }
        if (avgBatteryVolt > maxVolt) {
            avgBatteryVolt = maxVolt;
        }
        return avgBatteryVolt >= minVolt ? ((avgBatteryVolt - minVolt) * 100) / (maxVolt - minVolt) : 0f;
    }

}
