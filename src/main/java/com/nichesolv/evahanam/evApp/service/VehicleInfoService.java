package com.nichesolv.evahanam.evApp.service;

import com.nichesolv.evahanam.common.repository.ImageRepository;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.evApp.dto.VehicleDetailsDto;
import com.nichesolv.evahanam.evApp.dto.VehicleInfoDto;
import com.nichesolv.evahanam.evApp.dto.VehicleModeInfoDto;
import com.nichesolv.evahanam.evApp.exception.CurrentTimeLimitExceedException;
import com.nichesolv.evahanam.prediction.range.repository.VehicleRangeRepository;
import com.nichesolv.evahanam.telemetryData.dto.AiVinDto;
import com.nichesolv.evahanam.telemetryData.dto.CurrentDriveModeDto;
import com.nichesolv.evahanam.telemetryData.dto.MotorDcCurrentDto;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleBatteryData;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleLocationData;
import com.nichesolv.evahanam.telemetryData.repository.LocationDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.MotorDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.TelemetryBatteryRepository;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.vehicle.dto.ImageDto;
import com.nichesolv.evahanam.vehicle.dto.PartModelAttributeProjection;
import com.nichesolv.evahanam.vehicle.dto.VehiclePartAttributeDetailsDto;
import com.nichesolv.evahanam.vehicle.exception.VehicleException;
import com.nichesolv.evahanam.vehicle.exception.VehicleNotFoundException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleLatestData;
import com.nichesolv.evahanam.vehicle.jpa.VehicleRegistrationDetails;
import com.nichesolv.evahanam.vehicle.repository.VehicleLatestDataRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRegistrationDetailsRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import com.nichesolv.evahanam.vehicleModel.dto.DataTypeLongProjection;
import com.nichesolv.evahanam.vehicleModel.enums.DistanceUnit;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.enums.TemperatureUnit;
import com.nichesolv.evahanam.vehicleModel.exception.PartModelException;
import com.nichesolv.evahanam.vehicleModel.jpa.*;
import com.nichesolv.evahanam.vehicleModel.repository.DriveModeMaxRangeRepository;
import com.nichesolv.evahanam.vehicleModel.repository.PartModelRepository;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


@Slf4j
@Service
public class VehicleInfoService implements IVehicleInfoService {

    @Autowired
    MotorDataRepository motorDataRepository;

    @Autowired
    TelemetryBatteryRepository batteryStatusRepo;

    @Autowired
    VehicleRepository vehicleRepository;


    @Autowired
    VehicleDataRepository vehicleDataRepository;



    @Autowired
    VehicleRegistrationDetailsRepository vehicleRegistrationDetailsRepository;
    @Autowired
    ImageRepository imageRepository;

    @Autowired
    LocationDataRepository locationDataRepository;


    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    IVehicleService vehicleService;

    @Autowired
    DriveModeMaxRangeRepository driveModeMaxRangeRepository;

    @Autowired
    PartModelRepository partModelRepository;

    @Autowired
    VehicleLatestDataRepository vehicleLatestDataRepository;

    @Autowired
    VehicleRangeRepository vehicleRangeRepository;


    @Override
    @Transactional
    public VehicleInfoDto findVehicleInfo(String identifier, Integer timeLimitInSecondsForCurrent) throws VehicleException, VehicleNotFoundException {
        Float soc = null;
        DriveMode driveMode = null;
        Float avgBatteryVolt = null;
        boolean batteryConnected = true;
        boolean batteryCharging = false;
        String aiVinMinAttribute = null;
        String aiVinMaxAttribute = null;
        Boolean diMotion = false;
        Optional<PartModelAttributeProjection> aiVinMaxAttributeProjection = Optional.empty();
        Optional<PartModelAttributeProjection> aiVinMinAttributeProjection = Optional.empty();
        AtomicReference<String> themeHexColor = new AtomicReference<>();
        AtomicReference<String> themeColorType = new AtomicReference<>();

        Instant end = Instant.now();
        Optional<Integer> timeLimitInSecondsForCurrentOptional = Optional.ofNullable(timeLimitInSecondsForCurrent);

        if (timeLimitInSecondsForCurrentOptional.isPresent() && (timeLimitInSecondsForCurrent > 86400 || timeLimitInSecondsForCurrent < 0)) {
            throw new CurrentTimeLimitExceedException(evMessageBundle.getMessage("CURRENT_TIME_LIMIT_EXCEED"));
        }

        Instant start = end.minusSeconds(timeLimitInSecondsForCurrentOptional.orElseGet(() -> 30));

        Vehicle vehicle = vehicleService.getVehicleByAnyId(identifier);
        String imei = vehicle.getImei();
        List<MotorDcCurrentDto> motorDcCurrents = motorDataRepository.getDcMotorCurrentByImeiBetweenTimestamps(imei, start, end);
        List<AiVinDto> analogInputData = vehicleDataRepository.findFirst100ByTelemetryIdxImeiAndTelemetryIdxTimestampLessThanOrderByTelemetryIdxTimestampDesc(imei, Instant.now());

        Optional<VehicleLatestData> vehicleLatestData = vehicleLatestDataRepository.findByImei(imei);
        soc = vehicleLatestData.isPresent() ? vehicleLatestData.get().getSoc() : null;
        Optional<VehicleRegistrationDetails> vehicleRegistrationDetails = vehicleRegistrationDetailsRepository.findByVehicleId(vehicle.getId());
        Optional<VehicleLocationData> vehicleLocationData = locationDataRepository.findTop1SpeedByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenOrderByTelemetryIdxTimestampDesc(imei, end.minusSeconds(30), end);

        VehiclePartAttributeDetailsDto vehicleBatteryAttributes = vehicleService.getPartAttributesByImei(vehicle.getImei(), PartType.BATTERY, null);

        Boolean diMotionOptional = vehicleDataRepository.getLatestDiMotionData(vehicle.getImei(), Instant.now().minusSeconds(120), Instant.now());


        diMotion = Optional.ofNullable(diMotionOptional).isPresent() ? diMotionOptional : false;

        aiVinMaxAttribute = vehicleBatteryAttributes.getAttributes().get("aiVinMax");
        aiVinMinAttribute = vehicleBatteryAttributes.getAttributes().get("aiVinMin");


        driveMode = getDrivingModeFromMode(imei);
        avgBatteryVolt = getAverageOfVoltageInput(analogInputData);
        Long aiVinLastEpochTime = getLastAiVinTimestamp(analogInputData);

        boolean isItCalSoc = !Optional.ofNullable(soc).isPresent();
        soc = Optional.ofNullable(soc).isPresent() ? soc : getSocFromAvgBatteryVoltage(avgBatteryVolt, aiVinMinAttribute, aiVinMaxAttribute);

        batteryConnected = getBatteryConnectedStatus(avgBatteryVolt, aiVinMinAttribute);

        List<ImageDto> images =
                vehicle.getVehicleModel().getColorImages().entrySet().stream().filter(v -> v.getKey().getId() == vehicle.getColorModel().getId()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)).values().stream().flatMap(Set::stream).map(value -> imageRepository.findById(value).get()).map(value -> new ImageDto(value.getTag(), value.getUrl())).collect(Collectors.toList());

        Set<PartModelAttribute> partModelAttributes = vehicle.getColorModel().getPartModelAttributes();
        partModelAttributes.stream().forEach(e -> {
            if (e.getName().equals("themeColorHexCode")) {
                themeHexColor.set(e.getValue());
            } else if (e.getName().equals("colorType")) {
                themeColorType.set(e.getValue());
            }
        });
        VehicleInfoDto vehicleInfoDto = new VehicleInfoDto();

        vehicleInfoDto.setColor(themeHexColor.get());
        vehicleInfoDto.setColorType(themeColorType.get());
        vehicleInfoDto.setImages(images);
        vehicleInfoDto.setModelNo(vehicle.getVehicleModel().getModelNo());
        vehicleInfoDto.setRegNo(vehicleRegistrationDetails.isPresent() ? vehicleRegistrationDetails.get().getRegistrationNumber() : imei);
        vehicleInfoDto.setCurrent(getAvgCurrent(motorDcCurrents));
        vehicleInfoDto.setMotorDcCurrents(motorDcCurrents.stream().map((e) -> {
            return Optional.ofNullable(e).isPresent() ? e.getMotorDcCurrent() : null;
        }).collect(Collectors.toList()));

        if (vehicleLocationData.isPresent() && Optional.ofNullable(vehicleLocationData.get().getSpeed()).isPresent()) {
            vehicleInfoDto.setSpeed(vehicleLocationData.get().getSpeed());
        } else {
            vehicleInfoDto.setSpeed(0.0f);
        }


        List<VehicleModeInfoDto> vehicleModeInfoDtoList = getModesOfVehicle(vehicle, end);

        vehicleInfoDto.setCharge(soc.intValue());
        vehicleInfoDto.setCalculatedSoc(isItCalSoc);
        vehicleInfoDto.setBatteryCharging(batteryCharging); //not coming
        vehicleInfoDto.setBatteryConnected(batteryConnected);
        vehicleInfoDto.setRemainingTimeForCharge(soc == null ? null : getRemainingTimeForCharge(soc.intValue()));
        vehicleInfoDto.setCurrentDriveMode(driveMode);
        vehicleInfoDto.setTotalDistance(Math.round(Optional.ofNullable(vehicle.getTotalDistanceTraveled()).orElse(0f)));
        vehicleInfoDto.setTemperatureUnit(TemperatureUnit.C);
        vehicleInfoDto.setBatteryVoltage(Math.round(avgBatteryVolt));


        vehicleInfoDto.setVehicleModeInfoList(vehicleModeInfoDtoList);
        vehicleInfoDto.setAiVinRecordedTime(aiVinLastEpochTime);
        vehicleInfoDto.setBatteryName(vehicleBatteryAttributes.getModelName());
        vehicleInfoDto.setDiMotion(diMotion);

        return vehicleInfoDto;
    }

    @Override
    @Transactional
    public VehicleDetailsDto findVehicleDetails(String identifier) {
        VehicleDetailsDto vehicleDetails = new VehicleDetailsDto();
        Map<String, Float> driveModesRange = new HashMap<>();

        Vehicle vehicle = vehicleService.getVehicleByAnyId(identifier);
        Optional<VehicleRegistrationDetails> vehicleRegistrationDetails = vehicleRegistrationDetailsRepository.findByVehicleId(vehicle.getId());
        Set<PartModelAttribute> colorAttributes = vehicle.getColorModel().getPartModelAttributes();

        Optional<PartModelAttribute> colorHexCodeAttribute = colorAttributes.stream().filter(e -> e.getName().equals("colorHexCode")).findFirst();
        List<ImageDto> images =
                vehicle.getVehicleModel().getColorImages().entrySet().stream().filter(v -> v.getKey().getId() == vehicle.getColorModel().getId()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)).values().stream().flatMap(Set::stream).map(value -> imageRepository.findById(value).get()).map(value -> new ImageDto(value.getTag(), value.getUrl())).collect(Collectors.toList());
        VehiclePartAttributeDetailsDto vehicleWheelAttributes = vehicleService.getPartAttributesByImei(vehicle.getImei(), PartType.REAR_TYRE, null);
        DataTypeLongProjection batteryIdProjection = vehicleRepository.getPartModelIdByVehicleIdAndPartType(vehicle.getId(), PartType.BATTERY.toString()).orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("PART_MODEL_ID_NOT_FOUND", PartType.BATTERY)));
        PartModel batteryModel = partModelRepository.findById(batteryIdProjection.getId()).orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("PART_MODEL_ID_NOT_FOUND", PartType.BATTERY.toString())));
        Optional<PartModelAttribute> fullCapacityAttribute = batteryModel.getPartModelAttributes().stream().filter(e -> e.getName().equals("fullCapacity")).findFirst();
        List<DriveModeMaxRange> driveModesMaxRange = driveModeMaxRangeRepository.findByVehicleModelAndBatteryModel(vehicle.getVehicleModel(), batteryModel);
        driveModesMaxRange.stream().forEach((e) -> {
            driveModesRange.put(e.getDriveMode().getName(), e.getMaxRange());
        });

        vehicleDetails.setRegNo(vehicleRegistrationDetails.isPresent() ? vehicleRegistrationDetails.get().getRegistrationNumber() : null);
        vehicleDetails.setModelName(vehicle.getVehicleModel().getName());
        vehicleDetails.setVehicleManufacturerName(vehicle.getManufacturer().getOrganisationProfile().getName());
        vehicleDetails.setColorName(vehicle.getColorModel().getName());
        vehicleDetails.setVehicleColorHexCode(colorHexCodeAttribute.isPresent() ? colorHexCodeAttribute.get().getValue() : null);
        vehicleDetails.setImages(images);
        vehicleDetails.setOdometer(vehicle.getTotalDistanceTraveled());
        vehicleDetails.setBatteryName(batteryModel.getName());
        vehicleDetails.setBatteryManufacturerName(batteryModel.getManufacturer().getOrganisationProfile().getName());
        vehicleDetails.setFullCapacity(fullCapacityAttribute.isPresent() ? fullCapacityAttribute.get().getValue() : null);
        vehicleDetails.setRearTyreDiameter(vehicleWheelAttributes.getAttributes().get("rearTyreDiameter"));
        vehicleDetails.setFrontTyreDiameter(vehicleWheelAttributes.getAttributes().get("frontTyreDiameter"));
        vehicleDetails.setTyreManufacturerName(vehicleWheelAttributes.getManufacturerName());
        vehicleDetails.setNetWeight(vehicle.getVehicleModel().getNetWeight());
        vehicleDetails.setDriveModesRange(driveModesRange);

        return vehicleDetails;
    }

    private String getColorHexCodeFromColorModel(ColorModel colorModel) {

        Set<PartModelAttribute> partModelAttributes = colorModel.getPartModelAttributes();

        return partModelAttributes.stream().filter(e -> e.getName().equals("colorHexCode")).findFirst().map(e -> e.getValue()).orElse(null);
    }

    Float getAvgCurrent(List<MotorDcCurrentDto> motorDcCurrents) {
        Float current = null;
        int length = motorDcCurrents.size();
        if (length > 0) {
            current = 0f;
            for (MotorDcCurrentDto e : motorDcCurrents) {
                current = e.getMotorDcCurrent() + current;
            }
            current = current / length;
        }
        return current;
    }

    private Integer getSocFromAvgBatteryVoltage(Float avgBatteryVolt, String aiVinMinAttribute, String aiVinMaxAttribute) {
        Float minVolt = 64.0f;
        Float maxVolt = 84.0f;
        if (Optional.ofNullable(aiVinMaxAttribute).isPresent() && Optional.ofNullable(aiVinMinAttribute).isPresent()) {
            minVolt = Float.parseFloat(aiVinMinAttribute);
            maxVolt = Float.parseFloat(aiVinMaxAttribute);
        }

        if (avgBatteryVolt > maxVolt) {
            avgBatteryVolt = maxVolt;
        }
        return avgBatteryVolt >= minVolt ? Math.round(((avgBatteryVolt - minVolt) * 100) / (maxVolt - minVolt)) : 0;
    }

    private boolean getBatteryConnectedStatus(Float avgBatteryVolt, String aiVinMinAttribute) {
        Float minVolt = 64.0f;
        if (Optional.ofNullable(aiVinMinAttribute).isPresent()) {
            minVolt = Float.parseFloat(aiVinMinAttribute);
        }
        return avgBatteryVolt >= minVolt;
    }

    private Integer getAvgTemperature(String imei) {
        Optional<VehicleBatteryData> batteryStatus = batteryStatusRepo.findFirstByTelemetryIdxImeiOrderByTelemetryIdxTimestampDesc(imei);
        Integer temp = null;
        if (batteryStatus.isPresent()) {
            Float tempMinFloat = batteryStatus.get().getTemperatureMin();
            Float tempMaxFloat = batteryStatus.get().getTemperatureMax();
            if (tempMinFloat != null && tempMaxFloat != null) {
                temp = Math.round((tempMaxFloat + tempMinFloat) / 200);
            }
            temp = temp - 273;
        }
        return temp;
    }

    private DriveMode getDrivingModeFromMode(String imei) {
        Optional<CurrentDriveModeDto> driveMode = motorDataRepository.findFirstByMotorIdxImeiAndMotorDrivingModeIsNotNullOrderByMotorIdxTimestampDesc(imei);

        return driveMode.isPresent() ? driveMode.get().getMotorDrivingMode() : DriveMode.ECO;
    }

    private List<VehicleModeInfoDto> getModesOfVehicle(Vehicle vehicleErpInfo, Instant now) {

        Set<DriveModeSpeed> driveModes = vehicleErpInfo.getVehicleModel().getDriveModes();
        List<VehicleModeInfoDto> vehicleModeInfoDtoList = new ArrayList<>();
        Map<Long, Double> corrections = vehicleRangeRepository.findDriveModeWithCorrection(vehicleErpInfo.getImei(), now.minusSeconds(120), now)
                .stream()
                .collect(Collectors.toMap(
                        result -> ((Number) result[0]).longValue(),  // drive_mode
                        result -> ((Number) result[1]).doubleValue() // range_correction
                ));
        driveModes.forEach((e) -> {
            DataTypeLongProjection batteryIdProjection = vehicleRepository.getPartModelIdByVehicleIdAndPartType(vehicleErpInfo.getId(), PartType.BATTERY.toString()).orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("PART_MODEL_ID_NOT_FOUND", PartType.BATTERY)));
            PartModel batteryModel = partModelRepository.findById(batteryIdProjection.getId()).orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("PART_MODEL_ID_NOT_FOUND", PartType.BATTERY.toString())));
            Optional<DriveModeMaxRange> driveModeMaxRangeOptional = driveModeMaxRangeRepository.findByVehicleModelAndDriveModeAndBatteryModel(vehicleErpInfo.getVehicleModel(), e.getDriveMode(), batteryModel);
            Double correction = corrections.get(e.getDriveMode().getId());
            vehicleModeInfoDtoList.add(new VehicleModeInfoDto(e.getDriveMode() != null ? e.getDriveMode().getName() : null,
                    e.getHexColor(), getRangeAsPerCharge(e.getDriveMode() != null ? e.getDriveMode().getName() : null), DistanceUnit.KM, (driveModeMaxRangeOptional.isPresent() ? driveModeMaxRangeOptional.get().getMaxRange() : null), correction));
        });
        return vehicleModeInfoDtoList;
    }

    private Integer getRangeAsPerCharge(String mode) {
        Integer range = null;
        if (mode != null) {
            if (mode.equals(DriveMode.ECO.name())) {
                range = (int) Math.round(Math.random() * 100);
            } else if (mode.equals(DriveMode.CITY.name())) {
                range = (int) Math.round(Math.random() * 70);
            } else {
                range = (int) Math.round(Math.random() * 50);
            }
        }
        return range;
    }

    private int getRemainingTimeForCharge(int currentChargePercentage) {
        int remainingCharge = 100 - currentChargePercentage;
        int timePerChargeInMin = 3;

        return timePerChargeInMin * remainingCharge;
    }

    private Float getAverageOfVoltageInput(List<AiVinDto> analogInputDataList) {
        Integer length = 0;
        Float socInFloat = 0.0f;
        for (AiVinDto analogInputData : analogInputDataList) {
            Optional<Float> data = Optional.ofNullable(analogInputData.getAiVoltageInput());
            if (data.isPresent()) {
                socInFloat = socInFloat + data.get();
                length++;
            }
        }
        return socInFloat / length;
    }

    Long getLastAiVinTimestamp(List<AiVinDto> analogInputDataList) {
        Long lastEpochTime = null;
        boolean flag = false;
        for (AiVinDto analogInputData : analogInputDataList) {
            if (Optional.ofNullable(analogInputData.getAiVoltageInput()).isPresent() && flag == false) {
                Optional<Instant> data = Optional.ofNullable(analogInputData.getTelemetryIdx().getTimestamp());
                if (data.isPresent()) {
                    flag = true;
                    lastEpochTime = data.get().toEpochMilli();
                }
            }
        }
        return lastEpochTime;
    }
}