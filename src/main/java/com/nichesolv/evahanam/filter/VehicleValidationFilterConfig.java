package com.nichesolv.evahanam.filter;

import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.evahanam.util.VehicleIdentifierUtil;
import com.nichesolv.evahanam.vehicle.filter.VehicleIdTypeValidationFilter;
import com.nichesolv.evahanam.vehicle.filter.VehicleOwnershipValidationFilter;
import com.nichesolv.evahanam.vehicle.filter.VehicleValidationFilter;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class VehicleValidationFilterConfig {

    @Autowired
    IVehicleService vehicleService;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;

    @Autowired
    VehicleIdentifierUtil vehicleIdentifierUtil;

    static final String[] vehicleValidationUrls = {
            "/part-models/*", "/part-models",
            "/vehicles/*", "/vehicles",
            "/vehicle-tests/*", "/vehicle-tests",
            "/vehicle-alerts/*", "/vehicle-alerts",
            "/parts/*", "/parts",
            "/trips/*", "/trips",
            "/telemetry-data", "/telemetry-data/*",
            "/org-vehicles/*", "/org-vehicles",
            "/users/*",
            "/vehicle-test-rides/*","/vehicle-test-rides","/user-vehicles/*","/vehicle-status","/vehicle-status/*","/emergency"};
    @Bean
    public FilterRegistrationBean<VehicleValidationFilter> vehicleValidationFilterFilterRegistrationBean() {
        FilterRegistrationBean<VehicleValidationFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new VehicleValidationFilter(vehicleService, evMessageBundle, vehicleIdentifierUtil));
        registrationBean.addUrlPatterns(vehicleValidationUrls);
        registrationBean.setOrder(1);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<VehicleIdTypeValidationFilter> vehicleIdTypeValidationFilterFilterRegistrationBean() {
        FilterRegistrationBean<VehicleIdTypeValidationFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new VehicleIdTypeValidationFilter(evMessageBundle));
        registrationBean.addUrlPatterns("/vehicles/ids");


        registrationBean.setOrder(3);
        return registrationBean;
    }

    static final String[] vehicleOwnerShipCheckUrls = {
            "/vehicles/*", "/vehicles",
            "/vehicle-tests/*", "/vehicle-tests",
            "/vehicle-alerts/*", "/vehicle-alerts",
            "/parts/*", "/parts",
            "/trips/*", "/trips",
            "/telemetry-data", "/telemetry-data/*",
            "/org-vehicles/*", "/org-vehicles",
            "/users/*",
            "/vehicle-test-rides/*","/vehicle-test-rides","/vehicle-status","/vehicle-status/*","/emergency"};
    @Bean
    public FilterRegistrationBean<VehicleOwnershipValidationFilter> vehicleOwnershipValidationFilterFilterRegistrationBean() {
        FilterRegistrationBean<VehicleOwnershipValidationFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new VehicleOwnershipValidationFilter(httpRequestOriginUtil, vehicleIdentifierUtil));
        registrationBean.addUrlPatterns(vehicleOwnerShipCheckUrls);
        registrationBean.setOrder(2);
        return registrationBean;
    }
}


