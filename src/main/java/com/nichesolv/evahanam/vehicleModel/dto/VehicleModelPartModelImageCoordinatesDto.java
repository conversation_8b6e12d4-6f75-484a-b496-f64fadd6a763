package com.nichesolv.evahanam.vehicleModel.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VehicleModelPartModelImageCoordinatesDto {
    @NotNull(message = "vehicleModelPartModel cannot be null")
    VehicleModelPartModelDto vehicleModelPartModel;

    @NotNull(message = "imageCoordinates cannot be null")
    Map<Long, String> imageCoordinates;
}
