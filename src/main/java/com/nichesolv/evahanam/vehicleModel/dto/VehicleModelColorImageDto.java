package com.nichesolv.evahanam.vehicleModel.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VehicleModelColorImageDto {
    @NotNull(message = "vehicleModelPartModel cannot be null")
    VehicleModelPartModelDto vehicleModelPartModel;

    @NotNull(message = "imageIds cannot be null")
    Set<Long> imageIds;
}
