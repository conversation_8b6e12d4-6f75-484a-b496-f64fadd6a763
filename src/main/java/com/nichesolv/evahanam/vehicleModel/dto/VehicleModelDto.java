package com.nichesolv.evahanam.vehicleModel.dto;

import java.util.HashSet;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nichesolv.evahanam.vehicle.dto.ImageDto;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VehicleModelDto {

  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  Long id;

  @NotBlank(message = "{_VALIDATION}")
  String name;

  @NotBlank(message = "{VEHICLE_MODEL_NO_VALIDATION}")
  String modelNo;

  Set<PartModelDto> partModels = new HashSet<>();

  String weightUom;
  Float grossWeight;
  Float netWeight;
  Set<ImageDto> images = new HashSet<>();

  @NotBlank(message = "{VEHICLE_MFR_VALIDATION}")
  String manufacturerName;
  String description;

  Set<DriveModeSpeedDto> driveModeSpeed;

  public VehicleModelDto(Long id, String name, String modelNo, Set<PartModelDto> partModels, String weightUom, Float grossWeight, Float netWeight, Set<ImageDto> images, String manufacturerName, String description) {
    this.id = id;
    this.name = name;
    this.modelNo = modelNo;
    this.partModels = partModels;
    this.weightUom = weightUom;
    this.grossWeight = grossWeight;
    this.netWeight = netWeight;
    this.images = images;
    this.manufacturerName = manufacturerName;
    this.description = description;
  }
}
