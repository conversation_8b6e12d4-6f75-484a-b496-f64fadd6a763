package com.nichesolv.evahanam.vehicleModel.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Optional;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MotorModelDto{
    Long id;
    @NotBlank(message = "Part type cannot be empty") String partType;
    @NotBlank(message = "Part name cannot be empty")  String name;
    String description;
    String manufacturerName;
    Optional<Long> parentPartModelId = Optional.empty();
    Map<String, Object> attributes;
}
