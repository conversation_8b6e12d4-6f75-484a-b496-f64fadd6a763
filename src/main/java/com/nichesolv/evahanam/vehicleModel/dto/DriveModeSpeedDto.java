package com.nichesolv.evahanam.vehicleModel.dto;

import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class DriveModeSpeedDto {
    String mode;
    Double min;
    Double max;
    String unit;
    Double range;

    public DriveModeSpeedDto(String mode, Double min, Double max, String unit) {
        this.mode = mode;
        this.min = min;
        this.max = max;
        this.unit = unit;
    }
}
