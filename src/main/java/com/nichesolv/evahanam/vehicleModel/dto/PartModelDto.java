package com.nichesolv.evahanam.vehicleModel.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;
import java.util.Map;
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PartModelDto {
    long partModelId;
    @NotBlank(message = "{PART_TYPE_VALIDATION}")
    String partType;

    @NotBlank(message = "{PART_NAME_VALIDATION}")
    String name;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    String description;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    Long parentPartModelId;

    @NotBlank(message = "{PART_MFR_VALIDATION}")
    String manufacturerName;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    List<Map<String, String>> attributes;

        public PartModelDto(String partType, String name, String description,
                        Long parentPartModelId, String manufacturerName,
                        List<Map<String, String>> attributes) {
        this.partType = partType;
        this.name = name;
        this.description = description;
        this.parentPartModelId = parentPartModelId;
        this.manufacturerName = manufacturerName;
        this.attributes = attributes;
    }
    @JsonIgnore
    public void setPartId(long partModelId) {
        this.partModelId = partModelId;
    }
}
