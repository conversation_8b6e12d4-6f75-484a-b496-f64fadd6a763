package com.nichesolv.evahanam.vehicleModel.repository;

import com.nichesolv.evahanam.vehicleModel.jpa.DriveMode;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Set;

@Repository
public interface DriveModeRepository extends JpaRepository<DriveMode,Long> {
    DriveMode findByNameAndOrganisation(String driveMode, CustomOrganisation organisation);

    Set<DriveMode> findByOrganisation(CustomOrganisation organisation);
}
