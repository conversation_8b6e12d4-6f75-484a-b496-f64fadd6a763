package com.nichesolv.evahanam.vehicleModel.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.vehicleModel.dto.*;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.service.ModelService;
import com.nichesolv.nds.dto.organisation.response.OrganisationDto;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/part-models")
@SecurityRequirement(name = "Bearer Authentication")
public class PartModelController {

    @Autowired
    ModelService modelService;

    @PostMapping
    public void savePartModel(@Validated @RequestBody PartModelDto partModelDto) {
        modelService.convertAndSavePart(partModelDto);
    }

    @ReadOnly
    @GetMapping("/attributes")
    public List<PartModelAttributeDto> getPartModelAttributes(Long partModelId) throws Throwable {
        return modelService.findPartModelAttributes(partModelId);
    }

    @PostMapping("/attributes")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public void addPartModelAttributes(@RequestBody AddPartModelAttributesDto addPartModelAttributesDto) throws Throwable {
        modelService.addPartModelAttributes(addPartModelAttributesDto);
    }

    @GetMapping
    @ReadOnly
    public List<PartModelDto> getPartModels(
            @RequestParam Optional<Boolean> isMfr,
            @RequestParam Optional<PartType> partType,
            Pageable pageable
    ) {
        return modelService.findAll(isMfr, partType, pageable);
    }
    @GetMapping("/{id}")
    @ReadOnly
    public PartModelDto getPartModelById(@PathVariable("id") Long partModelId) {
        return modelService.findById(partModelId);
    }
    @ReadOnly
    @GetMapping("/{id}/manufacturer")
    public OrganisationDto getPartManufacturer(@PathVariable("id") Long partModelId) {
        return modelService.findPartManufacturer(partModelId);
    }

    @PostMapping("/motor")
    public void saveMotorModel(@Validated @RequestBody MotorModelDto motorModelDto) {
        modelService.convertAndSaveMotor(motorModelDto);
    }

    @ReadOnly
    @GetMapping("/motor")
    public List<MotorModelDto> getMotorModels(
            @PageableDefault(size = 10, page = 0, direction = Sort.Direction.ASC) Pageable pageable) {
        return modelService.findAllMotor(pageable);
    }

    @PostMapping("/color")
    public void saveColor(@Validated @RequestBody ColorModelDto colorModelDto) {
        modelService.covertAndSaveColor(colorModelDto);
    }


}
