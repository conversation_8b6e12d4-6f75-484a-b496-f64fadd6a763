package com.nichesolv.evahanam.vehicleModel.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.evahanam.vehicleModel.dto.*;
import com.nichesolv.evahanam.vehicleModel.service.ModelService;

import java.util.List;
import java.util.Optional;

import com.nichesolv.nds.model.organisation.CustomOrganisation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/vehicle-models")
@SecurityRequirement(name = "Bearer Authentication")
public class VehicleModelController {

    @Autowired
    ModelService modelService;

    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;


    @ReadOnly
    @GetMapping(value = {"/manufacturer"})
    public ResponseEntity<?> getAllVehicleModels(@RequestParam Optional<Long> orgId, HttpServletRequest request){
        CustomOrganisation organisation = httpRequestOriginUtil.getUserOrganisation(request);
        try {
            List<com.nichesolv.evahanam.vehicle.dto.VehicleModelDto> vehicleModelDtoList= modelService.getAllVehicleModel(organisation, orgId);
            return ResponseEntity.ok().body(vehicleModelDtoList);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new GenericMessage<>(e.getMessage()));
        }
    }
    /***
     * Add PartModels to VehicleModel
     * @param addPartModelRequestDto
     * @throws Throwable
     */
    @PostMapping("/parts")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public void addPartModelToVehicleModel(@RequestBody VehicleModelPartModelDto addPartModelRequestDto) throws Throwable {
        modelService.addPartModelToVehicleModel(addPartModelRequestDto);
    }

    /***
     * Save vehicle Model
     * @param vehicleModelDto
     */
    @PostMapping
    public void saveVehicleModel(@Validated @RequestBody VehicleModelDto vehicleModelDto) {
        modelService.convertAndSaveVehicleModel(vehicleModelDto);
    }

    /***
     * Get VehicleModel by ID, if empty return a paginated list of all VehicleModels
     * @param id
     * @param pageable
     * @return
     */
    @ReadOnly
    @GetMapping
    public List<VehicleModelDto> getVehicleModels(
            @RequestParam(value = "id", required = false) Optional<Long> id,
            @PageableDefault(size = 10, page = 0, direction = Sort.Direction.ASC) Pageable pageable) {

        return modelService.find(id, pageable);
    }


    @PutMapping("/drive-modes/ranges")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public void saveDriveModeRanges(@Validated @RequestBody DriveModelMaxRangeDto driveModelMaxRangeDto){

        modelService.saveAndUpdateDriveModeMaxRange(driveModelMaxRangeDto);
    }


    @PostMapping("/colors/images")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public void addColorImages(@Validated @RequestBody VehicleModelColorImageDto vehicleModelColorImageDto) throws Throwable {
        modelService.addColorImages(vehicleModelColorImageDto);
    }

    @PostMapping("/colors/images/coordinates")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public void addPartModelImageCoordinates(@Validated @RequestBody VehicleModelPartModelImageCoordinatesDto vehicleModelPartModelImageCoordinatesDto) throws Throwable {
        modelService.addPartModelImageCoordinates(vehicleModelPartModelImageCoordinatesDto);
    }

}
