package com.nichesolv.evahanam.vehicleModel.jpa;

import jakarta.persistence.*;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Entity
@Data
@NoArgsConstructor
@Table(name = "vehicle_model", uniqueConstraints = {
        @UniqueConstraint(columnNames = "modelNo")
})
public class VehicleModel extends  PartModel{

  @NonNull
  @Column(nullable = false, unique = true)
  String modelNo;

  /**
   * vehicle model comprise of various part models
   */
  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "vehicle_model_parts", joinColumns = {
      @JoinColumn(name = "model_id", nullable = false , foreignKey = @ForeignKey(name = "fk_model_id"))
  }, inverseJoinColumns = {
      @JoinColumn(name = "part_model_id", nullable = false , foreignKey = @ForeignKey(name = "fk_part_model_id"))
  })
  Set<PartModel> partModels = new HashSet<>();

  /**
   * Unit of Measurement for weight
   */
  @NonNull
  String weightUom;
  @NonNull
  Float grossWeight;
  @NonNull
  Float netWeight;

  @ElementCollection(fetch = FetchType.LAZY)
  @CollectionTable(name = "vehicle_color_images", joinColumns = @JoinColumn(name = "vehicle_model_id"),foreignKey = @ForeignKey(name = "fk_vehicle_model_id") )
  @MapKeyJoinColumn(name = "color_model_id",foreignKey = @ForeignKey(name = "fk_color_model_id"))
  @Column(name = "image_id")
  Map<ColorModel, Set<Long>> colorImages;

  @ManyToMany(fetch = FetchType.EAGER)
  @JoinTable(name = "vehicle_model_drive_mode", joinColumns = {
      @JoinColumn(name = "model_id", nullable = false,foreignKey = @ForeignKey(name = "fk_model_id"))

  }, inverseJoinColumns = {
      @JoinColumn(name = "mode_id", nullable = false,foreignKey = @ForeignKey(name = "fk_mode_id"))
  })
  Set<DriveModeSpeed> driveModes = new HashSet<>();

}
