package com.nichesolv.evahanam.vehicleModel.jpa;

import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.PrimaryKeyJoinColumn;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.Set;

@Entity
@NoArgsConstructor
@PrimaryKeyJoinColumn(foreignKey = @ForeignKey(name = "fk_part_model_id"))
public class RearTyreModel extends PartModel {
    public RearTyreModel(Long id, @NonNull String name, @NonNull PartType partType, String description, PartModel parentPartModelId, Organisation manufacturer, Set<PartModelAttribute> partModelAttributes) {
        super(id, name, partType, description, parentPartModelId, manufacturer, partModelAttributes);
    }

    public static RearTyreModel convertFromPartModel(PartModel partModel) {
        return new RearTyreModel(partModel.id, partModel.name, partModel.partType, partModel.description, partModel.parentPartModelId, partModel.manufacturer, partModel.partModelAttributes);
    }
}
