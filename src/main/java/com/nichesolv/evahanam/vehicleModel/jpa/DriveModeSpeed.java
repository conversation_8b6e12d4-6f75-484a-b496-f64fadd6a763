package com.nichesolv.evahanam.vehicleModel.jpa;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;

@Getter
@Setter
@Entity
@NoArgsConstructor
public class DriveModeSpeed {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "drive_mode_speed_seq")
    @SequenceGenerator(name = "drive_mode_speed_seq", sequenceName = "drive_mode_speed_seq", allocationSize = 1)
    Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(foreignKey = @ForeignKey(name = "fk_drive_mode_id"))
    DriveMode driveMode;

    Double min;
    Double max;
    String unit;
    Double range;
    String hexColor;

    public DriveModeSpeed(DriveMode driveMode, Double min, Double max, String unit, Double range) {
        this.driveMode = driveMode;
        this.min = min;
        this.unit = unit;
        this.max = max;
        this.range = range;
    }
}
