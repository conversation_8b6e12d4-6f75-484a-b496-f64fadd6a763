package com.nichesolv.evahanam.vehicleModel.service;

import com.nichesolv.evahanam.vehicle.dto.PartModelAttributeProjection;
import com.nichesolv.evahanam.vehicleModel.dto.*;

import java.util.List;
import java.util.Optional;

import com.nichesolv.evahanam.vehicleModel.jpa.PartModel;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.nds.dto.organisation.response.OrganisationDto;
import org.springframework.data.domain.Pageable;

public interface IModelService {

  PartModel convertAndSavePart(PartModelDto partModelDto);

  void addPartModelToVehicleModel(VehicleModelPartModelDto vehicleModelPartModelDto) throws Throwable;

  void convertAndSaveMotor(MotorModelDto partModelDto);

  List<PartModelDto> findAll(Pageable pageable);

  OrganisationDto findPartManufacturer(Long partModelId);

  List<MotorModelDto> findAllMotor(Pageable pageable);

  VehicleModel convertAndSaveVehicleModel(VehicleModelDto vehicleModelDto);

  List<VehicleModelDto> find(Optional<Long> id, Pageable pageable);

  void covertAndSaveColor(ColorModelDto colorModelDto);

  void addPartModelAttributes(AddPartModelAttributesDto addPartModelAttributesDto) throws Throwable;

  List<PartModelAttributeProjection> findPartModelAttributes(Long partModelId) throws Throwable;
}
