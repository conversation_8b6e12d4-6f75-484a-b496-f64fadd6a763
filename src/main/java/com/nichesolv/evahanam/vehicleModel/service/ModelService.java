package com.nichesolv.evahanam.vehicleModel.service;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleModel.dto.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.jpa.PartModel;
import com.nichesolv.evahanam.vehicleModel.jpa.PartModelAttribute;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.nds.dto.organisation.response.OrganisationDto;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import org.springframework.data.domain.Pageable;

public interface ModelService {

  List<com.nichesolv.evahanam.vehicle.dto.VehicleModelDto> getAllVehicleModel(Organisation organisation, Optional<Long> orgId);

  PartModel convertAndSavePart(PartModelDto partModelDto);

  void addPartModelToVehicleModel(VehicleModelPartModelDto vehicleModelPartModelDto) throws Throwable;

  void convertAndSaveMotor(MotorModelDto partModelDto);

  List<PartModelDto> findAll(Optional<Boolean> isMfr, Optional<PartType> partType,Pageable pageable);
  PartModelDto findById(Long partModelId);
  OrganisationDto findPartManufacturer(Long partModelId);

  List<MotorModelDto> findAllMotor(Pageable pageable);

  VehicleModel convertAndSaveVehicleModel(VehicleModelDto vehicleModelDto);

  List<VehicleModelDto> find(Optional<Long> id, Pageable pageable);

  void covertAndSaveColor(ColorModelDto colorModelDto);

  void addPartModelAttributes(AddPartModelAttributesDto addPartModelAttributesDto) throws Throwable;

  List<PartModelAttributeDto> findPartModelAttributes(Long partModelId) throws Throwable;

  PartModelTreeDto getPartModelTreeView(Long id);

  void saveAndUpdateDriveModeMaxRange(DriveModelMaxRangeDto driveModelMaxRangeDto);

  Optional<PartModelAttribute> getPartModelAttribute(Vehicle vehicle, PartType partType, String attributeName);

  void addColorImages(VehicleModelColorImageDto vehicleModelPartModelRequestDto) throws Throwable;

  void addPartModelImageCoordinates(VehicleModelPartModelImageCoordinatesDto vehicleModelColorImageDto) throws Throwable;
}
