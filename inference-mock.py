from flask import Flask, request, jsonify
import logging
import uuid

app = Flask(__name__)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
@app.route('/tyre_pressure', methods=['POST'])
def tyre_pressure():
    logging.info(f"Received headers: {dict(request.headers)}")
    logging.info(f"Received request: {request.json}")
    return jsonify({"status": "success"})

from flask import request, jsonify

@app.route('/trip_details', methods=['POST'])
def trip_details():
    logging.info(f"Received headers: {dict(request.headers)}")
    data = request.json
    logging.info(f"Received request: {data}")

    # Extract tripId from the incoming JSON (handle if missing)
    requested_trip_id = data.get('tripId', 0)  # fallback if missing

    # Build your response dynamically using the requested_trip_id
    response = {
        "tripId": requested_trip_id,
        "accelerations":[{"range":"0-20","startTime":"2025-06-24 05:34:29+00:00","stopTime":"2025-06-24 05:34:42+00:00","duration":13.0,"distance":30.0}],"decelerations":[{"range":"20-0","startTime":"2025-06-24 05:25:25+00:00","stopTime":"2025-06-24 05:25:31+00:00","duration":6.0,"distance":17.22}],"stopIntervals":[],"energyConsumptionIntervals":[{"startTime":"2025-06-24 05:06:13+00:00","stopTime":"2025-06-24 05:54:49+00:00","totalEnergy":299.5,"energyPerKm":26.67,"duration":0.81,"distance":11.23}],"brakeStat":[],"ranges":{"maxAccelerationMps2":3.33,"minAccelerationMps2":0.06,"maxMotorSpeed":61.96,"avgMotorSpeed":15.27,"maxGpsSpeed":61.0,"avgGpsSpeed":13.87,"maxMotorCurrentA":52.4,"minMotorCurrentA":0.0,"maxBatteryCurrentA":65.52,"minBatteryCurrentA":-20.41,"maxMotorVoltage":80.4,"minMotorVoltage":72.1,"maxBatteryVoltage":79.58,"minBatteryVoltage":73.44,"maxAltitude":976.0,"minAltitude":878.0,"maxLeanAngle":0,"avgLeanAngle":0},"turnStat":[{"time":1750741605000,"turnType":"Left","leanAngle":111.0},{"time":1750741614000,"turnType":"Left","leanAngle":21.0},{"time":1750742044000,"turnType":"Left","leanAngle":8.0},{"time":1750742628000,"turnType":"Left","leanAngle":20.0},{"time":1750742894000,"turnType":"Left","leanAngle":58.0},{"time":1750742900000,"turnType":"Left","leanAngle":91.0},{"time":1750742975000,"turnType":"Left","leanAngle":79.0},{"time":1750743181000,"turnType":"Left","leanAngle":10.0},{"time":1750743197000,"turnType":"Left","leanAngle":35.0},{"time":1750743268000,"turnType":"Left","leanAngle":30.0},{"time":1750743637000,"turnType":"Left","leanAngle":22.0},{"time":1750743645000,"turnType":"Left","leanAngle":30.0},{"time":1750743809000,"turnType":"Left","leanAngle":79.0},{"time":1750743824000,"turnType":"Left","leanAngle":34.0},{"time":1750743834000,"turnType":"Left","leanAngle":16.0},{"time":1750743894000,"turnType":"Left","leanAngle":38.0},{"time":1750743912000,"turnType":"Left","leanAngle":36.0},{"time":1750743926000,"turnType":"Left","leanAngle":31.0},{"time":1750743953000,"turnType":"Left","leanAngle":22.0},{"time":1750743961000,"turnType":"Left","leanAngle":57.0},{"time":1750744072000,"turnType":"Left","leanAngle":25.0},{"time":1750744110000,"turnType":"Left","leanAngle":21.0},{"time":1750744210000,"turnType":"Left","leanAngle":26.0},{"time":1750744378000,"turnType":"Left","leanAngle":46.0},{"time":1750741628000,"turnType":"Right","leanAngle":19.0},{"time":1750742700000,"turnType":"Right","leanAngle":6.0},{"time":1750742839000,"turnType":"Right","leanAngle":52.0},{"time":1750742937000,"turnType":"Right","leanAngle":69.0},{"time":1750742968000,"turnType":"Right","leanAngle":30.0},{"time":1750743006000,"turnType":"Right","leanAngle":22.0},{"time":1750743025000,"turnType":"Right","leanAngle":23.0},{"time":1750743552000,"turnType":"Right","leanAngle":27.0},{"time":1750743594000,"turnType":"Right","leanAngle":22.0},{"time":1750743676000,"turnType":"Right","leanAngle":39.0},{"time":1750743690000,"turnType":"Right","leanAngle":39.0},{"time":1750743741000,"turnType":"Right","leanAngle":24.0},{"time":1750743814000,"turnType":"Right","leanAngle":96.0},{"time":1750743851000,"turnType":"Right","leanAngle":58.0},{"time":1750743866000,"turnType":"Right","leanAngle":71.0},{"time":1750743986000,"turnType":"Right","leanAngle":19.0},{"time":1750744188000,"turnType":"Right","leanAngle":20.0},{"time":1750744200000,"turnType":"Right","leanAngle":15.0},{"time":1750744463000,"turnType":"Right","leanAngle":33.0},{"time":1750744475000,"turnType":"Right","leanAngle":47.0}],"rideSummary":{"totalGpsDistance":11.23,"totalMotorDistance":12.36,"brakesPerKm":0.0,"meanBrakeDuration":0,"totalBrakeDuration":0,"averageBrakeDistance":0,"brakeDistance":0,"maxSpeed":61.0,"meanSpeed":13.87,"statisticalSpeedMode":0.0,"modeWiseDistance":{"CITY":5.33,"ECO":1.92,"POWER":3.98},"modeWiseMeanSpeed":{"CITY":10.43,"ECO":14.63,"POWER":24.1},"startVoltage":79.1,"endVoltage":78.9,"moreThanOneHourStop":0,"stopDuration":602.0,"rideDuration":2916.0,"runningDuration":2314.0,"dischargePerCharge":0.86,"startSoc":62.0,"endSoc":49.0,"load":"HIGH","totaldischarge":13.0,"leftTurns":24,"rightTurns":20}
        }
    return jsonify(response)


@app.route('/v2/models/range-prediction/infer', methods=['POST'])
def infer():
    # Log the incoming request headers
    logging.info(f"Received headers: {dict(request.headers)}")

    # Log the incoming request data
    logging.info(f"Received request: {request.json}")

    # Create the response as per the new JSON structure
    response = {
        "model_name": "range-prediction",
        "model_version": None,
        "id": str(uuid.uuid4()),  # Generate a unique ID for each request
        "parameters": None,
        "outputs": [
            {
                "name": "output-0",
                "shape": [3],
                "datatype": "FP32",
                "parameters": None,
                "data": [
                    0.1807428002357483,
                    0.1732882559299469,
                    0.17470064759254456
                ]
            }
        ]
    }
    # Return the JSON response
    return jsonify(response)

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=8081)
