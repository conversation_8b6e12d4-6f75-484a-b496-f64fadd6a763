on:
  push:
    branches:
      - eks-dev-deployment-2-2
      - eks-dev-deployment-3

  workflow_dispatch:

name: Deploy to eks-dev

jobs:
  build:
    name: Build and Push Image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.client_payload.branch }}

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: 'gradle'

      - name: Build with <PERSON>rad<PERSON>
        uses: gradle/gradle-build-action@67421db6bd0bf253fb4bd25b31ebb98943c375e1
        env:
          GITHUB_TOKEN: ${{ secrets.TOKEN }}
          GITHUB_USERNAME: ${{ secrets.TOKEN }}
        with:
          arguments: -x test build

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.DEV_EKS_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEV_EKS_AWS_SECRET_KEY }}
          aws-region: ${{ secrets.DEV_EKS_AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: eks-dev-ev-datalayer-repo
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f .docker/Dockerfile .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

  deploy:
    name: Deploy to EKS
    runs-on: ubuntu-latest
    needs: build
    environment: eks-dev
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.client_payload.branch }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.DEV_EKS_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEV_EKS_AWS_SECRET_KEY }}
          aws-region: ${{ secrets.DEV_EKS_AWS_REGION }}

      - name: Update the image tag in the deployment file
        run: |
          sed -i'' -e 's/latest/${{ github.sha }}/g' manifests/eks-dev/ev-server-deployment.yml

      - name: Update kube config
        run: aws eks update-kubeconfig --name ${{ secrets.DEV_EKS_CLUSTER_NAME }} --region ${{ secrets.DEV_EKS_AWS_REGION }}

      - name: Deploy to eks-dev
        run: |
          kubectl apply -f  manifests/eks-dev/ev-server-configmap.yml
          kubectl apply -f  manifests/eks-dev/ev-server-sealed-secrets.yml 
          kubectl apply -f  manifests/eks-dev/ev-server-service.yml 
          kubectl apply -f  manifests/eks-dev/ev-server-deployment.yml 
          kubectl apply -f  manifests/eks-dev/ev-server-ingress.yml
