on:
  push:
    branches:
      - dev-deployment
      - deploy-1943
      - deploy-1943-and-1553-redis
      - dev-deployment-2
      - dev-deployment-with-2062
      - deploy-2122-schema-separation
      - deploy-1830-chassis-no
      - deploy-2143-db-optimisation
      - 2245-dev-deploy
      - 1912-dev-deploy
      - 2314-dev-deploy-test
      - 2305-deploy
      - f_f_qa_issue_2
  workflow_dispatch:

name: Deploy to dev

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: dev

    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.client_payload.branch }}

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: 'gradle'

      - name: Build with <PERSON>rad<PERSON>
        uses: gradle/gradle-build-action@67421db6bd0bf253fb4bd25b31ebb98943c375e1
        env:
          GITHUB_TOKEN: ${{ secrets.TOKEN }}
          GITHUB_USERNAME: ${{ secrets.TOKEN }}
        with:
          arguments: -x test build

      - name: Configure A<PERSON> credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ev-vehicle-be-repo
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f .docker/Dockerfile .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Update the image tag
        run: |
          sed -i'' -e 's/latest/${{ github.sha }}/g' manifests/dev/ev-server-deployment.yml

      - name: Set up kubectl and kubecontext
        uses: azure/k8s-set-context@v1
        with:
          kubeconfig: ${{ secrets.KUBECONFIG }}

      - name: Deploy to dev
        run: |
          kubectl apply -f  manifests/dev/ev-server-configmap.yml --insecure-skip-tls-verify --validate=false
          kubectl apply -f  manifests/dev/ev-server-sealed-secrets.yml --insecure-skip-tls-verify
          kubectl apply -f  manifests/dev/ev-server-service.yml --insecure-skip-tls-verify
          kubectl apply -f  manifests/dev/ev-server-deployment.yml --insecure-skip-tls-verify