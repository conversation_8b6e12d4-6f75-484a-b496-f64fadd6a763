apiVersion: v1
kind: ConfigMap
metadata:
  name: ev-server-configmap
  namespace: dev
data:
  POSTGRES_DB: 'evahana'
  POSTGRES_HOST_URI: 'timescale-dev-temp-replica.dev.svc.cluster.local:5432'
  POSTGRES_REPLICA_HOST_URI: 'timescale-dev-temp-replica.dev.svc.cluster.local:5432'
  AWS_PROFILE: 'default'
  DDL_AUTO: 'update'
  GITHUB_USERNAME: 'ev-nichesolv'
  GOOGLE_OAUTH2_CLIENT_ID: '54947800811-9op619s69vgv78n31kqn6dgpdpn5n38i.apps.googleusercontent.com'
  GOOGLE_OAUTH2_REDIRECT_URI: 'http://localhost:3000/oauth2/redirect'
  JWT_ISSUER: 'nds'
  MAIL_USER: '<EMAIL>'
  INSTANCE_TYPE: 'dev'
  RABBIT_MQ_HOST: 'rabbitmqcluster.default.svc.cluster.local:5672'
  TRIP_DETAILS_INPUT_QUEUE: 'TRIP_DETAILS_INPUT_QUEUE'
  TRIP_DETAILS_OUTPUT_QUEUE: 'TRIP_DETAILS_OUTPUT_QUEUE'
  TELEMETRY_PERSISTENCE_QUEUE: 'TELEMETRY_PERSISTENCE_QUEUE'
  LOCATION_PERSISTENCE_QUEUE: 'LOCATION_PERSISTENCE_QUEUE'
  BATTERY_CELL_PERSISTENCE_QUEUE: 'BATTERY_CELL_PERSISTENCE_QUEUE'
  BATTERY_STACK_PERSISTENCE_QUEUE: 'BATTERY_STACK_PERSISTENCE_QUEUE'
  BATTERY_STATUS_PERSISTENCE_QUEUE: 'BATTERY_STATUS_PERSISTENCE_QUEUE'
  TEST_RIDE_ANALYTICS_ENDPOINT: 'http://ev-servables-service.dev.svc.cluster.local:8000/v2/trip_details'
  REVERSE_GEOCODING_API: 'http://ev-location-service.dev.svc.cluster.local:3500/reversegeocoding'
  CSV_REQUEST_QUEUE: 'CSV_REQUEST_QUEUE'
  ADMIN_PASSWORD_RESET_MAIL_ID: '<EMAIL>'
  APPLE_TEST_PHONE_NUMBER: '+************'
  APPLE_TEST_OTP: '957595'
  RANGE_PREDICTION_HOST: 'http://range-prediction-predictor.dev.svc.cluster.local'
  RANGE_PREDICTION_HOST_HEADER: 'range-prediction-dev.example.com'
  REDIS_TTL: '0'
  REDIS_LOG_LEVEL: 'DEBUG'
  S3_CSV_REPORTS_BUCKET_NAME: 'ev-csv-reports-shared'
  ADMINISTRATOR_ORG: 'NicheSolv'
  GRAFANA_BASE_URL: 'https://ev-be-dev.nichesolv.com/grafana'
  GRAFANA_POSTGRES_DB: 'evahana'
  GRAFANA_DATASOURCE_UID: 'evahana_uid'
  GRAFANA_DATASOURCE_NAME: 'EVahana'
  GRAFANA_DATASOURCE_DB_TYPE_LOGO: 'public/app/plugins/datasource/postgres/img/postgresql_logo.svg'
  GRAFANA_DATASOURCE_DATABASE: 'evahana'
  GRAFANA_DATASOURCE_DB_TYPE: 'postgres'
  GRAFANA_DATASOURCE_DB_TYPE_NAME: 'PostgreSQL'
  GRAFANA_JAVA_ADMIN_USERNAME: 'javaadmin'
  WEBAPP_JWT_EXPIRY: '2592000000'
  MOBAPP_JWT_EXPIRY: '5184000000'
  LOG_LEVEL: 'INFO'

